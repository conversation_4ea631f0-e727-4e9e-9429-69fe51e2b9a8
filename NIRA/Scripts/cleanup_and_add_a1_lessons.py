#!/usr/bin/env python3
"""
Cleanup A1 Lessons and Add New Ones
1. Remove "Days, Months and Time" (keep "Days, Weeks, and Months" + add time)
2. Remove "Travel and Transportation Advanced" 
3. Add "Music and Movies"
4. Add "Famous Landmarks"
5. Add "Sports"
Result: 30 unique A1 lessons
"""

import requests
import json
import uuid
from datetime import datetime

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Learning Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

def get_current_lessons():
    """Get all current A1 Tamil lessons"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/lessons?path_id=eq.{TAMIL_A1_PATH_ID}&select=id,title,sequence_order"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            lessons = response.json()
            print(f"📚 Found {len(lessons)} current A1 lessons:")
            for lesson in sorted(lessons, key=lambda x: x.get('sequence_order', 0)):
                print(f"  {lesson.get('sequence_order', '?')}: {lesson['title']}")
            return lessons
        else:
            print(f"❌ Failed to fetch lessons: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error fetching lessons: {e}")
        return []

def delete_lesson(lesson_id, title):
    """Delete a lesson from database"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.delete(url, headers=headers)
        if response.status_code == 204:
            print(f"✅ Deleted: {title}")
            return True
        else:
            print(f"❌ Failed to delete {title}: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error deleting {title}: {e}")
        return False

def update_days_weeks_months_lesson():
    """Update Days, Weeks, and Months lesson to include time"""
    try:
        # First find the lesson
        url = f"{SUPABASE_URL}/rest/v1/lessons?path_id=eq.{TAMIL_A1_PATH_ID}&title=eq.Days, Weeks, and Months&select=id,content_metadata"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers)
        if response.status_code != 200 or not response.json():
            print("❌ Days, Weeks, and Months lesson not found")
            return False
            
        lesson = response.json()[0]
        lesson_id = lesson['id']
        content_metadata = lesson['content_metadata']
        
        # Add time vocabulary to existing vocabulary
        time_vocabulary = [
            {"word": "நேரம்", "translation": "Time", "example": "நேரம் என்ன?"},
            {"word": "மணி", "translation": "Hour/O'clock", "example": "இரண்டு மணி"},
            {"word": "நிமிடம்", "translation": "Minute", "example": "பத்து நிமிடம்"},
            {"word": "வினாடி", "translation": "Second", "example": "ஒரு வினாடி"},
            {"word": "காலை", "translation": "Morning", "example": "காலை வணக்கம்"},
            {"word": "மதியம்", "translation": "Afternoon", "example": "மதியம் சாப்பிடுகிறேன்"},
            {"word": "மாலை", "translation": "Evening", "example": "மாலை நேரம்"},
            {"word": "இரவு", "translation": "Night", "example": "இரவு தூங்குகிறேன்"},
            {"word": "கடிகாரம்", "translation": "Clock/Watch", "example": "கடிகாரம் பார்க்கிறேன்"}
        ]
        
        # Update vocabulary (keep existing + add time words)
        existing_vocab = content_metadata.get('vocabulary', [])
        # Remove last few items to make room for time vocabulary
        updated_vocab = existing_vocab[:16] + time_vocabulary
        content_metadata['vocabulary'] = updated_vocab
        
        # Update title and description
        update_url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}"
        update_data = {
            "title": "Days, Weeks, Months, and Time",
            "description": "Master time expressions, calendar vocabulary, days, weeks, months, and telling time",
            "content_metadata": content_metadata
        }
        
        response = requests.patch(update_url, json=update_data, headers=headers)
        if response.status_code == 204:
            print("✅ Updated: Days, Weeks, Months, and Time (added time vocabulary)")
            return True
        else:
            print(f"❌ Failed to update lesson: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error updating lesson: {e}")
        return False

# New lessons to add
NEW_LESSONS = [
    {
        "title": "Music and Movies",
        "description": "Learn about music, movies, entertainment, and cultural expressions in Tamil",
        "topic": "Entertainment",
        "sequence_order": 30,
        "vocabulary": [
            {"word": "இசை", "translation": "Music", "example": "இசை கேட்கிறேன்"},
            {"word": "பாடல்", "translation": "Song", "example": "பாடல் பாடுகிறேன்"},
            {"word": "திரைப்படம்", "translation": "Movie", "example": "திரைப்படம் பார்க்கிறேன்"},
            {"word": "நடிகர்", "translation": "Actor", "example": "நடிகர் நல்லவர்"},
            {"word": "நடிகை", "translation": "Actress", "example": "நடிகை அழகானவள்"},
            {"word": "இயக்குநர்", "translation": "Director", "example": "இயக்குநர் திறமையானவர்"},
            {"word": "தமிழ் சினிமா", "translation": "Tamil cinema", "example": "தமிழ் சினிமா பிரபலம்"},
            {"word": "கர்நாடக இசை", "translation": "Carnatic music", "example": "கர்நாடக இசை பாரம்பரியம்"},
            {"word": "வீணை", "translation": "Veena", "example": "வீணை வாசிக்கிறேன்"},
            {"word": "மிருதங்கம்", "translation": "Mridangam", "example": "மிருதங்கம் அடிக்கிறேன்"},
            {"word": "நாதஸ்வரம்", "translation": "Nadaswaram", "example": "நாதஸ்வரம் இனிமையானது"},
            {"word": "தபலா", "translation": "Tabla", "example": "தபலா கற்றுக்கொள்கிறேன்"},
            {"word": "கிட்டார்", "translation": "Guitar", "example": "கிட்டார் வாசிக்கிறேன்"},
            {"word": "பியானோ", "translation": "Piano", "example": "பியானோ கற்றுக்கொள்கிறேன்"},
            {"word": "நடனம்", "translation": "Dance", "example": "நடனம் ஆடுகிறேன்"},
            {"word": "பரதநாட்டியம்", "translation": "Bharatanatyam", "example": "பரதநாட்டியம் கற்றுக்கொள்கிறேன்"},
            {"word": "கச்சேரி", "translation": "Concert", "example": "கச்சேரி கேட்கிறேன்"},
            {"word": "தியேட்டர்", "translation": "Theater", "example": "தியேட்டரில் படம் பார்க்கிறேன்"},
            {"word": "டிக்கெட்", "translation": "Ticket", "example": "டிக்கெட் வாங்குகிறேன்"},
            {"word": "பொழுதுபோக்கு", "translation": "Entertainment", "example": "பொழுதுபோக்கு வேண்டும்"},
            {"word": "கலை", "translation": "Art", "example": "கலை அழகானது"},
            {"word": "கலைஞர்", "translation": "Artist", "example": "கலைஞர் திறமையானவர்"},
            {"word": "ரசிகர்", "translation": "Fan", "example": "நான் ரசிகர்"},
            {"word": "விழா", "translation": "Festival", "example": "இசை விழா நடக்கிறது"},
            {"word": "பாராட்டு", "translation": "Appreciation", "example": "பாராட்டு கிடைத்தது"}
        ]
    },
    {
        "title": "Famous Landmarks",
        "description": "Learn about famous Tamil Nadu landmarks, temples, and cultural sites",
        "topic": "Landmarks",
        "sequence_order": 31,
        "vocabulary": [
            {"word": "சென்னை", "translation": "Chennai", "example": "சென்னை பெரிய நகரம்"},
            {"word": "மீனாக்ஷி கோயில்", "translation": "Meenakshi Temple", "example": "மீனாக்ஷி கோயில் மதுரையில்"},
            {"word": "பிரகதீஸ்வரர் கோயில்", "translation": "Brihadeeswarar Temple", "example": "பிரகதீஸ்வரர் கோயில் தஞ்சாவூரில்"},
            {"word": "மரீனா கடற்கரை", "translation": "Marina Beach", "example": "மரீனா கடற்கரை நீளமானது"},
            {"word": "கோடைக்கானல்", "translation": "Kodaikanal", "example": "கோடைக்கானல் குளிர்ச்சியானது"},
            {"word": "ஊட்டி", "translation": "Ooty", "example": "ஊட்டி மலை நிலையம்"},
            {"word": "கன்யாகுமரி", "translation": "Kanyakumari", "example": "கன்யாகுமரி தென் முனை"},
            {"word": "ராமேஸ்வரம்", "translation": "Rameswaram", "example": "ராமேஸ்வரம் புனித தலம்"},
            {"word": "மாமல்லபுரம்", "translation": "Mamallapuram", "example": "மாமல்லபுரம் சிற்பங்கள்"},
            {"word": "திருச்சி", "translation": "Trichy", "example": "திருச்சி ரக் கோட்டை"},
            {"word": "மதுரை", "translation": "Madurai", "example": "மதுரை பழைய நகரம்"},
            {"word": "தஞ்சாவூர்", "translation": "Thanjavur", "example": "தஞ்சாவூர் கலை மையம்"},
            {"word": "திருவண்ணாமலை", "translation": "Tiruvannamalai", "example": "திருவண்ணாமலை மலை"},
            {"word": "சிதம்பரம்", "translation": "Chidambaram", "example": "சிதம்பரம் நடராஜர் கோயில்"},
            {"word": "திருப்பதி", "translation": "Tirupati", "example": "திருப்பதி வெங்கடேஸ்வரர்"},
            {"word": "கோயில்", "translation": "Temple", "example": "கோயில் பழமையானது"},
            {"word": "கோபுரம்", "translation": "Temple tower", "example": "கோபுரம் உயரமானது"},
            {"word": "சிலை", "translation": "Statue", "example": "சிலை அழகானது"},
            {"word": "கல்வெட்டு", "translation": "Inscription", "example": "கல்வெட்டு பழமையானது"},
            {"word": "அரண்மனை", "translation": "Palace", "example": "அரண்மனை பெரியது"},
            {"word": "கோட்டை", "translation": "Fort", "example": "கோட்டை வலுவானது"},
            {"word": "அருங்காட்சியகம்", "translation": "Museum", "example": "அருங்காட்சியகம் சுவாரஸ்யம்"},
            {"word": "பாரம்பரியம்", "translation": "Heritage", "example": "பாரம்பரியம் பாதுகாக்க வேண்டும்"},
            {"word": "சுற்றுலா", "translation": "Tourism", "example": "சுற்றுலா வளர்ச்சி"},
            {"word": "வரலாறு", "translation": "History", "example": "வரலாறு முக்கியம்"}
        ]
    },
    {
        "title": "Sports and Games",
        "description": "Learn about sports, games, and physical activities in Tamil culture",
        "topic": "Sports",
        "sequence_order": 32,
        "vocabulary": [
            {"word": "விளையாட்டு", "translation": "Sports/Games", "example": "விளையாட்டு நல்லது"},
            {"word": "கிரிக்கெட்", "translation": "Cricket", "example": "கிரிக்கெட் விளையாடுகிறேன்"},
            {"word": "கால்பந்து", "translation": "Football", "example": "கால்பந்து பிரபலம்"},
            {"word": "கபடி", "translation": "Kabaddi", "example": "கபடி தமிழ் விளையாட்டு"},
            {"word": "சிலம்பம்", "translation": "Silambam", "example": "சிலம்பம் கற்றுக்கொள்கிறேன்"},
            {"word": "கலரிப்பயட்டு", "translation": "Kalaripayattu", "example": "கலரிப்பயட்டு தற்காப்பு"},
            {"word": "ஓட்டம்", "translation": "Running", "example": "காலையில் ஓட்டம்"},
            {"word": "நீச்சல்", "translation": "Swimming", "example": "நீச்சல் கற்றுக்கொள்கிறேன்"},
            {"word": "சைக்கிள்", "translation": "Cycling", "example": "சைக்கிள் ஓட்டுகிறேன்"},
            {"word": "டென்னிஸ்", "translation": "Tennis", "example": "டென்னிஸ் விளையாடுகிறேன்"},
            {"word": "பேட்மிண்டன்", "translation": "Badminton", "example": "பேட்மிண்டன் எளிதானது"},
            {"word": "வாலிபால்", "translation": "Volleyball", "example": "வாலிபால் குழு விளையாட்டு"},
            {"word": "கூடைப்பந்து", "translation": "Basketball", "example": "கூடைப்பந்து உயரம் வேண்டும்"},
            {"word": "ஹாக்கி", "translation": "Hockey", "example": "ஹாக்கி வேகமானது"},
            {"word": "சதுரங்கம்", "translation": "Chess", "example": "சதுரங்கம் புத்திசாலித்தனம்"},
            {"word": "கேரம்", "translation": "Carrom", "example": "கேரம் வீட்டு விளையாட்டு"},
            {"word": "பல்லாங்குழி", "translation": "Pallanguzhi", "example": "பல்லாங்குழி பாரம்பரியம்"},
            {"word": "கோலி", "translation": "Goli (marbles)", "example": "கோலி விளையாடுகிறேன்"},
            {"word": "பயிற்சி", "translation": "Practice", "example": "தினமும் பயிற்சி"},
            {"word": "போட்டி", "translation": "Competition", "example": "போட்டியில் பங்கேற்கிறேன்"},
            {"word": "வெற்றி", "translation": "Victory", "example": "வெற்றி கிடைத்தது"},
            {"word": "தோல்வி", "translation": "Defeat", "example": "தோல்வியில் கற்றுக்கொள்கிறேன்"},
            {"word": "குழு", "translation": "Team", "example": "குழு வேலை முக்கியம்"},
            {"word": "விளையாட்டு மைதானம்", "translation": "Playground", "example": "விளையாட்டு மைதானம் பெரியது"},
            {"word": "ஆரோக்கியம்", "translation": "Health", "example": "விளையாட்டு ஆரோக்கியம் தரும்"}
        ]
    }
]

def create_lesson_content(lesson_config):
    """Create comprehensive lesson content"""
    
    # Generate conversations
    conversations = [
        {
            "title": f"Talking about {lesson_config['topic']}",
            "scenario": f"A conversation about {lesson_config['topic'].lower()}",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": f"{lesson_config['vocabulary'][0]['word']} பிடிக்கிறதா?",
                    "speaker": "Person A",
                    "translation": f"Do you like {lesson_config['vocabulary'][0]['translation'].lower()}?",
                    "pronunciation": f"{lesson_config['vocabulary'][0]['word']} pidikkiradha?",
                    "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_conv_01_01.mp3"
                },
                {
                    "text": f"ஆம், {lesson_config['vocabulary'][0]['word']} மிகவும் பிடிக்கும்",
                    "speaker": "Person B", 
                    "translation": f"Yes, I like {lesson_config['vocabulary'][0]['translation'].lower()} very much",
                    "pronunciation": f"aam, {lesson_config['vocabulary'][0]['word']} migavum pidikkum",
                    "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_conv_01_02.mp3"
                }
            ]
        }
    ]
    
    # Generate grammar points
    grammar_points = [
        {
            "rule": f"{lesson_config['topic']} Expressions",
            "explanation": f"Learn how to express preferences and talk about {lesson_config['topic'].lower()}",
            "examples": [
                f"{lesson_config['vocabulary'][0]['example']}",
                f"{lesson_config['vocabulary'][1]['example']}",
                f"{lesson_config['vocabulary'][2]['example']}"
            ],
            "examples_audio_urls": [
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_01.mp3",
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_02.mp3",
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_03.mp3"
            ],
            "tips": f"Practice expressing your interests in {lesson_config['topic'].lower()}"
        }
    ]
    
    # Generate exercises
    exercises = [
        {
            "type": "multiple_choice",
            "question": f"What is the Tamil word for '{lesson_config['vocabulary'][0]['translation']}'?",
            "options": [
                lesson_config['vocabulary'][0]['word'],
                lesson_config['vocabulary'][1]['word'], 
                lesson_config['vocabulary'][2]['word'],
                lesson_config['vocabulary'][3]['word']
            ],
            "correctAnswer": 0,
            "explanation": f"{lesson_config['vocabulary'][0]['word']} means {lesson_config['vocabulary'][0]['translation']}",
            "points": 10
        }
    ]
    
    return {
        "path_id": TAMIL_A1_PATH_ID,
        "title": lesson_config["title"],
        "description": lesson_config["description"],
        "lesson_type": "vocabulary",
        "difficulty_level": 1,
        "estimated_duration": 25,
        "sequence_order": lesson_config["sequence_order"],
        "learning_objectives": [
            f"Learn {lesson_config['topic'].lower()} vocabulary",
            "Practice pronunciation with audio",
            "Express preferences and interests"
        ],
        "vocabulary_focus": [],
        "grammar_concepts": ["Basic sentence structure", f"{lesson_config['topic']} expressions"],
        "cultural_notes": f"Tamil {lesson_config['topic'].lower()} culture and traditions",
        "prerequisite_lessons": [],
        "content_metadata": {
            "vocabulary": lesson_config["vocabulary"],
            "conversations": conversations,
            "grammar_points": grammar_points,
            "exercises": exercises
        },
        "is_active": True,
        "has_audio": True,
        "audio_metadata": {
            "total_files": len(lesson_config["vocabulary"]) * 2,
            "voice_id": "9BWtsMINqrJLrRacOk9x",
            "generated_at": datetime.now().isoformat()
        }
    }

def add_lesson_to_database(lesson_data):
    """Add lesson to Supabase database"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Create lesson record
        lesson_record = {
            "id": str(uuid.uuid4()),
            "path_id": lesson_data["path_id"],
            "title": lesson_data["title"],
            "description": lesson_data["description"],
            "lesson_type": lesson_data["lesson_type"],
            "difficulty_level": lesson_data["difficulty_level"],
            "estimated_duration": lesson_data["estimated_duration"],
            "sequence_order": lesson_data["sequence_order"],
            "learning_objectives": lesson_data["learning_objectives"],
            "vocabulary_focus": lesson_data["vocabulary_focus"],
            "grammar_concepts": lesson_data["grammar_concepts"],
            "cultural_notes": lesson_data["cultural_notes"],
            "prerequisite_lessons": lesson_data["prerequisite_lessons"],
            "content_metadata": lesson_data["content_metadata"],
            "is_active": lesson_data["is_active"],
            "has_audio": lesson_data["has_audio"],
            "audio_metadata": lesson_data["audio_metadata"]
        }
        
        response = requests.post(url, json=lesson_record, headers=headers)
        
        if response.status_code == 201:
            print(f"✅ Successfully added lesson: {lesson_data['title']}")
            return True
        else:
            print(f"❌ Failed to add lesson: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error adding lesson: {e}")
        return False

def main():
    """Cleanup and reorganize A1 Tamil lessons"""
    print("🧹 Cleaning up A1 Tamil Lessons")
    print("=" * 50)
    
    # Step 1: Get current lessons
    current_lessons = get_current_lessons()
    
    # Step 2: Delete duplicate lessons
    print(f"\n🗑️ Removing duplicate lessons...")
    lessons_to_delete = [
        "Days, Months and Time",
        "Travel and Transportation Advanced"
    ]
    
    deleted_count = 0
    for lesson in current_lessons:
        if lesson['title'] in lessons_to_delete:
            if delete_lesson(lesson['id'], lesson['title']):
                deleted_count += 1
    
    print(f"✅ Deleted {deleted_count} duplicate lessons")
    
    # Step 3: Update Days, Weeks, and Months to include time
    print(f"\n🔄 Updating Days, Weeks, and Months lesson...")
    update_days_weeks_months_lesson()
    
    # Step 4: Add new lessons
    print(f"\n➕ Adding new lessons...")
    success_count = 0
    
    for lesson_config in NEW_LESSONS:
        print(f"\n📚 Creating lesson: {lesson_config['title']}")
        
        # Create lesson content
        lesson_data = create_lesson_content(lesson_config)
        
        # Add to database
        if add_lesson_to_database(lesson_data):
            success_count += 1
        
        print(f"⏳ Waiting 2 seconds...")
        import time
        time.sleep(2)
    
    print(f"\n🎉 Cleanup completed!")
    print(f"✅ Deleted: {deleted_count} duplicate lessons")
    print(f"✅ Updated: 1 lesson (added time vocabulary)")
    print(f"✅ Added: {success_count}/{len(NEW_LESSONS)} new lessons")
    print(f"📱 A1 Tamil now has 30 unique lessons!")

if __name__ == "__main__":
    main()
