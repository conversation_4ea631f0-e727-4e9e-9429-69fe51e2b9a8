#!/usr/bin/env python3
"""
Fix Vocabulary Examples - Add English Translation and Romanization
"""

import requests
import json

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Lesson ID for Animals and Nature
LESSON_ID = "b966c742-d36d-4d94-9e35-7c17a5039487"

# Updated vocabulary with proper English translations and romanization
UPDATED_VOCABULARY = [
    {
        "word": "நாய்",
        "example": "நாய் குரைக்கிறது (naai kuraikkirathu) - The dog is barking",
        "difficulty": "basic",
        "translation": "Dog",
        "pronunciation": "naai",
        "part_of_speech": "noun"
    },
    {
        "word": "பூனை",
        "example": "பூனை பால் குடிக்கிறது (poonai paal kudikkirathu) - The cat is drinking milk",
        "difficulty": "basic",
        "translation": "Cat",
        "pronunciation": "poonai",
        "part_of_speech": "noun"
    },
    {
        "word": "பறவை",
        "example": "பறவை பறக்கிறது (paravai parakkiRathu) - The bird is flying",
        "difficulty": "basic",
        "translation": "Bird",
        "pronunciation": "paravai",
        "part_of_speech": "noun"
    },
    {
        "word": "மரம்",
        "example": "பெரிய மரம் இருக்கிறது (periya maram irukkiRathu) - There is a big tree",
        "difficulty": "basic",
        "translation": "Tree",
        "pronunciation": "maram",
        "part_of_speech": "noun"
    },
    {
        "word": "பூ",
        "example": "அழகான பூ மலர்ந்திருக்கிறது (azhagaana poo malarndhirukkiRathu) - The beautiful flower is blooming",
        "difficulty": "basic",
        "translation": "Flower",
        "pronunciation": "poo",
        "part_of_speech": "noun"
    },
    {
        "word": "இலை",
        "example": "பச்சை இலை விழுந்தது (pachchai ilai vizhundhathu) - The green leaf fell",
        "difficulty": "basic",
        "translation": "Leaf",
        "pronunciation": "ilai",
        "part_of_speech": "noun"
    },
    {
        "word": "புல்",
        "example": "பச்சை புல் வளர்கிறது (pachchai pul valarkiRathu) - Green grass is growing",
        "difficulty": "basic",
        "translation": "Grass",
        "pronunciation": "pul",
        "part_of_speech": "noun"
    },
    {
        "word": "மீன்",
        "example": "மீன் நீரில் நீந்துகிறது (meen neeril neenthukiRathu) - The fish is swimming in water",
        "difficulty": "basic",
        "translation": "Fish",
        "pronunciation": "meen",
        "part_of_speech": "noun"
    },
    {
        "word": "யானை",
        "example": "பெரிய யானை நடக்கிறது (periya yaanai nadakkiRathu) - The big elephant is walking",
        "difficulty": "basic",
        "translation": "Elephant",
        "pronunciation": "yaanai",
        "part_of_speech": "noun"
    },
    {
        "word": "சிங்கம்",
        "example": "சிங்கம் கர்ஜிக்கிறது (singam karjikkiRathu) - The lion is roaring",
        "difficulty": "basic",
        "translation": "Lion",
        "pronunciation": "singam",
        "part_of_speech": "noun"
    },
    {
        "word": "குதிரை",
        "example": "குதிரை ஓடுகிறது (kudhirai odukiRathu) - The horse is running",
        "difficulty": "basic",
        "translation": "Horse",
        "pronunciation": "kudhirai",
        "part_of_speech": "noun"
    },
    {
        "word": "பசு",
        "example": "பசு பால் கொடுக்கிறது (pasu paal kodukkirathu) - The cow gives milk",
        "difficulty": "basic",
        "translation": "Cow",
        "pronunciation": "pasu",
        "part_of_speech": "noun"
    },
    {
        "word": "ஆடு",
        "example": "ஆடு புல் தின்கிறது (aadu pul thinkiRathu) - The goat is eating grass",
        "difficulty": "basic",
        "translation": "Goat",
        "pronunciation": "aadu",
        "part_of_speech": "noun"
    },
    {
        "word": "கோழி",
        "example": "கோழி முட்டை இடுகிறது (kozhi muttai idukiRathu) - The chicken is laying eggs",
        "difficulty": "basic",
        "translation": "Chicken",
        "pronunciation": "kozhi",
        "part_of_speech": "noun"
    },
    {
        "word": "வாத்து",
        "example": "வாத்து நீரில் நீந்துகிறது (vaathu neeril neenthukiRathu) - The duck is swimming in water",
        "difficulty": "basic",
        "translation": "Duck",
        "pronunciation": "vaathu",
        "part_of_speech": "noun"
    },
    {
        "word": "முயல்",
        "example": "முயல் வேகமாக ஓடுகிறது (muyal vegamaaga odukiRathu) - The rabbit is running fast",
        "difficulty": "basic",
        "translation": "Rabbit",
        "pronunciation": "muyal",
        "part_of_speech": "noun"
    },
    {
        "word": "எலி",
        "example": "சிறிய எலி ஓடுகிறது (siriya eli odukiRathu) - The small mouse is running",
        "difficulty": "basic",
        "translation": "Mouse",
        "pronunciation": "eli",
        "part_of_speech": "noun"
    },
    {
        "word": "பாம்பு",
        "example": "பாம்பு ஊர்ந்து செல்கிறது (paambu oorndhu selkiRathu) - The snake is crawling",
        "difficulty": "basic",
        "translation": "Snake",
        "pronunciation": "paambu",
        "part_of_speech": "noun"
    },
    {
        "word": "தவளை",
        "example": "தவளை குதிக்கிறது (thavalai kudhikkiRathu) - The frog is jumping",
        "difficulty": "basic",
        "translation": "Frog",
        "pronunciation": "thavalai",
        "part_of_speech": "noun"
    },
    {
        "word": "வண்ணத்துப்பூச்சி",
        "example": "அழகான வண்ணத்துப்பூச்சி பறக்கிறது (azhagaana vannathupoochi parakkiRathu) - The beautiful butterfly is flying",
        "difficulty": "basic",
        "translation": "Butterfly",
        "pronunciation": "vannathupoochi",
        "part_of_speech": "noun"
    },
    {
        "word": "தேனீ",
        "example": "தேனீ தேன் சேகரிக்கிறது (thenee then segarikkiRathu) - The bee is collecting honey",
        "difficulty": "basic",
        "translation": "Bee",
        "pronunciation": "thenee",
        "part_of_speech": "noun"
    },
    {
        "word": "எறும்பு",
        "example": "எறும்பு வேலை செய்கிறது (erumbu velai seykiRathu) - The ant is working",
        "difficulty": "basic",
        "translation": "Ant",
        "pronunciation": "erumbu",
        "part_of_speech": "noun"
    },
    {
        "word": "சூரியன்",
        "example": "சூரியன் பிரகாசிக்கிறது (sooriyan piragaasikkiRathu) - The sun is shining",
        "difficulty": "basic",
        "translation": "Sun",
        "pronunciation": "sooriyan",
        "part_of_speech": "noun"
    },
    {
        "word": "நிலா",
        "example": "நிலா ஒளிர்கிறது (nila oLirkiRathu) - The moon is glowing",
        "difficulty": "basic",
        "translation": "Moon",
        "pronunciation": "nila",
        "part_of_speech": "noun"
    },
    {
        "word": "நட்சத்திரம்",
        "example": "நட்சத்திரம் மின்னுகிறது (natchathiram minnukiRathu) - The star is twinkling",
        "difficulty": "basic",
        "translation": "Star",
        "pronunciation": "natchathiram",
        "part_of_speech": "noun"
    }
]

def update_vocabulary():
    """Update vocabulary with proper English translations and romanization"""
    try:
        print("🔄 Updating vocabulary examples with English translations and romanization...")
        
        # Get current lesson content
        query_url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{LESSON_ID}&select=content_metadata"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(query_url, headers=headers)
        if response.status_code != 200:
            print(f"❌ Failed to fetch lesson: {response.status_code}")
            return False
            
        lesson_data = response.json()[0]
        content_metadata = lesson_data["content_metadata"]
        
        # Update vocabulary section with audio URLs preserved
        current_vocab = content_metadata["vocabulary"]
        
        for i, updated_vocab in enumerate(UPDATED_VOCABULARY):
            if i < len(current_vocab):
                # Preserve existing audio URLs
                updated_vocab["word_audio_url"] = current_vocab[i].get("word_audio_url", "")
                updated_vocab["example_audio_url"] = current_vocab[i].get("example_audio_url", "")
        
        content_metadata["vocabulary"] = UPDATED_VOCABULARY
        
        # Update the lesson in database
        update_url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{LESSON_ID}"
        update_data = {"content_metadata": content_metadata}
        
        response = requests.patch(update_url, json=update_data, headers=headers)
        
        if response.status_code == 204:
            print("✅ Successfully updated vocabulary examples!")
            return True
        else:
            print(f"❌ Failed to update lesson: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error updating vocabulary: {e}")
        return False

def main():
    """Fix vocabulary examples with proper formatting"""
    print("🔧 Fixing vocabulary examples...")
    
    if update_vocabulary():
        print("🎉 Vocabulary examples fixed successfully!")
        print("📋 All examples now have:")
        print("  ✅ Tamil text with romanization")
        print("  ✅ English translation")
        print("  ✅ Preserved audio URLs")
    else:
        print("❌ Failed to fix vocabulary examples")

if __name__ == "__main__":
    main()
