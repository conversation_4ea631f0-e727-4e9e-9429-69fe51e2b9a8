#!/usr/bin/env python3
"""
Add New A1 Tamil Lessons
- Vegetables
- Days, Weeks, and Months  
- Local Transportation (split from Transportation)
- Travel (split from Transportation)
"""

import requests
import json
import uuid
from datetime import datetime

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Learning Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

# New lesson configurations
NEW_LESSONS = [
    {
        "title": "Vegetables and Healthy Eating",
        "description": "Learn vegetable names, healthy eating vocabulary, and food preparation terms",
        "topic": "Vegetables",
        "sequence_order": 26,
        "vocabulary": [
            {"word": "காய்கறிகள்", "translation": "Vegetables", "example": "காய்கறிகள் ஆரோக்கியமானவை"},
            {"word": "தக்காளி", "translation": "Tomato", "example": "தக்காளி சிவப்பு நிறம்"},
            {"word": "வெங்காயம்", "translation": "Onion", "example": "வெங்காயம் கண்ணீர் வரும்"},
            {"word": "கேரட்", "translation": "Carrot", "example": "கேரட் ஆரஞ்சு நிறம்"},
            {"word": "உருளைக்கிழங்கு", "translation": "Potato", "example": "உருளைக்கிழங்கு சுவையானது"},
            {"word": "பீன்ஸ்", "translation": "Beans", "example": "பீன்ஸ் பச்சை நிறம்"},
            {"word": "கத்தரிக்காய்", "translation": "Eggplant", "example": "கத்தரிக்காய் ஊதா நிறம்"},
            {"word": "முள்ளங்கி", "translation": "Radish", "example": "முள்ளங்கி வெள்ளை நிறம்"},
            {"word": "பூசணிக்காய்", "translation": "Pumpkin", "example": "பூசணிக்காய் பெரியது"},
            {"word": "வெண்டைக்காய்", "translation": "Okra", "example": "வெண்டைக்காய் பச்சை நிறம்"},
            {"word": "கீரை", "translation": "Spinach", "example": "கீரை ஆரோக்கியமானது"},
            {"word": "காலிஃப்ளவர்", "translation": "Cauliflower", "example": "காலிஃப்ளவர் வெள்ளை நிறம்"},
            {"word": "முட்டைகோஸ்", "translation": "Cabbage", "example": "முட்டைகோஸ் பச்சை நிறம்"},
            {"word": "பச்சை மிளகாய்", "translation": "Green chili", "example": "பச்சை மிளகாய் காரமானது"},
            {"word": "இஞ்சி", "translation": "Ginger", "example": "இஞ்சி மருத்துவ குணம்"},
            {"word": "பூண்டு", "translation": "Garlic", "example": "பூண்டு வாசனை அதிகம்"},
            {"word": "கொத்தமல்லி", "translation": "Coriander", "example": "கொத்தமல்லி சுவை சேர்க்கும்"},
            {"word": "புதினா", "translation": "Mint", "example": "புதினா குளிர்ச்சி தரும்"},
            {"word": "கறிவேப்பிலை", "translation": "Curry leaves", "example": "கறிவேப்பிலை வாசனை நல்லது"},
            {"word": "மஞ்சள்", "translation": "Turmeric", "example": "மஞ்சள் மருத்துவ குணம்"},
            {"word": "சமையல்", "translation": "Cooking", "example": "சமையல் செய்கிறேன்"},
            {"word": "ஆரோக்கியம்", "translation": "Health", "example": "ஆரோக்கியம் முக்கியம்"},
            {"word": "சத்து", "translation": "Nutrition", "example": "காய்கறிகளில் சத்து அதிகம்"},
            {"word": "சுவை", "translation": "Taste", "example": "காய்கறிகள் சுவையானவை"},
            {"word": "சந்தை", "translation": "Market", "example": "சந்தையில் காய்கறிகள் வாங்குகிறேன்"}
        ]
    },
    {
        "title": "Days, Weeks, and Months",
        "description": "Master time expressions, calendar vocabulary, and temporal relationships",
        "topic": "Time and Calendar",
        "sequence_order": 27,
        "vocabulary": [
            {"word": "நாள்", "translation": "Day", "example": "இன்று நல்ல நாள்"},
            {"word": "வாரம்", "translation": "Week", "example": "ஒரு வாரம் ஏழு நாட்கள்"},
            {"word": "மாதம்", "translation": "Month", "example": "ஒரு மாதம் முப்பது நாட்கள்"},
            {"word": "ஞாயிறு", "translation": "Sunday", "example": "ஞாயிறு விடுமுறை நாள்"},
            {"word": "திங்கள்", "translation": "Monday", "example": "திங்கள் வேலை ஆரம்பம்"},
            {"word": "செவ்வாய்", "translation": "Tuesday", "example": "செவ்வாய் பள்ளி செல்கிறேன்"},
            {"word": "புதன்", "translation": "Wednesday", "example": "புதன் நடுவார நாள்"},
            {"word": "வியாழன்", "translation": "Thursday", "example": "வியாழன் வேலை நாள்"},
            {"word": "வெள்ளி", "translation": "Friday", "example": "வெள்ளி கடைசி வேலை நாள்"},
            {"word": "சனி", "translation": "Saturday", "example": "சனி விடுமுறை நாள்"},
            {"word": "ஜனவரி", "translation": "January", "example": "ஜனவரி குளிர் மாதம்"},
            {"word": "பிப்ரவரி", "translation": "February", "example": "பிப்ரவரி குறுகிய மாதம்"},
            {"word": "மார்ச்", "translation": "March", "example": "மார்ச் வசந்த மாதம்"},
            {"word": "ஏப்ரல்", "translation": "April", "example": "ஏப்ரல் வெப்ப மாதம்"},
            {"word": "மே", "translation": "May", "example": "மே கோடை மாதம்"},
            {"word": "ஜூன்", "translation": "June", "example": "ஜூன் மழை மாதம்"},
            {"word": "ஜூலை", "translation": "July", "example": "ஜூலை மழைக்காலம்"},
            {"word": "ஆகஸ்ட்", "translation": "August", "example": "ஆகஸ்ட் மழை அதிகம்"},
            {"word": "செப்டம்பர்", "translation": "September", "example": "செப்டம்பர் பண்டிகை மாதம்"},
            {"word": "அக்டோபர்", "translation": "October", "example": "அக்டோபர் குளிர் ஆரம்பம்"},
            {"word": "நவம்பர்", "translation": "November", "example": "நவம்பர் குளிர் மாதம்"},
            {"word": "டிசம்பர்", "translation": "December", "example": "டிசம்பர் ஆண்டு முடிவு"},
            {"word": "இன்று", "translation": "Today", "example": "இன்று நல்ல நாள்"},
            {"word": "நேற்று", "translation": "Yesterday", "example": "நேற்று மழை பெய்தது"},
            {"word": "நாளை", "translation": "Tomorrow", "example": "நாளை பள்ளி செல்கிறேன்"}
        ]
    }
]

def create_lesson_content(lesson_config):
    """Create comprehensive lesson content"""
    
    # Generate conversations
    conversations = [
        {
            "title": f"Learning about {lesson_config['topic']}",
            "scenario": f"A conversation about {lesson_config['topic'].lower()}",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": f"{lesson_config['vocabulary'][0]['word']} என்றால் என்ன?",
                    "speaker": "Teacher",
                    "translation": f"What does {lesson_config['vocabulary'][0]['word']} mean?",
                    "pronunciation": f"{lesson_config['vocabulary'][0]['word']} endraal enna?",
                    "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_conv_01_01.mp3"
                },
                {
                    "text": f"{lesson_config['vocabulary'][0]['word']} என்றால் {lesson_config['vocabulary'][0]['translation']}",
                    "speaker": "Student", 
                    "translation": f"{lesson_config['vocabulary'][0]['word']} means {lesson_config['vocabulary'][0]['translation']}",
                    "pronunciation": f"{lesson_config['vocabulary'][0]['word']} endraal {lesson_config['vocabulary'][0]['translation']}",
                    "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_conv_01_02.mp3"
                }
            ]
        }
    ]
    
    # Generate grammar points
    grammar_points = [
        {
            "rule": "Noun Usage",
            "explanation": f"Learn how to use {lesson_config['topic'].lower()} nouns in Tamil sentences",
            "examples": [
                f"{lesson_config['vocabulary'][0]['example']}",
                f"{lesson_config['vocabulary'][1]['example']}",
                f"{lesson_config['vocabulary'][2]['example']}"
            ],
            "examples_audio_urls": [
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_01.mp3",
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_02.mp3",
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_03.mp3"
            ],
            "tips": f"Practice using {lesson_config['topic'].lower()} words in daily conversations"
        }
    ]
    
    # Generate exercises
    exercises = [
        {
            "type": "multiple_choice",
            "question": f"What is the Tamil word for '{lesson_config['vocabulary'][0]['translation']}'?",
            "options": [
                lesson_config['vocabulary'][0]['word'],
                lesson_config['vocabulary'][1]['word'], 
                lesson_config['vocabulary'][2]['word'],
                lesson_config['vocabulary'][3]['word']
            ],
            "correctAnswer": 0,
            "explanation": f"{lesson_config['vocabulary'][0]['word']} means {lesson_config['vocabulary'][0]['translation']}",
            "points": 10
        }
    ]
    
    return {
        "path_id": TAMIL_A1_PATH_ID,
        "title": lesson_config["title"],
        "description": lesson_config["description"],
        "lesson_type": "vocabulary",
        "difficulty_level": 1,
        "estimated_duration": 25,
        "sequence_order": lesson_config["sequence_order"],
        "learning_objectives": [
            f"Learn {lesson_config['topic'].lower()} vocabulary",
            "Practice pronunciation with audio",
            "Understand basic sentence structure"
        ],
        "vocabulary_focus": [],
        "grammar_concepts": ["Basic sentence structure", "Noun usage"],
        "cultural_notes": f"Tamil {lesson_config['topic'].lower()} in daily life context",
        "prerequisite_lessons": [],
        "content_metadata": {
            "vocabulary": lesson_config["vocabulary"],
            "conversations": conversations,
            "grammar_points": grammar_points,
            "exercises": exercises
        },
        "is_active": True,
        "has_audio": True,
        "audio_metadata": {
            "total_files": len(lesson_config["vocabulary"]) * 2,
            "voice_id": "9BWtsMINqrJLrRacOk9x",
            "generated_at": datetime.now().isoformat()
        }
    }

def add_lesson_to_database(lesson_data):
    """Add lesson to Supabase database"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Create lesson record
        lesson_record = {
            "id": str(uuid.uuid4()),
            "path_id": lesson_data["path_id"],
            "title": lesson_data["title"],
            "description": lesson_data["description"],
            "lesson_type": lesson_data["lesson_type"],
            "difficulty_level": lesson_data["difficulty_level"],
            "estimated_duration": lesson_data["estimated_duration"],
            "sequence_order": lesson_data["sequence_order"],
            "learning_objectives": lesson_data["learning_objectives"],
            "vocabulary_focus": lesson_data["vocabulary_focus"],
            "grammar_concepts": lesson_data["grammar_concepts"],
            "cultural_notes": lesson_data["cultural_notes"],
            "prerequisite_lessons": lesson_data["prerequisite_lessons"],
            "content_metadata": lesson_data["content_metadata"],
            "is_active": lesson_data["is_active"],
            "has_audio": lesson_data["has_audio"],
            "audio_metadata": lesson_data["audio_metadata"]
        }
        
        response = requests.post(url, json=lesson_record, headers=headers)
        
        if response.status_code == 201:
            print(f"✅ Successfully added lesson: {lesson_data['title']}")
            return True
        else:
            print(f"❌ Failed to add lesson: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error adding lesson: {e}")
        return False

def main():
    """Add new A1 Tamil lessons"""
    print("🆕 Adding New A1 Tamil Lessons")
    print("=" * 50)
    
    success_count = 0
    
    for lesson_config in NEW_LESSONS:
        print(f"\n📚 Creating lesson: {lesson_config['title']}")
        
        # Create lesson content
        lesson_data = create_lesson_content(lesson_config)
        
        # Add to database
        if add_lesson_to_database(lesson_data):
            success_count += 1
        
        print(f"⏳ Waiting 2 seconds...")
        import time
        time.sleep(2)
    
    print(f"\n🎉 Added {success_count}/{len(NEW_LESSONS)} lessons successfully!")
    print("📱 New lessons are now available in the NIRA app!")

if __name__ == "__main__":
    main()
