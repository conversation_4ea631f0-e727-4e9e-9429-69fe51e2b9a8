# 🎵 NIRA Audio Generation System

## 📋 Overview

This system generates high-quality Tamil audio for all NIRA lesson content using ElevenLabs API and stores it in Supabase Storage. **This is a PREREQUISITE** before the app can function properly.

## 🎯 Current Status

### ✅ **Animals and Nature Lesson (Lesson 24)**
- **25 vocabulary items** - each needs 2 audio files (word + example) = **50 files**
- **15 conversations** - each exchange needs audio = **30 files** 
- **10 grammar points** - each with 3 examples = **30 files**
- **25 exercises** - questions and explanations = **50 files**

**Total: ~160 audio files needed for this lesson alone**

## 🔧 Setup Instructions

### 1. **Get ElevenLabs API Key**
```bash
# Sign up at https://elevenlabs.io
# Get your API key from the dashboard
# Update the key in both places:
```

**File 1:** `NIRA/Config/APIKeys.swift`
```swift
static let elevenLabsAPIKey = "sk_YOUR_REAL_API_KEY_HERE"
```

**File 2:** `NIRA/Scripts/generate_animals_audio.py`
```python
ELEVENLABS_API_KEY = "sk_YOUR_REAL_API_KEY_HERE"
```

### 2. **Install Python Dependencies**
```bash
cd NIRA/Scripts
pip install requests
```

### 3. **Create Audio Directory**
```bash
mkdir -p Assets/Audio/Tamil/A1/Lesson24
```

## 🚀 Generation Process

### **Step 1: Generate Audio Files**
```bash
cd NIRA/Scripts
python generate_animals_audio.py
```

This will create:
- `Assets/Audio/Tamil/A1/Lesson24/vocab_01_word.mp3`
- `Assets/Audio/Tamil/A1/Lesson24/vocab_01_example.mp3`
- `Assets/Audio/Tamil/A1/Lesson24/conv_01.mp3`
- `Assets/Audio/Tamil/A1/Lesson24/grammar_01.mp3`
- ... and many more

### **Step 2: Upload to Supabase**
```bash
python upload_and_update_audio.py
```

This will:
- Upload all audio files to Supabase Storage
- Generate public URLs
- Save URLs to `audio_urls.json`

### **Step 3: Update Database**
Manually update the lesson content_metadata with audio URLs:

```sql
UPDATE lessons 
SET content_metadata = jsonb_set(
  content_metadata, 
  '{vocabulary,0,word_audio_url}', 
  '"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_24_vocab_01_word.mp3"'
)
WHERE id = 'b966c742-d36d-4d94-9e35-7c17a5039487';
```

## 📊 Content Breakdown

### **Vocabulary Audio (50 files)**
Each vocabulary item needs:
- **Word audio**: Tamil word pronunciation
- **Example audio**: Full Tamil sentence

Example:
- `vocab_01_word.mp3` → "நாய்" (naai)
- `vocab_01_example.mp3` → "நாய் குரைக்கிறது" (naai kuraikkirathu)

### **Conversation Audio (30 files)**
Each conversation exchange:
- `conv_01.mp3` → "இது என்ன விலங்கு?" (idhu enna vilangku?)
- `conv_02.mp3` → "இது யானை" (idhu yaanai)

### **Grammar Audio (30 files)**
Each grammar example:
- `grammar_01.mp3` → "நாய் குரைக்கிறது (naai kuraikkirathu) - The dog is barking"

### **Exercise Audio (50 files)**
Questions and explanations for practice exercises.

## 🎛️ Voice Configuration

### **Recommended ElevenLabs Voices:**
- **Vocabulary**: Adam (pNInz6obpgDQGcFmaJgB) - Clear pronunciation
- **Conversations**: Bella (EXAVITQu4vr4xnSDxMaL) - Natural conversation
- **Grammar**: Adam (pNInz6obpgDQGcFmaJgB) - Educational tone

### **Voice Settings:**
```json
{
  "stability": 0.5,
  "similarity_boost": 0.8,
  "style": 0.2,
  "use_speaker_boost": true
}
```

## 📁 File Structure

```
NIRA/
├── Assets/Audio/Tamil/A1/Lesson24/
│   ├── vocab_01_word.mp3
│   ├── vocab_01_example.mp3
│   ├── vocab_02_word.mp3
│   ├── vocab_02_example.mp3
│   ├── ...
│   ├── conv_01.mp3
│   ├── conv_02.mp3
│   ├── ...
│   ├── grammar_01.mp3
│   ├── grammar_02.mp3
│   └── ...
├── Scripts/
│   ├── generate_animals_audio.py
│   ├── upload_and_update_audio.py
│   ├── batch_audio_generation.py
│   └── README_AUDIO_GENERATION.md
└── Config/
    └── APIKeys.swift
```

## 🔄 Workflow for Other Lessons

1. **Extract lesson content** from database
2. **Modify generation script** with new content
3. **Generate audio files** using ElevenLabs
4. **Upload to Supabase Storage**
5. **Update database** with audio URLs
6. **Test in app**

## ⚠️ Important Notes

### **Rate Limiting**
- ElevenLabs has API rate limits
- Add `time.sleep(1)` between requests
- Monitor your usage quota

### **Cost Management**
- Each audio generation costs credits
- Pre-generate all content in batches
- Don't generate audio in real-time for production

### **Quality Control**
- Test audio files before uploading
- Ensure Tamil pronunciation is correct
- Use consistent voice settings

## 🧪 Testing

### **Test Audio Generation:**
```bash
# Test with a single word
python -c "
from generate_animals_audio import generate_audio
generate_audio('நாய்', 'test_audio.mp3')
"
```

### **Test Upload:**
```bash
# Test Supabase upload
python -c "
from upload_and_update_audio import upload_file_to_supabase
url = upload_file_to_supabase('test_audio.mp3', 'test/test.mp3')
print(f'URL: {url}')
"
```

## 🎯 Next Steps

1. **Get real ElevenLabs API key** ⚠️ **CRITICAL**
2. **Generate all audio for Animals & Nature lesson**
3. **Test audio playback in app**
4. **Repeat for remaining 24 A1 lessons**
5. **Scale to A2 and B1 levels**

## 📞 Support

If you encounter issues:
1. Check API key configuration
2. Verify Supabase permissions
3. Test with small batches first
4. Monitor ElevenLabs usage dashboard

---

**Remember: Audio generation is REQUIRED before the app can function properly!** 🎵
