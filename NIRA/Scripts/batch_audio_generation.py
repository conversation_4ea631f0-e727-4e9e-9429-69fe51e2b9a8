#!/usr/bin/env python3
"""
Comprehensive Batch Audio Generation for NIRA Tamil Lessons
Generates audio for ALL content types: vocabulary, conversations, grammar, exercises
"""

import os
import json
import requests
import time
from pathlib import Path
from typing import Dict, List, Any
import argparse

# Configuration
ELEVENLABS_API_KEY = "sk_b8b8c8e8f8e8f8e8f8e8f8e8f8e8f8e8f8e8f8e8"  # TODO: Replace with real key
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NzI4NzQsImV4cCI6MjA1MDA0ODg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"

# Voice configurations for different content types
VOICE_CONFIG = {
    "vocabulary": {
        "voice_id": "pNInz6obpgDQGcFmaJgB",  # Adam - clear pronunciation
        "stability": 0.5,
        "similarity_boost": 0.8,
        "style": 0.2
    },
    "conversations": {
        "voice_id": "EXAVITQu4vr4xnSDxMaL",  # Bella - conversational
        "stability": 0.6,
        "similarity_boost": 0.7,
        "style": 0.4
    },
    "grammar": {
        "voice_id": "pNInz6obpgDQGcFmaJgB",  # Adam - educational
        "stability": 0.7,
        "similarity_boost": 0.8,
        "style": 0.1
    },
    "exercises": {
        "voice_id": "EXAVITQu4vr4xnSDxMaL",  # Bella - engaging
        "stability": 0.6,
        "similarity_boost": 0.7,
        "style": 0.3
    }
}

class AudioGenerator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "xi-api-key": ELEVENLABS_API_KEY,
            "Content-Type": "application/json"
        })
        self.generated_files = []
        
    def generate_audio(self, text: str, voice_config: Dict, output_path: str) -> bool:
        """Generate audio using ElevenLabs API"""
        try:
            print(f"🎵 Generating audio for: {text[:50]}...")
            
            # ElevenLabs API endpoint
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_config['voice_id']}"
            
            payload = {
                "text": text,
                "model_id": "eleven_multilingual_v2",
                "voice_settings": {
                    "stability": voice_config["stability"],
                    "similarity_boost": voice_config["similarity_boost"],
                    "style": voice_config["style"]
                }
            }
            
            response = self.session.post(url, json=payload)
            
            if response.status_code == 200:
                # Save audio file
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                with open(output_path, 'wb') as f:
                    f.write(response.content)
                
                self.generated_files.append(output_path)
                print(f"✅ Generated: {output_path}")
                return True
            else:
                print(f"❌ Failed to generate audio: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error generating audio: {e}")
            return False
    
    def upload_to_supabase(self, file_path: str, storage_path: str) -> str:
        """Upload audio file to Supabase Storage"""
        try:
            print(f"📤 Uploading {file_path} to Supabase...")
            
            # Read file
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Upload to Supabase Storage
            upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{storage_path}"
            headers = {
                "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
                "Content-Type": "audio/mpeg"
            }
            
            response = requests.post(upload_url, data=file_data, headers=headers)
            
            if response.status_code in [200, 201]:
                public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
                print(f"✅ Uploaded: {public_url}")
                return public_url
            else:
                print(f"❌ Upload failed: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            print(f"❌ Upload error: {e}")
            return ""

def generate_lesson_audio(lesson_data: Dict, output_dir: str = "generated_audio"):
    """Generate all audio for a lesson"""
    generator = AudioGenerator()
    lesson_id = lesson_data["id"]
    content = lesson_data["content_metadata"]
    
    print(f"\n🎯 Generating audio for lesson: {lesson_data['title']}")
    print(f"📊 Content summary:")
    print(f"   - Vocabulary: {len(content.get('vocabulary', []))} items")
    print(f"   - Conversations: {len(content.get('conversations', []))} dialogues")
    print(f"   - Grammar: {len(content.get('grammar_points', []))} points")
    print(f"   - Exercises: {len(content.get('exercises', []))} questions")
    
    audio_urls = {}
    
    # 1. Generate Vocabulary Audio
    print(f"\n📚 Generating vocabulary audio...")
    vocabulary = content.get("vocabulary", [])
    for i, vocab in enumerate(vocabulary, 1):
        word = vocab["word"]
        example = vocab["example"]
        
        # Generate word audio
        word_file = f"{output_dir}/vocab_{i:02d}_word.mp3"
        if generator.generate_audio(word, VOICE_CONFIG["vocabulary"], word_file):
            word_url = generator.upload_to_supabase(
                word_file, 
                f"tamil/a1/lesson_24_vocab_{i:02d}_word.mp3"
            )
            vocab["word_audio_url"] = word_url
        
        # Generate example audio
        example_file = f"{output_dir}/vocab_{i:02d}_example.mp3"
        if generator.generate_audio(example, VOICE_CONFIG["vocabulary"], example_file):
            example_url = generator.upload_to_supabase(
                example_file, 
                f"tamil/a1/lesson_24_vocab_{i:02d}_example.mp3"
            )
            vocab["example_audio_url"] = example_url
        
        time.sleep(1)  # Rate limiting
    
    # 2. Generate Conversation Audio
    print(f"\n💬 Generating conversation audio...")
    conversations = content.get("conversations", [])
    for i, conv in enumerate(conversations, 1):
        for j, exchange in enumerate(conv.get("exchanges", [])):
            text = exchange["text"]
            
            conv_file = f"{output_dir}/conv_{i:02d}_{j:02d}.mp3"
            if generator.generate_audio(text, VOICE_CONFIG["conversations"], conv_file):
                conv_url = generator.upload_to_supabase(
                    conv_file, 
                    f"tamil/a1/lesson_24_conv_{i:02d}_{j:02d}.mp3"
                )
                exchange["audio_url"] = conv_url
            
            time.sleep(1)  # Rate limiting
    
    # 3. Generate Grammar Audio
    print(f"\n📖 Generating grammar audio...")
    grammar_points = content.get("grammar_points", [])
    for i, grammar in enumerate(grammar_points, 1):
        for j, example in enumerate(grammar.get("examples", [])):
            grammar_file = f"{output_dir}/grammar_{i:02d}_{j:02d}.mp3"
            if generator.generate_audio(example, VOICE_CONFIG["grammar"], grammar_file):
                grammar_url = generator.upload_to_supabase(
                    grammar_file, 
                    f"tamil/a1/lesson_24_grammar_{i:02d}_{j:02d}.mp3"
                )
                # Add audio URL to example (we'll need to modify the data structure)
                if "audio_urls" not in grammar:
                    grammar["audio_urls"] = []
                grammar["audio_urls"].append(grammar_url)
            
            time.sleep(1)  # Rate limiting
    
    print(f"\n🎉 Audio generation complete!")
    print(f"📁 Generated {len(generator.generated_files)} audio files")
    
    return content

def main():
    parser = argparse.ArgumentParser(description="Generate audio for NIRA Tamil lessons")
    parser.add_argument("--lesson-id", default="b966c742-d36d-4d94-9e35-7c17a5039487", 
                       help="Lesson ID to generate audio for")
    parser.add_argument("--output-dir", default="generated_audio", 
                       help="Output directory for audio files")
    
    args = parser.parse_args()
    
    # For now, use the lesson data we have
    lesson_data = {
        "id": args.lesson_id,
        "title": "Animals and Nature",
        "content_metadata": {
            # We'll load this from the database or use the data we have
        }
    }
    
    print("🚀 Starting batch audio generation for NIRA Tamil lessons")
    print(f"🎯 Target lesson: {lesson_data['title']}")
    
    # Generate audio
    updated_content = generate_lesson_audio(lesson_data, args.output_dir)
    
    print("\n✅ Batch audio generation completed!")
    print("📋 Next steps:")
    print("   1. Update lesson content_metadata with new audio URLs")
    print("   2. Test audio playback in the app")
    print("   3. Repeat for other lessons")

if __name__ == "__main__":
    main()
