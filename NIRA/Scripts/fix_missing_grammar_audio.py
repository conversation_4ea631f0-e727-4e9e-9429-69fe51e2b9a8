#!/usr/bin/env python3
"""
Fix Missing Grammar Audio URLs
"""

import requests
import json

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Lesson ID for Animals and Nature
LESSON_ID = "b966c742-d36d-4d94-9e35-7c17a5039487"

def fix_grammar_audio_urls():
    """Fix missing audio URLs in grammar examples"""
    try:
        print("🔄 Checking and fixing grammar audio URLs...")
        
        # Get current lesson content
        query_url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{LESSON_ID}&select=content_metadata"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(query_url, headers=headers)
        if response.status_code != 200:
            print(f"❌ Failed to fetch lesson: {response.status_code}")
            return False
            
        lesson_data = response.json()[0]
        content_metadata = lesson_data["content_metadata"]
        
        # Check grammar points for missing audio URLs
        grammar_points = content_metadata.get("grammar_points", [])
        fixed_count = 0
        
        for gram_i, grammar in enumerate(grammar_points):
            examples = grammar.get("examples", [])
            examples_audio_urls = grammar.get("examples_audio_urls", [])
            
            # Ensure we have audio URLs for all examples
            if len(examples_audio_urls) < len(examples):
                print(f"🔧 Fixing grammar point {gram_i + 1}: {grammar.get('rule', 'Unknown')}")
                
                # Generate missing audio URLs
                new_audio_urls = []
                for ex_i, example in enumerate(examples):
                    if ex_i < len(examples_audio_urls) and examples_audio_urls[ex_i]:
                        # Keep existing URL
                        new_audio_urls.append(examples_audio_urls[ex_i])
                    else:
                        # Generate new URL
                        audio_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/grammar_{gram_i+1:02d}_{ex_i+1:02d}.mp3"
                        new_audio_urls.append(audio_url)
                        print(f"  ✅ Added: grammar_{gram_i+1:02d}_{ex_i+1:02d}.mp3")
                        fixed_count += 1
                
                # Update the grammar point
                grammar_points[gram_i]["examples_audio_urls"] = new_audio_urls
        
        if fixed_count > 0:
            # Update the lesson in database
            content_metadata["grammar_points"] = grammar_points
            update_url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{LESSON_ID}"
            update_data = {"content_metadata": content_metadata}
            
            response = requests.patch(update_url, json=update_data, headers=headers)
            
            if response.status_code == 204:
                print(f"✅ Successfully fixed {fixed_count} missing grammar audio URLs!")
                return True
            else:
                print(f"❌ Failed to update lesson: {response.status_code} - {response.text}")
                return False
        else:
            print("✅ All grammar audio URLs are already present!")
            return True
            
    except Exception as e:
        print(f"❌ Error fixing grammar audio URLs: {e}")
        return False

def test_audio_urls():
    """Test a few audio URLs to make sure they exist"""
    print("🧪 Testing sample audio URLs...")
    
    test_urls = [
        f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/grammar_01_01.mp3",
        f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/grammar_01_02.mp3",
        f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/grammar_01_03.mp3"
    ]
    
    for url in test_urls:
        try:
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ EXISTS: {url.split('/')[-1]}")
            else:
                print(f"❌ MISSING: {url.split('/')[-1]} (HTTP {response.status_code})")
        except Exception as e:
            print(f"❌ ERROR: {url.split('/')[-1]} - {e}")

def main():
    """Fix missing grammar audio URLs"""
    print("🔧 Fixing missing grammar audio URLs...")
    
    if fix_grammar_audio_urls():
        print("🎉 Grammar audio URLs fixed successfully!")
        test_audio_urls()
    else:
        print("❌ Failed to fix grammar audio URLs")

if __name__ == "__main__":
    main()
