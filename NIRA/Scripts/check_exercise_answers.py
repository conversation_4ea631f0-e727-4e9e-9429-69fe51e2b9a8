#!/usr/bin/env python3
"""
Check Exercise Answer Validation
"""

import requests
import json

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Lesson ID for Animals and Nature
LESSON_ID = "b966c742-d36d-4d94-9e35-7c17a5039487"

def check_exercise_answers():
    """Check if exercise answers are correct"""
    try:
        print("🔍 Checking exercise answer validation...")
        
        # Get current lesson content
        query_url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{LESSON_ID}&select=content_metadata"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(query_url, headers=headers)
        if response.status_code != 200:
            print(f"❌ Failed to fetch lesson: {response.status_code}")
            return False
            
        lesson_data = response.json()[0]
        content_metadata = lesson_data["content_metadata"]
        exercises = content_metadata.get("exercises", [])
        
        print(f"📝 Found {len(exercises)} exercises")
        print()
        
        issues_found = 0
        
        for i, exercise in enumerate(exercises[:5]):  # Check first 5
            print(f"🧩 Exercise {i+1}:")
            print(f"   Question: {exercise.get('question', 'No question')}")
            
            options = exercise.get('options', [])
            correct_answer = exercise.get('correctAnswer', -1)
            
            print(f"   Options ({len(options)}):")
            for j, option in enumerate(options):
                marker = "✅" if j == correct_answer else "  "
                print(f"     {marker} {j}: {option}")
            
            print(f"   Correct Answer Index: {correct_answer}")
            
            # Validate
            if correct_answer < 0 or correct_answer >= len(options):
                print(f"   ❌ ERROR: correctAnswer {correct_answer} is out of range (0-{len(options)-1})")
                issues_found += 1
            else:
                print(f"   ✅ Valid: {options[correct_answer]}")
            
            print()
        
        if issues_found > 0:
            print(f"❌ Found {issues_found} issues with exercise answers!")
            return False
        else:
            print("✅ All exercise answers are valid!")
            return True
            
    except Exception as e:
        print(f"❌ Error checking exercises: {e}")
        return False

def main():
    """Check exercise answer validation"""
    print("🔧 Checking exercise answer validation...")
    
    if check_exercise_answers():
        print("🎉 Exercise validation check completed successfully!")
    else:
        print("❌ Issues found with exercise validation")

if __name__ == "__main__":
    main()
