# 🎵 Complete Tamil Audio Setup Guide for NIRA

## 🎯 Overview

This guide will help you generate ALL Tamil audio for the Animals and Nature lesson using ElevenLabs API with your preferred Freya and Elli voices, upload to Supabase Storage, and properly link everything in the app.

## ⚠️ Prerequisites

1. **ElevenLabs API Key** - Get from https://elevenlabs.io
2. **Supabase Access** - Already configured
3. **Python 3** - Already available on your system

## 🚀 Step-by-Step Process

### **Step 1: Configure ElevenLabs API Key**

Update the API key in the generation script:

```bash
cd /Users/<USER>/Documents/NIRA
nano NIRA/Scripts/generate_animals_audio.py
```

Change line 15:
```python
ELEVENLABS_API_KEY = "YOUR_REAL_ELEVENLABS_API_KEY_HERE"
```

To:
```python
ELEVENLABS_API_KEY = "sk_your_actual_api_key_here"
```

### **Step 2: Generate All Tamil Audio**

Run the comprehensive audio generation script:

```bash
cd /Users/<USER>/Documents/NIRA
python3 NIRA/Scripts/generate_animals_audio.py
```

**What this does:**
- ✅ Fetches Animals and Nature lesson from Supabase
- ✅ Extracts ALL Tamil content (vocabulary, conversations, grammar, exercises)
- ✅ Generates audio using Freya and Elli voices (alternating)
- ✅ Uploads each audio file to Supabase Storage
- ✅ Creates public URLs for each audio file
- ✅ Saves all URLs to `generated_audio_urls.json`

**Expected Output:**
```
🚀 Starting complete Tamil audio generation for Animals and Nature lesson
📥 Fetching lesson content from Supabase...
✅ Fetched lesson: Animals and Nature
📊 Found 150+ Tamil text items to generate audio for

🎵 Processing 1/150: நாய்...
✅ Generated audio: vocab_01_word.mp3
📤 Uploading tamil/a1/animals_nature/vocab_01_word.mp3...
✅ Uploaded: https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_01_word.mp3
✅ Complete: vocab_01_word.mp3

[... continues for all items ...]

🎉 Tamil audio generation complete!
📊 Generated 150/150 audio files
📁 Audio URLs saved to: generated_audio_urls.json
```

### **Step 3: Update Lesson Content with Audio URLs**

Link the generated audio URLs to the lesson content:

```bash
python3 NIRA/Scripts/update_lesson_with_audio_urls.py
```

**What this does:**
- ✅ Reads `generated_audio_urls.json`
- ✅ Fetches current lesson content from Supabase
- ✅ Updates content_metadata with proper audio URL relationships
- ✅ Saves updated lesson back to Supabase

**Expected Output:**
```
📁 Loaded 150 audio URLs from generated_audio_urls.json
📥 Fetching current lesson content...
✅ Fetched lesson: Animals and Nature
🔗 Updating content with 150 audio URLs...
   Adding: vocabulary.0.word_audio_url -> https://...
   Adding: vocabulary.0.example_audio_url -> https://...
   [... continues ...]
📝 Updating lesson in database...
✅ Successfully updated lesson content

🎉 Successfully updated Animals and Nature lesson with audio URLs!
📊 Updated 150 audio references
```

### **Step 4: Update iOS App Audio Configuration**

Update the ElevenLabs API key in the iOS app:

```swift
// File: NIRA/Config/APIKeys.swift
static let elevenLabsAPIKey = "sk_your_actual_api_key_here"
```

### **Step 5: Test Audio in App**

1. **Build and run** the NIRA app
2. **Navigate** to Animals and Nature lesson
3. **Test audio buttons** in:
   - ✅ Vocabulary section (word + example audio)
   - ✅ Conversations section (dialogue audio)
   - ✅ Grammar section (example audio)
   - ✅ Practice exercises (option audio)

## 📊 What Gets Generated

### **Vocabulary Audio (50 files)**
- `vocab_01_word.mp3` → "நாய்" (Freya voice)
- `vocab_01_example.mp3` → "நாய் குரைக்கிறது" (Elli voice)
- `vocab_02_word.mp3` → "பூனை" (Freya voice)
- ... and so on for all 25 vocabulary items

### **Conversation Audio (30+ files)**
- `conv_01_01.mp3` → "இது என்ன விலங்கு?" (Freya voice)
- `conv_01_02.mp3` → "இது யானை" (Elli voice)
- ... for all conversation exchanges

### **Grammar Audio (30+ files)**
- `grammar_01_01.mp3` → "நாய் குரைக்கிறது" (Freya voice)
- `grammar_01_02.mp3` → "பறவை பறக்கிறது" (Elli voice)
- ... for all grammar examples

### **Exercise Audio (25+ files)**
- `exercise_01_option_01.mp3` → "நாய்" (Freya voice)
- `exercise_01_option_02.mp3` → "பூனை" (Elli voice)
- ... for all Tamil exercise options

## 🔧 Audio System Features

### **Caching System**
- ✅ Audio files cached locally when played
- ✅ Subsequent plays use cached version
- ✅ No repeated downloads

### **Voice Configuration**
- ✅ **Freya voice** for vocabulary words and odd-numbered items
- ✅ **Elli voice** for examples and even-numbered items
- ✅ High-quality Tamil pronunciation
- ✅ Consistent voice settings across all audio

### **URL Relationships**
- ✅ Each vocabulary word has `word_audio_url`
- ✅ Each vocabulary example has `example_audio_url`
- ✅ Each conversation exchange has `audio_url`
- ✅ Each grammar example has entry in `examples_audio_urls` array
- ✅ Each exercise option has entry in `options_audio_urls` array

## 🧪 Testing Checklist

After completing the setup:

- [ ] **Vocabulary**: Tap audio button next to "நாய்" - should play clear Tamil pronunciation
- [ ] **Examples**: Tap audio button next to "நாய் குரைக்கிறது" - should play full sentence
- [ ] **Conversations**: Tap audio button next to "இது என்ன விலங்கு?" - should play question
- [ ] **Grammar**: Tap audio button next to grammar examples - should play Tamil part only
- [ ] **Exercises**: Tap audio button next to Tamil options - should play option pronunciation
- [ ] **Caching**: Play same audio twice - second time should be instant (cached)
- [ ] **No errors**: No "Audio file not available" messages
- [ ] **Voice quality**: Clear, natural Tamil pronunciation using Freya/Elli voices

## 🔄 For Other Lessons

Once this works perfectly for Animals and Nature:

1. **Modify** `LESSON_ID` in the scripts
2. **Run** the same process for other lessons
3. **Scale** to all 25 A1 lessons
4. **Expand** to A2 and B1 levels

## 📞 Troubleshooting

### **"Please update ELEVENLABS_API_KEY"**
- Update the API key in `generate_animals_audio.py`

### **"Failed to fetch lesson content"**
- Check Supabase connection
- Verify lesson ID is correct

### **"Upload failed"**
- Check Supabase Storage permissions
- Verify storage bucket exists

### **"Audio file not available" in app**
- Ensure Step 3 (update lesson content) completed successfully
- Check that URLs are properly linked in database

---

**🎉 Once complete, you'll have a fully functional Tamil audio system with caching, proper voice selection, and seamless UI integration!**
