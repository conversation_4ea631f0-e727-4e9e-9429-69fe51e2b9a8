#!/usr/bin/env python3
"""
Generate Audio URLs from Existing Files
Since many audio files already exist in Supabase Storage, this script generates the URLs
"""

import json
import requests

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Lesson ID for Animals and Nature
LESSON_ID = "b966c742-d36d-4d94-9e35-7c17a5039487"

def fetch_lesson_content():
    """Fetch the Animals and Nature lesson content from Supabase"""
    try:
        print("📥 Fetching lesson content from Supabase...")
        
        query_url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        params = {
            "id": f"eq.{LESSON_ID}",
            "select": "id,title,content_metadata"
        }
        
        response = requests.get(query_url, headers=headers, params=params)
        
        if response.status_code == 200:
            lessons = response.json()
            if lessons and len(lessons) > 0:
                lesson = lessons[0]
                print(f"✅ Fetched lesson: {lesson['title']}")
                return lesson['content_metadata']
            else:
                print("❌ No lesson found with that ID")
                return None
        else:
            print(f"❌ Failed to fetch lesson: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error fetching lesson: {e}")
        return None

def generate_audio_urls_from_content(content_metadata):
    """Generate audio URLs for all Tamil content based on existing files"""
    audio_urls = {}
    
    # Generate vocabulary URLs
    vocabulary = content_metadata.get("vocabulary", [])
    for i, vocab in enumerate(vocabulary):
        word = vocab.get("word", "")
        example = vocab.get("example", "")
        
        if word:
            word_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_{i+1:02d}_word.mp3"
            audio_urls[f"vocabulary.{i}.word_audio_url"] = word_url
        
        if example:
            example_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_{i+1:02d}_example.mp3"
            audio_urls[f"vocabulary.{i}.example_audio_url"] = example_url
    
    # Generate conversation URLs
    conversations = content_metadata.get("conversations", [])
    for conv_i, conversation in enumerate(conversations):
        exchanges = conversation.get("exchanges", [])
        for ex_i, exchange in enumerate(exchanges):
            text = exchange.get("text", "")
            if text and contains_tamil(text):
                conv_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/conv_{conv_i+1:02d}_{ex_i+1:02d}.mp3"
                audio_urls[f"conversations.{conv_i}.exchanges.{ex_i}.audio_url"] = conv_url
    
    # Generate grammar URLs
    grammar_points = content_metadata.get("grammar_points", [])
    for gram_i, grammar in enumerate(grammar_points):
        examples = grammar.get("examples", [])
        for ex_i, example in enumerate(examples):
            tamil_text = extract_tamil_from_example(example)
            if tamil_text:
                grammar_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/grammar_{gram_i+1:02d}_{ex_i+1:02d}.mp3"
                audio_urls[f"grammar_points.{gram_i}.examples_audio_urls.{ex_i}"] = grammar_url
    
    # Generate exercise URLs
    exercises = content_metadata.get("exercises", [])
    for ex_i, exercise in enumerate(exercises):
        options = exercise.get("options", [])
        for opt_i, option in enumerate(options):
            if contains_tamil(option):
                exercise_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/exercise_{ex_i+1:02d}_option_{opt_i+1:02d}.mp3"
                audio_urls[f"exercises.{ex_i}.options_audio_urls.{opt_i}"] = exercise_url
    
    return audio_urls

def contains_tamil(text):
    """Check if text contains Tamil Unicode characters"""
    return any('\u0B80' <= char <= '\u0BFF' for char in text)

def extract_tamil_from_example(example):
    """Extract Tamil text from grammar examples"""
    import re
    if " - " in example:
        tamil_part = example.split(" - ")[0]
        tamil_only = re.sub(r'\s*\([^)]*\)', '', tamil_part)
        return tamil_only.strip()
    return ""

def test_audio_urls(audio_urls, sample_size=5):
    """Test a sample of audio URLs to see which ones actually exist"""
    print(f"\n🧪 Testing {sample_size} sample audio URLs...")
    
    tested_urls = list(audio_urls.items())[:sample_size]
    existing_count = 0
    
    for path, url in tested_urls:
        try:
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ EXISTS: {url}")
                existing_count += 1
            else:
                print(f"❌ MISSING: {url} (HTTP {response.status_code})")
        except Exception as e:
            print(f"❌ ERROR: {url} - {e}")
    
    print(f"\n📊 Sample test results: {existing_count}/{sample_size} files exist")
    return existing_count > 0

def main():
    """Generate audio URLs from existing files and save them"""
    
    print("🚀 Generating audio URLs from existing files in Supabase Storage")
    
    # 1. Fetch lesson content
    content_metadata = fetch_lesson_content()
    if not content_metadata:
        print("❌ Failed to fetch lesson content")
        return
    
    # 2. Generate audio URLs
    print("🔗 Generating audio URLs for all Tamil content...")
    audio_urls = generate_audio_urls_from_content(content_metadata)
    print(f"📊 Generated {len(audio_urls)} audio URLs")
    
    # 3. Test a sample of URLs
    if test_audio_urls(audio_urls):
        print("✅ Audio files exist in Supabase Storage!")
    else:
        print("⚠️  Some audio files may be missing")
    
    # 4. Save URLs to file
    with open("generated_audio_urls.json", "w") as f:
        json.dump(audio_urls, f, indent=2)
    
    print(f"\n🎉 Audio URL generation complete!")
    print(f"📁 Saved {len(audio_urls)} URLs to generated_audio_urls.json")
    print(f"\n📋 Next steps:")
    print(f"1. Run: python3 update_lesson_with_audio_urls.py")
    print(f"2. Test audio playback in the NIRA app")
    print(f"3. Verify all audio buttons work correctly")

if __name__ == "__main__":
    main()
