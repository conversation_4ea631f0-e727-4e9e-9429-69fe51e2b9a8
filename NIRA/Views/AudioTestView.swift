import SwiftUI
import AVFoundation

struct AudioTestView: View {
    @StateObject private var audioService = AudioPlayerService.shared
    @StateObject private var audioManager = AudioContentManager.shared
    @StateObject private var elevenLabsService = ElevenLabsService.shared
    @State private var testURLs: [String] = []
    @State private var selectedURL: String = ""
    @State private var testTexts: [String] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Text("Audio Playback Test")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Test remote audio playback with caching")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                
                // Status indicators
                HStack(spacing: 20) {
                    StatusIndicator(
                        title: "Loading",
                        isActive: audioService.isLoading,
                        color: .orange
                    )
                    
                    StatusIndicator(
                        title: "Playing",
                        isActive: audioService.isPlaying,
                        color: .green
                    )
                    
                    StatusIndicator(
                        title: "Error",
                        isActive: audioService.playbackError != nil,
                        color: .red
                    )
                }
                .padding()
                
                // Error display
                if let error = audioService.playbackError {
                    Text("Error: \(error)")
                        .foregroundColor(.red)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                        .padding(.horizontal)
                }
                
                // Test URLs
                VStack(alignment: .leading, spacing: 12) {
                    Text("Remote Audio Files")
                        .font(.headline)
                        .padding(.horizontal)

                    ForEach(testURLs, id: \.self) { url in
                        AudioTestRow(
                            url: url,
                            isSelected: selectedURL == url,
                            isCurrentlyPlaying: audioService.currentAudioURL == url && audioService.isPlaying,
                            isCurrentlyLoading: audioService.currentAudioURL == url && audioService.isLoading
                        ) {
                            selectedURL = url
                            audioService.playAudio(from: url)
                        }
                    }
                }
                .padding()

                // Test Audio Generation
                VStack(alignment: .leading, spacing: 12) {
                    Text("Audio Generation Test")
                        .font(.headline)
                        .padding(.horizontal)

                    ForEach(testTexts, id: \.self) { text in
                        AudioGenerationTestRow(
                            text: text,
                            isGenerating: elevenLabsService.isGenerating
                        )
                    }
                }
                .padding()
                
                Spacer()
                
                // Controls
                HStack(spacing: 20) {
                    Button("Stop") {
                        audioService.stopAudio()
                        selectedURL = ""
                    }
                    .buttonStyle(.bordered)
                    .disabled(!audioService.isPlaying && !audioService.isLoading)
                    
                    Button("Clear Cache") {
                        audioService.clearAudioCache()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Refresh URLs") {
                        loadTestURLs()
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
            }
        }
        .onAppear {
            loadTestURLs()
            loadTestTexts()
        }
    }
    
    private func loadTestURLs() {
        // Load some test URLs from the Supabase storage
        testURLs = [
            "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_1_word.mp3",
            "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_1_example.mp3",
            "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_2_word.mp3"
        ]
    }

    private func loadTestTexts() {
        // Load some test texts for audio generation
        testTexts = [
            "வணக்கம்",
            "நன்றி",
            "தயவுசெய்து",
            "மன்னிக்கவும்",
            "என் பெயர் ராம்"
        ]
    }
}

struct StatusIndicator: View {
    let title: String
    let isActive: Bool
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Circle()
                .fill(isActive ? color : Color.gray.opacity(0.3))
                .frame(width: 12, height: 12)
            
            Text(title)
                .font(.caption)
                .foregroundColor(isActive ? color : .secondary)
        }
    }
}

struct AudioTestRow: View {
    let url: String
    let isSelected: Bool
    let isCurrentlyPlaying: Bool
    let isCurrentlyLoading: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(URL(string: url)?.lastPathComponent ?? "Unknown")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(url)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                if isCurrentlyLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                } else if isCurrentlyPlaying {
                    Image(systemName: "speaker.wave.2.fill")
                        .foregroundColor(.green)
                } else {
                    Image(systemName: "play.circle")
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AudioGenerationTestRow: View {
    let text: String
    let isGenerating: Bool

    @StateObject private var elevenLabsService = ElevenLabsService.shared
    @StateObject private var audioService = AudioPlayerService.shared

    var body: some View {
        Button(action: {
            Task {
                await generateAndPlayAudio()
            }
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(text)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text("Tap to generate and play")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "waveform.badge.plus")
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isGenerating)
    }

    private func generateAndPlayAudio() async {
        do {
            let audioURL = try await elevenLabsService.generateAudio(text: text)
            audioService.playAudio(from: audioURL.path)
        } catch {
            print("❌ Failed to generate audio: \(error)")
        }
    }
}

#Preview {
    AudioTestView()
}
