#!/usr/bin/env python3
"""
🎵 TAMIL AUDIO PROGRESS MONITOR
Tracks Tamil audio generation progress by checking audio URLs in lessons
"""

import requests
import json
from datetime import datetime

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def get_tamil_paths():
    """Get all Tamil learning paths"""
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/learning_paths",
        headers=headers,
        params={'select': 'id,name,level', 'limit': '1000'}
    )
    
    if response.status_code != 200:
        return []
    
    paths = response.json()
    tamil_paths = []
    
    for path in paths:
        name = path.get('name', '').lower()
        if 'tamil' in name:
            tamil_paths.append(path)
    
    return tamil_paths

def count_lessons_with_audio(path_id):
    """Count lessons with audio URLs for a specific path"""
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'path_id': f'eq.{path_id}',
            'select': 'id,title,content_metadata'
        }
    )
    
    if response.status_code != 200:
        return 0, 0
    
    lessons = response.json()
    total_lessons = len(lessons)
    audio_lessons = 0
    
    for lesson in lessons:
        content = lesson.get('content_metadata', {})
        audio_urls = content.get('audio_urls', {})
        
        if audio_urls and len(audio_urls) > 0:
            audio_lessons += 1
    
    return total_lessons, audio_lessons

def main():
    print("🎵 TAMIL AUDIO PROGRESS MONITOR")
    print("="*60)
    print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Get Tamil paths
    tamil_paths = get_tamil_paths()
    
    if not tamil_paths:
        print("❌ No Tamil paths found!")
        return
    
    print(f"📁 Found {len(tamil_paths)} Tamil learning paths")
    print()
    
    total_lessons = 0
    total_with_audio = 0
    
    print("📊 TAMIL AUDIO STATUS BY LEVEL:")
    print("-" * 50)
    
    # Group by level
    levels = {}
    for path in tamil_paths:
        level = path.get('level', 'Unknown').upper()
        if level not in levels:
            levels[level] = []
        levels[level].append(path)
    
    for level in sorted(levels.keys()):
        paths_in_level = levels[level]
        level_lessons = 0
        level_audio = 0
        
        for path in paths_in_level:
            lessons, audio = count_lessons_with_audio(path['id'])
            level_lessons += lessons
            level_audio += audio
        
        if level_lessons > 0:
            percentage = (level_audio / level_lessons * 100) if level_lessons > 0 else 0
            print(f"🎯 {level:<3}: {level_audio:>3}/{level_lessons:<3} lessons ({percentage:>5.1f}%)")
            
            total_lessons += level_lessons
            total_with_audio += level_audio
    
    print("-" * 50)
    
    overall_percentage = (total_with_audio / total_lessons * 100) if total_lessons > 0 else 0
    
    print(f"📈 TOTAL: {total_with_audio}/{total_lessons} lessons with audio ({overall_percentage:.1f}%)")
    print()
    
    if overall_percentage >= 80:
        print("🎉 EXCELLENT PROGRESS! Most Tamil lessons have audio")
    elif overall_percentage >= 50:
        print("🔄 GOOD PROGRESS! About half the lessons have audio")
    elif overall_percentage >= 20:
        print("⚠️  MODERATE PROGRESS: Audio generation is ongoing")
    else:
        print("🚀 EARLY STAGE: Audio generation just started")
    
    print()
    print("💡 Audio files are stored at: lesson-audio/tamil/{level}/{lesson_slug}/")
    print("📊 Re-run this script to track progress")

if __name__ == "__main__":
    main() 