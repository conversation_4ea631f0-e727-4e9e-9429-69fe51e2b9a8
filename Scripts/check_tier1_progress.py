#!/usr/bin/env python3
"""
📊 TIER 1 PROGRESS CHECKER
Quick status check for Tier 1 language creation progress
"""

import requests
import json
from datetime import datetime

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

# Tier 1 Languages
TIER1_LANGUAGES = {
    'english': 'English', 'spanish': 'Spanish', 'french': 'French', 'german': 'German',
    'italian': 'Italian', 'portuguese': 'Portuguese', 'dutch': 'Dutch', 'russian': 'Russian',
    'chinese': 'Chinese', 'japanese': 'Japanese', 'korean': 'Korean', 'arabic': 'Arabic',
    'hindi': 'Hindi', 'tamil': 'Tamil', 'telugu': 'Telugu', 'kannada': 'Kannada',
    'malayalam': 'Malayalam', 'bengali': 'Bengali', 'marathi': 'Marathi', 
    'punjabi': 'Punjabi', 'gujarati': 'Gujarati'
}

def check_learning_paths():
    """Check learning paths for all languages"""
    print("🔍 CHECKING LEARNING PATHS")
    print("="*50)
    
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/learning_paths",
        headers=headers,
        params={'select': 'id,name,level'}
    )
    
    if response.status_code == 200:
        paths = response.json()
        language_paths = {}
        
        for path in paths:
            name = path.get('name', '').lower()
            for lang_key, lang_name in TIER1_LANGUAGES.items():
                if lang_name.lower() in name and ('complete' in name or 'a1-c2' in name):
                    language_paths[lang_key] = {
                        'id': path['id'],
                        'name': path['name'],
                        'level': path.get('level', 'Unknown')
                    }
                    break
        
        print(f"✅ Found {len(language_paths)}/21 language paths:")
        for lang, path_info in language_paths.items():
            print(f"  📁 {TIER1_LANGUAGES[lang]}: {path_info['level']}")
        
        return language_paths
    else:
        print(f"❌ Failed to fetch paths: {response.status_code}")
        return {}

def check_lessons_per_language(language_paths):
    """Check lesson counts for each language"""
    print(f"\n📚 CHECKING LESSON COUNTS")
    print("="*50)
    
    total_lessons = 0
    total_expected = len(TIER1_LANGUAGES) * 130  # 130 lessons per language
    
    print(f"{'Language':<15} | {'Lessons':<8} | {'Progress':<8} | {'Status'}")
    print("-" * 50)
    
    for lang_key, path_info in language_paths.items():
        path_id = path_info['id']
        lang_name = TIER1_LANGUAGES[lang_key]
        
        # Count lessons for this path
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=headers,
            params={
                'path_id': f'eq.{path_id}',
                'select': 'id,title,difficulty_level,content_metadata'
            }
        )
        
        if response.status_code == 200:
            lessons = response.json()
            lesson_count = len(lessons)
            total_lessons += lesson_count
            
            # Check content completeness
            complete_lessons = 0
            for lesson in lessons:
                content = lesson.get('content_metadata', {})
                if (content.get('vocabulary') and 
                    content.get('conversations') and 
                    content.get('grammar_points') and 
                    content.get('exercises')):
                    complete_lessons += 1
            
            progress = f"{lesson_count}/130"
            completion = f"{(lesson_count/130*100):.0f}%"
            status = "✅" if lesson_count >= 100 else "🔄" if lesson_count > 0 else "❌"
            
            print(f"{lang_name:<15} | {progress:<8} | {completion:<8} | {status}")
        else:
            print(f"{lang_name:<15} | ERROR   | 0%       | ❌")
    
    print("-" * 50)
    print(f"📊 TOTALS: {total_lessons}/{total_expected} lessons ({(total_lessons/total_expected*100):.1f}%)")
    
    return total_lessons, total_expected

def check_recent_activity():
    """Check recent lesson creation activity"""
    print(f"\n🕐 RECENT ACTIVITY (Last 1 hour)")
    print("="*50)
    
    # Get lessons created in last hour
    from datetime import datetime, timedelta
    one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()
    
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'created_at': f'gte.{one_hour_ago}',
            'select': 'id,title,created_at',
            'order': 'created_at.desc',
            'limit': '10'
        }
    )
    
    if response.status_code == 200:
        recent_lessons = response.json()
        print(f"📈 {len(recent_lessons)} lessons created in last hour")
        
        if recent_lessons:
            print("Recent lessons:")
            for lesson in recent_lessons[:5]:
                title = lesson.get('title', 'Unknown')[:40]
                created = lesson.get('created_at', '')[:19]
                print(f"  📚 {created}: {title}...")
        else:
            print("⚠️  No recent activity detected")
    else:
        print(f"❌ Failed to check recent activity: {response.status_code}")

def main():
    print("📊 TIER 1 PROGRESS CHECKER")
    print("="*60)
    print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check learning paths
    language_paths = check_learning_paths()
    
    if language_paths:
        # Check lesson counts
        total_lessons, total_expected = check_lessons_per_language(language_paths)
        
        # Check recent activity
        check_recent_activity()
        
        # Overall status
        completion_rate = (total_lessons / total_expected * 100)
        print(f"\n🎯 OVERALL STATUS")
        print("="*50)
        print(f"✅ Paths Created: {len(language_paths)}/21")
        print(f"📚 Lessons Created: {total_lessons}/{total_expected}")
        print(f"📊 Completion Rate: {completion_rate:.1f}%")
        
        if completion_rate >= 80:
            print("🎉 EXCELLENT PROGRESS!")
        elif completion_rate >= 50:
            print("🔄 GOOD PROGRESS, KEEP GOING!")
        elif completion_rate >= 20:
            print("⚠️  EARLY PROGRESS")
        else:
            print("🚀 JUST GETTING STARTED")
    else:
        print("❌ No language paths found. Creator may not have started yet.")

if __name__ == "__main__":
    main() 