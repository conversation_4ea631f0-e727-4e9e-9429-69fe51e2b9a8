#!/usr/bin/env python3
"""
🧪 FIXED ONE LESSON TEST
Creates exactly ONE Tamil lesson with the correct database schema
"""

import os
import json
import requests
import uuid
from datetime import datetime

# API Configuration
GEMINI_API_KEY = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def create_one_lesson_fixed():
    """Create exactly one Tamil lesson with correct schema"""
    print("🧪 FIXED ONE LESSON TEST")
    print("=" * 50)
    
    # Initialize Gemini 2.0 Flash Lite
    try:
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        model = genai.GenerativeModel("gemini-2.0-flash-lite")
        print("✅ Gemini 2.0 Flash Lite initialized")
    except Exception as e:
        print(f"❌ Gemini failed: {e}")
        return False
    
    # Create lesson content
    prompt = """Create a Tamil C2 lesson about advanced literature. Return ONLY valid JSON:

{
    "title": "Advanced Tamil Literature Analysis",
    "description": "Study of classical Tamil poetry",
    "vocabulary": [
        {"word": "இலக்கியம்", "romanization": "ilakkiyam", "pronunciation": "i-lak-ki-yam", "meaning": "literature"},
        {"word": "கவிதை", "romanization": "kavithai", "pronunciation": "ka-vi-thai", "meaning": "poetry"}
    ],
    "conversations": [
        {"speaker": "A", "text": "இந்த கவிதையின் பொருள் என்ன?", "translation": "What is the meaning of this poem?"}
    ],
    "grammar_points": [
        {"rule": "Advanced syntax", "explanation": "Complex structures", "examples": ["example1"], "translations": ["translation1"]}
    ],
    "exercises": [
        {"question": "Which word means literature?", "options": ["இலக்கியம்", "கவிதை", "நூல்", "கதை"], "correct_answer": 0, "explanation": "இலக்கியம் means literature"}
    ]
}"""
    
    print("🔄 Calling Gemini API...")
    try:
        response = model.generate_content(prompt)
        if not response or not response.text:
            print("❌ No response from Gemini")
            return False
        
        print(f"✅ Got response ({len(response.text)} chars)")
        
        # Clean response
        content = response.text.strip()
        if content.startswith('```json'):
            content = content.replace('```json', '').replace('```', '').strip()
        elif content.startswith('```'):
            content = content.replace('```', '').strip()
        
        # Parse JSON
        try:
            lesson_data = json.loads(content)
            print("✅ JSON parsed successfully!")
        except json.JSONDecodeError as e:
            print(f"❌ JSON error: {e}")
            return False
        
        # Create payload with CORRECT schema
        lesson_payload = {
            "path_id": "5ebe8e2d-6752-48f0-a52e-34c6c586ace8",  # Tamil C2
            "title": lesson_data.get("title", "Tamil C2 Literature"),
            "description": lesson_data.get("description", "Advanced Tamil lesson"),
            "lesson_type": "literature",
            "difficulty_level": 6,  # C2 = level 6
            "estimated_duration": 45,
            "sequence_order": 1,
            "learning_objectives": ["Master advanced Tamil literature", "Analyze complex texts"],
            "vocabulary_focus": None,
            "grammar_concepts": ["Advanced syntax", "Literary devices"],
            "cultural_notes": "Classical Tamil literature analysis",
            "prerequisite_lessons": [],
            "content_metadata": {
                "title": lesson_data.get("title", "Tamil C2 Literature"),
                "description": lesson_data.get("description", "Advanced Tamil lesson"),
                "vocabulary": lesson_data.get("vocabulary", []),
                "conversations": lesson_data.get("conversations", []),
                "grammar_points": lesson_data.get("grammar_points", []),
                "exercises": lesson_data.get("exercises", []),
                "estimated_duration": 45,
                "learning_objectives": ["Master advanced Tamil literature", "Analyze complex texts"]
            },
            "is_active": True,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "audio_url": None,
            "audio_metadata": {},
            "has_audio": False
        }
        
        print("💾 Saving to database with correct schema...")
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=headers,
            json=lesson_payload
        )
        
        print(f"🌐 Database response: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("🎉 SUCCESS: Lesson saved to database!")
            print(f"📚 Title: {lesson_data.get('title')}")
            print(f"📝 Vocabulary: {len(lesson_data.get('vocabulary', []))} items")
            print(f"💬 Conversations: {len(lesson_data.get('conversations', []))} items")
            print(f"📚 Grammar: {len(lesson_data.get('grammar_points', []))} items")
            print(f"🎯 Exercises: {len(lesson_data.get('exercises', []))} items")
            return True
        else:
            print(f"❌ Database error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = create_one_lesson_fixed()
    print("\n" + "=" * 50)
    if success:
        print("🎉 LESSON CREATION WORKING!")
        print("✅ Ready to fix main scripts and scale up")
    else:
        print("❌ Still failing - need more investigation") 