#!/usr/bin/env python3
"""
🗄️ TAMIL LESSONS TO EXCEL EXTRACTOR
Extracts complete Tamil lesson structure from Supabase and creates detailed Excel file
"""

import requests
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import os

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil Learning Path IDs
TAMIL_PATHS = {
    'A1': '6b427613-420f-4586-bce8-2773d722f0b4',
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

class TamilLessonsExcelExtractor:
    """Extract Tamil lessons and create comprehensive Excel file"""
    
    def __init__(self):
        self.all_lessons = []
        self.vocabulary_data = []
        self.conversations_data = []
        self.grammar_data = []
        self.exercises_data = []
        self.lesson_summary = []
        
    def fetch_tamil_lessons(self) -> List[Dict[str, Any]]:
        """Fetch all Tamil lessons from all levels"""
        
        print("🔍 Fetching Tamil lessons from Supabase...")
        all_lessons = []
        
        for level, path_id in TAMIL_PATHS.items():
            print(f"  📚 Fetching {level} lessons...")
            
            try:
                response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/lessons",
                    headers=headers,
                    params={
                        'path_id': f'eq.{path_id}',
                        'select': '*',
                        'order': 'sequence_order.asc'
                    }
                )
                
                if response.status_code == 200:
                    lessons = response.json()
                    print(f"    ✅ Found {len(lessons)} {level} lessons")
                    
                    # Add level info to each lesson
                    for lesson in lessons:
                        lesson['level'] = level
                        lesson['path_id'] = path_id
                        all_lessons.append(lesson)
                        
                else:
                    print(f"    ❌ Failed to fetch {level} lessons: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ Error fetching {level} lessons: {e}")
        
        print(f"📊 Total lessons fetched: {len(all_lessons)}")
        return all_lessons
    
    def process_lesson_data(self, lessons: List[Dict[str, Any]]):
        """Process lesson data into structured format for Excel"""
        
        print("🔄 Processing lesson data...")
        
        for lesson in lessons:
            lesson_id = lesson.get('id', '')
            title = lesson.get('title', '')
            level = lesson.get('level', '')
            sequence = lesson.get('sequence_order', 0)
            content = lesson.get('content_metadata', {})
            
            # Lesson Summary
            vocab_count = len(content.get('vocabulary', []))
            conv_count = len(content.get('conversations', []))
            grammar_count = len(content.get('grammar_points', []))
            exercise_count = len(content.get('exercises', []))
            
            self.lesson_summary.append({
                'Lesson_ID': lesson_id,
                'Level': level,
                'Sequence_Order': sequence,
                'Title': title,
                'Description': lesson.get('description', ''),
                'Lesson_Type': lesson.get('lesson_type', ''),
                'Difficulty_Level': lesson.get('difficulty_level', ''),
                'Estimated_Duration': lesson.get('estimated_duration', ''),
                'Vocabulary_Count': vocab_count,
                'Conversations_Count': conv_count,
                'Grammar_Count': grammar_count,
                'Exercises_Count': exercise_count,
                'Has_Audio': lesson.get('has_audio', False),
                'Audio_URL': lesson.get('audio_url', ''),
                'Is_Active': lesson.get('is_active', True),
                'Created_At': lesson.get('created_at', ''),
                'Updated_At': lesson.get('updated_at', '')
            })
            
            # Process Vocabulary
            for i, vocab in enumerate(content.get('vocabulary', []), 1):
                # Extract full example with pronunciation if available
                example_full = vocab.get('example', '')
                example_translation = vocab.get('example_translation', '')

                # If example contains pronunciation in parentheses, extract it
                example_clean = example_full
                example_pronunciation = ''
                if '(' in example_full and ')' in example_full:
                    parts = example_full.split('(')
                    if len(parts) > 1:
                        example_clean = parts[0].strip()
                        pron_part = parts[1].split(')')[0].strip()
                        example_pronunciation = pron_part

                self.vocabulary_data.append({
                    'Lesson_ID': lesson_id,
                    'Level': level,
                    'Lesson_Title': title,
                    'Sequence_Order': sequence,
                    'Vocab_Index': i,
                    'Tamil_Word': vocab.get('word', ''),
                    'English_Translation': vocab.get('translation', ''),
                    'Romanized_Pronunciation': vocab.get('pronunciation', ''),
                    'Tamil_Example_Sentence': example_clean,
                    'Example_Pronunciation': example_pronunciation,
                    'English_Example_Translation': example_translation,
                    'Part_of_Speech': vocab.get('part_of_speech', ''),
                    'Difficulty_Level': vocab.get('difficulty', ''),
                    'Context_Notes': vocab.get('context', ''),
                    'Word_Audio_URL': vocab.get('word_audio_url', ''),
                    'Example_Audio_URL': vocab.get('example_audio_url', ''),
                    'Full_Example_Original': example_full
                })
            
            # Process Conversations
            for i, conv in enumerate(content.get('conversations', []), 1):
                conv_title = conv.get('title', f'Conversation {i}')
                scenario = conv.get('scenario', '')
                difficulty = conv.get('difficulty', 'beginner')

                # If no exchanges, create a single row for the conversation
                exchanges = conv.get('exchanges', [])
                if not exchanges:
                    self.conversations_data.append({
                        'Lesson_ID': lesson_id,
                        'Level': level,
                        'Lesson_Title': title,
                        'Sequence_Order': sequence,
                        'Conversation_Index': i,
                        'Conversation_Title': conv_title,
                        'Scenario_Description': scenario,
                        'Difficulty_Level': difficulty,
                        'Exchange_Index': 0,
                        'Speaker_Name': 'N/A',
                        'Tamil_Text': 'No exchanges found',
                        'English_Translation': 'No exchanges found',
                        'Romanized_Pronunciation': '',
                        'Cultural_Notes': conv.get('cultural_notes', ''),
                        'Audio_URL': '',
                        'Total_Exchanges': 0
                    })
                else:
                    for j, exchange in enumerate(exchanges, 1):
                        self.conversations_data.append({
                            'Lesson_ID': lesson_id,
                            'Level': level,
                            'Lesson_Title': title,
                            'Sequence_Order': sequence,
                            'Conversation_Index': i,
                            'Conversation_Title': conv_title,
                            'Scenario_Description': scenario,
                            'Difficulty_Level': difficulty,
                            'Exchange_Index': j,
                            'Speaker_Name': exchange.get('speaker', f'Speaker {j}'),
                            'Tamil_Text': exchange.get('text', ''),
                            'English_Translation': exchange.get('translation', ''),
                            'Romanized_Pronunciation': exchange.get('pronunciation', ''),
                            'Cultural_Notes': exchange.get('cultural_note', ''),
                            'Audio_URL': exchange.get('audio_url', ''),
                            'Total_Exchanges': len(exchanges)
                        })
            
            # Process Grammar Points
            for i, grammar in enumerate(content.get('grammar_points', []), 1):
                examples = grammar.get('examples', [])

                # Process each example separately for better analysis
                if examples:
                    for j, example in enumerate(examples, 1):
                        # Extract Tamil text and pronunciation if available
                        example_tamil = example
                        example_pronunciation = ''
                        example_translation = ''

                        # Parse example format: "Tamil (pronunciation) - English"
                        if '(' in example and ')' in example and '-' in example:
                            parts = example.split('(')
                            if len(parts) > 1:
                                example_tamil = parts[0].strip()
                                remaining = parts[1]
                                if ')' in remaining:
                                    pron_parts = remaining.split(')')
                                    example_pronunciation = pron_parts[0].strip()
                                    if len(pron_parts) > 1 and '-' in pron_parts[1]:
                                        example_translation = pron_parts[1].split('-', 1)[1].strip()

                        self.grammar_data.append({
                            'Lesson_ID': lesson_id,
                            'Level': level,
                            'Lesson_Title': title,
                            'Sequence_Order': sequence,
                            'Grammar_Index': i,
                            'Grammar_Rule_Title': grammar.get('rule', ''),
                            'Rule_Explanation': grammar.get('explanation', ''),
                            'Example_Number': j,
                            'Tamil_Example': example_tamil,
                            'Example_Pronunciation': example_pronunciation,
                            'English_Translation': example_translation,
                            'Full_Example_Original': example,
                            'Learning_Tips': grammar.get('tips', ''),
                            'Total_Examples': len(examples),
                            'Example_Audio_URL': grammar.get('audio_urls', [])[j-1] if j-1 < len(grammar.get('audio_urls', [])) else ''
                        })
                else:
                    # No examples found
                    self.grammar_data.append({
                        'Lesson_ID': lesson_id,
                        'Level': level,
                        'Lesson_Title': title,
                        'Sequence_Order': sequence,
                        'Grammar_Index': i,
                        'Grammar_Rule_Title': grammar.get('rule', ''),
                        'Rule_Explanation': grammar.get('explanation', ''),
                        'Example_Number': 0,
                        'Tamil_Example': 'No examples found',
                        'Example_Pronunciation': '',
                        'English_Translation': '',
                        'Full_Example_Original': '',
                        'Learning_Tips': grammar.get('tips', ''),
                        'Total_Examples': 0,
                        'Example_Audio_URL': ''
                    })
            
            # Process Exercises
            for i, exercise in enumerate(content.get('exercises', []), 1):
                options = exercise.get('options', [])
                audio_urls = exercise.get('options_audio_urls', [])
                correct_answer_index = exercise.get('correctAnswer', 0)

                # Get correct answer text
                correct_answer_text = ''
                if isinstance(correct_answer_index, int) and 0 <= correct_answer_index < len(options):
                    correct_answer_text = options[correct_answer_index]
                elif isinstance(correct_answer_index, str) and correct_answer_index.isdigit():
                    idx = int(correct_answer_index)
                    if 0 <= idx < len(options):
                        correct_answer_text = options[idx]

                # Process each option separately for detailed analysis
                if options:
                    for j, option in enumerate(options):
                        is_correct = (j == correct_answer_index) or (str(j) == str(correct_answer_index))
                        option_audio = audio_urls[j] if j < len(audio_urls) else ''

                        self.exercises_data.append({
                            'Lesson_ID': lesson_id,
                            'Level': level,
                            'Lesson_Title': title,
                            'Sequence_Order': sequence,
                            'Exercise_Index': i,
                            'Exercise_Type': exercise.get('type', ''),
                            'Question_Text': exercise.get('question', ''),
                            'Option_Number': j + 1,
                            'Option_Text': option,
                            'Is_Correct_Answer': is_correct,
                            'Correct_Answer_Index': correct_answer_index,
                            'Correct_Answer_Text': correct_answer_text,
                            'Answer_Explanation': exercise.get('explanation', ''),
                            'Points_Value': exercise.get('points', 10),
                            'Total_Options': len(options),
                            'Option_Audio_URL': option_audio,
                            'Question_Audio_URL': exercise.get('question_audio_url', ''),
                            'Hints': exercise.get('hints', ''),
                            'Difficulty_Level': exercise.get('difficulty', ''),
                            'Exercise_Instructions': exercise.get('instructions', '')
                        })
                else:
                    # No options found
                    self.exercises_data.append({
                        'Lesson_ID': lesson_id,
                        'Level': level,
                        'Lesson_Title': title,
                        'Sequence_Order': sequence,
                        'Exercise_Index': i,
                        'Exercise_Type': exercise.get('type', ''),
                        'Question_Text': exercise.get('question', ''),
                        'Option_Number': 0,
                        'Option_Text': 'No options found',
                        'Is_Correct_Answer': False,
                        'Correct_Answer_Index': correct_answer_index,
                        'Correct_Answer_Text': correct_answer_text,
                        'Answer_Explanation': exercise.get('explanation', ''),
                        'Points_Value': exercise.get('points', 10),
                        'Total_Options': 0,
                        'Option_Audio_URL': '',
                        'Question_Audio_URL': exercise.get('question_audio_url', ''),
                        'Hints': exercise.get('hints', ''),
                        'Difficulty_Level': exercise.get('difficulty', ''),
                        'Exercise_Instructions': exercise.get('instructions', '')
                    })
    
    def create_excel_file(self):
        """Create comprehensive Excel file with multiple sheets"""
        
        print("📊 Creating Excel file with detailed content...")

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"Tamil_Lessons_FULL_CONTENT_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:

            # Sheet 1: Lesson Summary
            if self.lesson_summary:
                df_summary = pd.DataFrame(self.lesson_summary)
                df_summary.to_excel(writer, sheet_name='Lesson_Summary', index=False)
                print(f"  ✅ Lesson Summary: {len(df_summary)} rows")

            # Sheet 2: Full Vocabulary Content
            if self.vocabulary_data:
                df_vocab = pd.DataFrame(self.vocabulary_data)
                df_vocab.to_excel(writer, sheet_name='Vocabulary_Full_Content', index=False)
                print(f"  ✅ Vocabulary (Full Content): {len(df_vocab)} rows")

            # Sheet 3: Full Conversations Content
            if self.conversations_data:
                df_conv = pd.DataFrame(self.conversations_data)
                df_conv.to_excel(writer, sheet_name='Conversations_Full_Content', index=False)
                print(f"  ✅ Conversations (Full Content): {len(df_conv)} rows")

            # Sheet 4: Full Grammar Content
            if self.grammar_data:
                df_grammar = pd.DataFrame(self.grammar_data)
                df_grammar.to_excel(writer, sheet_name='Grammar_Full_Content', index=False)
                print(f"  ✅ Grammar Points (Full Content): {len(df_grammar)} rows")

            # Sheet 5: Full Exercises Content
            if self.exercises_data:
                df_exercises = pd.DataFrame(self.exercises_data)
                df_exercises.to_excel(writer, sheet_name='Exercises_Full_Content', index=False)
                print(f"  ✅ Exercises (Full Content): {len(df_exercises)} rows")

            # Sheet 6: Statistics by Level
            self.create_statistics_sheet(writer)

            # Sheet 7: Content Quality Analysis
            self.create_content_quality_sheet(writer)
        
        print(f"✅ Excel file created: {filename}")
        return filename
    
    def create_statistics_sheet(self, writer):
        """Create statistics summary sheet"""
        
        stats_data = []
        
        for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
            level_lessons = [l for l in self.lesson_summary if l['Level'] == level]
            
            if level_lessons:
                total_lessons = len(level_lessons)
                total_vocab = sum(l['Vocabulary_Count'] for l in level_lessons)
                total_conv = sum(l['Conversations_Count'] for l in level_lessons)
                total_grammar = sum(l['Grammar_Count'] for l in level_lessons)
                total_exercises = sum(l['Exercises_Count'] for l in level_lessons)
                lessons_with_audio = sum(1 for l in level_lessons if l['Has_Audio'])
                
                stats_data.append({
                    'Level': level,
                    'Total_Lessons': total_lessons,
                    'Total_Vocabulary': total_vocab,
                    'Total_Conversations': total_conv,
                    'Total_Grammar_Points': total_grammar,
                    'Total_Exercises': total_exercises,
                    'Lessons_With_Audio': lessons_with_audio,
                    'Audio_Percentage': f"{(lessons_with_audio/total_lessons*100):.1f}%" if total_lessons > 0 else "0%",
                    'Avg_Vocab_Per_Lesson': f"{total_vocab/total_lessons:.1f}" if total_lessons > 0 else "0",
                    'Avg_Conv_Per_Lesson': f"{total_conv/total_lessons:.1f}" if total_lessons > 0 else "0"
                })
        
        if stats_data:
            df_stats = pd.DataFrame(stats_data)
            df_stats.to_excel(writer, sheet_name='Statistics_by_Level', index=False)
            print(f"  ✅ Statistics: {len(df_stats)} levels")

    def create_content_quality_sheet(self, writer):
        """Create content quality analysis sheet"""

        quality_data = []

        # Analyze vocabulary quality
        vocab_with_pronunciation = sum(1 for v in self.vocabulary_data if v.get('Romanized_Pronunciation', ''))
        vocab_with_examples = sum(1 for v in self.vocabulary_data if v.get('Tamil_Example_Sentence', ''))
        vocab_with_audio = sum(1 for v in self.vocabulary_data if v.get('Word_Audio_URL', ''))

        # Analyze conversation quality
        conv_with_pronunciation = sum(1 for c in self.conversations_data if c.get('Romanized_Pronunciation', ''))
        conv_with_audio = sum(1 for c in self.conversations_data if c.get('Audio_URL', ''))

        # Analyze grammar quality
        grammar_with_examples = sum(1 for g in self.grammar_data if g.get('Tamil_Example', '') and g.get('Tamil_Example', '') != 'No examples found')
        grammar_with_audio = sum(1 for g in self.grammar_data if g.get('Example_Audio_URL', ''))

        # Analyze exercise quality
        exercises_with_audio = sum(1 for e in self.exercises_data if e.get('Option_Audio_URL', ''))
        exercises_with_explanations = sum(1 for e in self.exercises_data if e.get('Answer_Explanation', ''))

        quality_data = [
            {
                'Content_Type': 'Vocabulary',
                'Total_Items': len(self.vocabulary_data),
                'Items_with_Pronunciation': vocab_with_pronunciation,
                'Pronunciation_Percentage': f"{(vocab_with_pronunciation/len(self.vocabulary_data)*100):.1f}%" if self.vocabulary_data else "0%",
                'Items_with_Examples': vocab_with_examples,
                'Examples_Percentage': f"{(vocab_with_examples/len(self.vocabulary_data)*100):.1f}%" if self.vocabulary_data else "0%",
                'Items_with_Audio': vocab_with_audio,
                'Audio_Percentage': f"{(vocab_with_audio/len(self.vocabulary_data)*100):.1f}%" if self.vocabulary_data else "0%"
            },
            {
                'Content_Type': 'Conversations',
                'Total_Items': len(self.conversations_data),
                'Items_with_Pronunciation': conv_with_pronunciation,
                'Pronunciation_Percentage': f"{(conv_with_pronunciation/len(self.conversations_data)*100):.1f}%" if self.conversations_data else "0%",
                'Items_with_Examples': 'N/A',
                'Examples_Percentage': 'N/A',
                'Items_with_Audio': conv_with_audio,
                'Audio_Percentage': f"{(conv_with_audio/len(self.conversations_data)*100):.1f}%" if self.conversations_data else "0%"
            },
            {
                'Content_Type': 'Grammar Points',
                'Total_Items': len(self.grammar_data),
                'Items_with_Pronunciation': 'N/A',
                'Pronunciation_Percentage': 'N/A',
                'Items_with_Examples': grammar_with_examples,
                'Examples_Percentage': f"{(grammar_with_examples/len(self.grammar_data)*100):.1f}%" if self.grammar_data else "0%",
                'Items_with_Audio': grammar_with_audio,
                'Audio_Percentage': f"{(grammar_with_audio/len(self.grammar_data)*100):.1f}%" if self.grammar_data else "0%"
            },
            {
                'Content_Type': 'Exercises',
                'Total_Items': len(self.exercises_data),
                'Items_with_Pronunciation': 'N/A',
                'Pronunciation_Percentage': 'N/A',
                'Items_with_Examples': exercises_with_explanations,
                'Examples_Percentage': f"{(exercises_with_explanations/len(self.exercises_data)*100):.1f}%" if self.exercises_data else "0%",
                'Items_with_Audio': exercises_with_audio,
                'Audio_Percentage': f"{(exercises_with_audio/len(self.exercises_data)*100):.1f}%" if self.exercises_data else "0%"
            }
        ]

        if quality_data:
            df_quality = pd.DataFrame(quality_data)
            df_quality.to_excel(writer, sheet_name='Content_Quality_Analysis', index=False)
            print(f"  ✅ Content Quality Analysis: {len(df_quality)} content types")
    
    def run_extraction(self):
        """Run complete extraction process"""
        
        print("🚀 STARTING TAMIL LESSONS EXCEL EXTRACTION")
        print("=" * 60)
        
        # 1. Fetch all lessons
        lessons = self.fetch_tamil_lessons()
        
        if not lessons:
            print("❌ No lessons found. Exiting.")
            return None
        
        # 2. Process lesson data
        self.process_lesson_data(lessons)
        
        # 3. Create Excel file
        filename = self.create_excel_file()
        
        # 4. Print detailed summary
        print("\n📊 DETAILED EXTRACTION SUMMARY:")
        print("=" * 50)
        print(f"📚 Total Lessons: {len(self.lesson_summary)}")
        print(f"📝 Total Vocabulary Items: {len(self.vocabulary_data)}")
        print(f"💬 Total Conversation Exchanges: {len(self.conversations_data)}")
        print(f"📖 Total Grammar Examples: {len(self.grammar_data)}")
        print(f"🧩 Total Exercise Options: {len(self.exercises_data)}")
        print(f"📁 Excel File: {filename}")

        # Content breakdown by level
        print(f"\n📊 CONTENT BREAKDOWN BY LEVEL:")
        print("-" * 40)
        for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
            level_lessons = [l for l in self.lesson_summary if l['Level'] == level]
            if level_lessons:
                level_vocab = [v for v in self.vocabulary_data if v['Level'] == level]
                level_conv = [c for c in self.conversations_data if c['Level'] == level]
                level_grammar = [g for g in self.grammar_data if g['Level'] == level]
                level_exercises = [e for e in self.exercises_data if e['Level'] == level]

                print(f"{level}: {len(level_lessons)} lessons, {len(level_vocab)} vocab, {len(level_conv)} conversations, {len(level_grammar)} grammar, {len(level_exercises)} exercises")

        print(f"\n🎯 EXCEL STRUCTURE:")
        print("-" * 30)
        print("📋 Sheet 1: Lesson_Summary - Overview of all lessons")
        print("📝 Sheet 2: Vocabulary_Full_Content - Complete vocabulary with Tamil text, pronunciations, examples")
        print("💬 Sheet 3: Conversations_Full_Content - Complete conversations with speakers, scenarios, pronunciations")
        print("📖 Sheet 4: Grammar_Full_Content - Complete grammar rules with examples and explanations")
        print("🧩 Sheet 5: Exercises_Full_Content - Complete exercises with all options, correct answers, explanations")
        print("📊 Sheet 6: Statistics_by_Level - Summary statistics per CEFR level")
        print("🔍 Sheet 7: Content_Quality_Analysis - Quality metrics and completion percentages")

        return filename

if __name__ == "__main__":
    extractor = TamilLessonsExcelExtractor()
    result = extractor.run_extraction()

    if result:
        print(f"\n🎉 SUCCESS! Complete Tamil lesson content exported to: {result}")
        print("📁 File contains 7 comprehensive sheets with FULL lesson content including:")
        print("   • Complete Tamil text with romanized pronunciations")
        print("   • Full English translations for all content")
        print("   • Detailed conversation exchanges with speakers")
        print("   • Complete grammar examples with explanations")
        print("   • Full exercise options with correct answers")
        print("   • Audio URL mappings for all content types")
        print("   • Quality analysis and statistics")
    else:
        print("\n❌ FAILED! Could not extract Tamil lessons")
