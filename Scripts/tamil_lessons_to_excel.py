#!/usr/bin/env python3
"""
🗄️ TAMIL LESSONS TO EXCEL EXTRACTOR
Extracts complete Tamil lesson structure from Supabase and creates detailed Excel file
"""

import requests
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import os

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil Learning Path IDs
TAMIL_PATHS = {
    'A1': '6b427613-420f-4586-bce8-2773d722f0b4',
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

class TamilLessonsExcelExtractor:
    """Extract Tamil lessons and create comprehensive Excel file"""
    
    def __init__(self):
        self.all_lessons = []
        self.vocabulary_data = []
        self.conversations_data = []
        self.grammar_data = []
        self.exercises_data = []
        self.lesson_summary = []
        
    def fetch_tamil_lessons(self) -> List[Dict[str, Any]]:
        """Fetch all Tamil lessons from all levels"""
        
        print("🔍 Fetching Tamil lessons from Supabase...")
        all_lessons = []
        
        for level, path_id in TAMIL_PATHS.items():
            print(f"  📚 Fetching {level} lessons...")
            
            try:
                response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/lessons",
                    headers=headers,
                    params={
                        'path_id': f'eq.{path_id}',
                        'select': '*',
                        'order': 'sequence_order.asc'
                    }
                )
                
                if response.status_code == 200:
                    lessons = response.json()
                    print(f"    ✅ Found {len(lessons)} {level} lessons")
                    
                    # Add level info to each lesson
                    for lesson in lessons:
                        lesson['level'] = level
                        lesson['path_id'] = path_id
                        all_lessons.append(lesson)
                        
                else:
                    print(f"    ❌ Failed to fetch {level} lessons: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ Error fetching {level} lessons: {e}")
        
        print(f"📊 Total lessons fetched: {len(all_lessons)}")
        return all_lessons
    
    def process_lesson_data(self, lessons: List[Dict[str, Any]]):
        """Process lesson data into structured format for Excel"""
        
        print("🔄 Processing lesson data...")
        
        for lesson in lessons:
            lesson_id = lesson.get('id', '')
            title = lesson.get('title', '')
            level = lesson.get('level', '')
            sequence = lesson.get('sequence_order', 0)
            content = lesson.get('content_metadata', {})
            
            # Lesson Summary
            vocab_count = len(content.get('vocabulary', []))
            conv_count = len(content.get('conversations', []))
            grammar_count = len(content.get('grammar_points', []))
            exercise_count = len(content.get('exercises', []))
            
            self.lesson_summary.append({
                'Lesson_ID': lesson_id,
                'Level': level,
                'Sequence_Order': sequence,
                'Title': title,
                'Description': lesson.get('description', ''),
                'Lesson_Type': lesson.get('lesson_type', ''),
                'Difficulty_Level': lesson.get('difficulty_level', ''),
                'Estimated_Duration': lesson.get('estimated_duration', ''),
                'Vocabulary_Count': vocab_count,
                'Conversations_Count': conv_count,
                'Grammar_Count': grammar_count,
                'Exercises_Count': exercise_count,
                'Has_Audio': lesson.get('has_audio', False),
                'Audio_URL': lesson.get('audio_url', ''),
                'Is_Active': lesson.get('is_active', True),
                'Created_At': lesson.get('created_at', ''),
                'Updated_At': lesson.get('updated_at', '')
            })
            
            # Process Vocabulary
            for i, vocab in enumerate(content.get('vocabulary', []), 1):
                self.vocabulary_data.append({
                    'Lesson_ID': lesson_id,
                    'Level': level,
                    'Lesson_Title': title,
                    'Vocab_Index': i,
                    'Word': vocab.get('word', ''),
                    'Translation': vocab.get('translation', ''),
                    'Pronunciation': vocab.get('pronunciation', ''),
                    'Example': vocab.get('example', ''),
                    'Example_Translation': vocab.get('example_translation', ''),
                    'Part_of_Speech': vocab.get('part_of_speech', ''),
                    'Difficulty': vocab.get('difficulty', ''),
                    'Word_Audio_URL': vocab.get('word_audio_url', ''),
                    'Example_Audio_URL': vocab.get('example_audio_url', '')
                })
            
            # Process Conversations
            for i, conv in enumerate(content.get('conversations', []), 1):
                conv_title = conv.get('title', f'Conversation {i}')
                scenario = conv.get('scenario', '')
                
                for j, exchange in enumerate(conv.get('exchanges', []), 1):
                    self.conversations_data.append({
                        'Lesson_ID': lesson_id,
                        'Level': level,
                        'Lesson_Title': title,
                        'Conversation_Index': i,
                        'Conversation_Title': conv_title,
                        'Scenario': scenario,
                        'Exchange_Index': j,
                        'Speaker': exchange.get('speaker', ''),
                        'Text': exchange.get('text', ''),
                        'Translation': exchange.get('translation', ''),
                        'Pronunciation': exchange.get('pronunciation', ''),
                        'Audio_URL': exchange.get('audio_url', '')
                    })
            
            # Process Grammar Points
            for i, grammar in enumerate(content.get('grammar_points', []), 1):
                examples_text = ' | '.join(grammar.get('examples', []))
                
                self.grammar_data.append({
                    'Lesson_ID': lesson_id,
                    'Level': level,
                    'Lesson_Title': title,
                    'Grammar_Index': i,
                    'Rule': grammar.get('rule', ''),
                    'Explanation': grammar.get('explanation', ''),
                    'Examples': examples_text,
                    'Tips': grammar.get('tips', ''),
                    'Audio_URLs': ' | '.join(grammar.get('audio_urls', []))
                })
            
            # Process Exercises
            for i, exercise in enumerate(content.get('exercises', []), 1):
                options_text = ' | '.join(exercise.get('options', []))
                audio_urls = ' | '.join(exercise.get('options_audio_urls', []))
                
                self.exercises_data.append({
                    'Lesson_ID': lesson_id,
                    'Level': level,
                    'Lesson_Title': title,
                    'Exercise_Index': i,
                    'Type': exercise.get('type', ''),
                    'Question': exercise.get('question', ''),
                    'Options': options_text,
                    'Correct_Answer': exercise.get('correctAnswer', ''),
                    'Explanation': exercise.get('explanation', ''),
                    'Points': exercise.get('points', ''),
                    'Options_Audio_URLs': audio_urls
                })
    
    def create_excel_file(self):
        """Create comprehensive Excel file with multiple sheets"""
        
        print("📊 Creating Excel file...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"Tamil_Lessons_Complete_Structure_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # Sheet 1: Lesson Summary
            if self.lesson_summary:
                df_summary = pd.DataFrame(self.lesson_summary)
                df_summary.to_excel(writer, sheet_name='Lesson_Summary', index=False)
                print(f"  ✅ Lesson Summary: {len(df_summary)} rows")
            
            # Sheet 2: Vocabulary
            if self.vocabulary_data:
                df_vocab = pd.DataFrame(self.vocabulary_data)
                df_vocab.to_excel(writer, sheet_name='Vocabulary', index=False)
                print(f"  ✅ Vocabulary: {len(df_vocab)} rows")
            
            # Sheet 3: Conversations
            if self.conversations_data:
                df_conv = pd.DataFrame(self.conversations_data)
                df_conv.to_excel(writer, sheet_name='Conversations', index=False)
                print(f"  ✅ Conversations: {len(df_conv)} rows")
            
            # Sheet 4: Grammar Points
            if self.grammar_data:
                df_grammar = pd.DataFrame(self.grammar_data)
                df_grammar.to_excel(writer, sheet_name='Grammar_Points', index=False)
                print(f"  ✅ Grammar Points: {len(df_grammar)} rows")
            
            # Sheet 5: Exercises
            if self.exercises_data:
                df_exercises = pd.DataFrame(self.exercises_data)
                df_exercises.to_excel(writer, sheet_name='Exercises', index=False)
                print(f"  ✅ Exercises: {len(df_exercises)} rows")
            
            # Sheet 6: Statistics by Level
            self.create_statistics_sheet(writer)
        
        print(f"✅ Excel file created: {filename}")
        return filename
    
    def create_statistics_sheet(self, writer):
        """Create statistics summary sheet"""
        
        stats_data = []
        
        for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
            level_lessons = [l for l in self.lesson_summary if l['Level'] == level]
            
            if level_lessons:
                total_lessons = len(level_lessons)
                total_vocab = sum(l['Vocabulary_Count'] for l in level_lessons)
                total_conv = sum(l['Conversations_Count'] for l in level_lessons)
                total_grammar = sum(l['Grammar_Count'] for l in level_lessons)
                total_exercises = sum(l['Exercises_Count'] for l in level_lessons)
                lessons_with_audio = sum(1 for l in level_lessons if l['Has_Audio'])
                
                stats_data.append({
                    'Level': level,
                    'Total_Lessons': total_lessons,
                    'Total_Vocabulary': total_vocab,
                    'Total_Conversations': total_conv,
                    'Total_Grammar_Points': total_grammar,
                    'Total_Exercises': total_exercises,
                    'Lessons_With_Audio': lessons_with_audio,
                    'Audio_Percentage': f"{(lessons_with_audio/total_lessons*100):.1f}%" if total_lessons > 0 else "0%",
                    'Avg_Vocab_Per_Lesson': f"{total_vocab/total_lessons:.1f}" if total_lessons > 0 else "0",
                    'Avg_Conv_Per_Lesson': f"{total_conv/total_lessons:.1f}" if total_lessons > 0 else "0"
                })
        
        if stats_data:
            df_stats = pd.DataFrame(stats_data)
            df_stats.to_excel(writer, sheet_name='Statistics_by_Level', index=False)
            print(f"  ✅ Statistics: {len(df_stats)} levels")
    
    def run_extraction(self):
        """Run complete extraction process"""
        
        print("🚀 STARTING TAMIL LESSONS EXCEL EXTRACTION")
        print("=" * 60)
        
        # 1. Fetch all lessons
        lessons = self.fetch_tamil_lessons()
        
        if not lessons:
            print("❌ No lessons found. Exiting.")
            return None
        
        # 2. Process lesson data
        self.process_lesson_data(lessons)
        
        # 3. Create Excel file
        filename = self.create_excel_file()
        
        # 4. Print summary
        print("\n📊 EXTRACTION SUMMARY:")
        print("-" * 40)
        print(f"Total Lessons: {len(self.lesson_summary)}")
        print(f"Total Vocabulary Items: {len(self.vocabulary_data)}")
        print(f"Total Conversation Exchanges: {len(self.conversations_data)}")
        print(f"Total Grammar Points: {len(self.grammar_data)}")
        print(f"Total Exercises: {len(self.exercises_data)}")
        print(f"Excel File: {filename}")
        
        return filename

if __name__ == "__main__":
    extractor = TamilLessonsExcelExtractor()
    result = extractor.run_extraction()
    
    if result:
        print(f"\n🎉 SUCCESS! Tamil lessons exported to: {result}")
        print("📁 File contains 6 sheets with complete lesson structure")
    else:
        print("\n❌ FAILED! Could not extract Tamil lessons")
