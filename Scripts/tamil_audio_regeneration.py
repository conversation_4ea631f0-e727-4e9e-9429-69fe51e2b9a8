#!/usr/bin/env python3
"""
Tamil Audio Regeneration with ElevenLabs
Generates proper Tamil audio using approved voices
"""

import requests
import json
import time
import base64

# Configuration
ELEVENLABS_API_KEY = "sk_b3f4c8c9c8a4b8f4e8d9c8b4a8f4e8d9c8b4a8f4e8d9c8b4a8f4e8d9c8b4a8f4"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Approved voices for Tamil (using voice IDs that work with multilingual model)
APPROVED_VOICES = {
    'freya': 'jsCqWAovK2LkecY7zXl4',  # Freya
    'elli': 'MF3mGyEYCl7XYWbV9V6O',   # Elli  
    'arnold': 'VR6AewLTigWG4xSOukaG', # Arnold
    'sam': 'yoZ06aMxZJJ28mfd3POQ'     # Sam
}

def generate_tamil_audio(text, voice_name='freya'):
    """Generate Tamil audio using ElevenLabs"""
    
    voice_id = APPROVED_VOICES.get(voice_name, APPROVED_VOICES['freya'])
    
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
    
    headers = {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': ELEVENLABS_API_KEY
    }
    
    data = {
        'text': text,
        'model_id': 'eleven_multilingual_v2',
        'voice_settings': {
            'stability': 0.5,
            'similarity_boost': 0.5,
            'style': 0.0,
            'use_speaker_boost': True
        }
    }
    
    try:
        print(f"🎵 Generating audio for: {text[:30]}...")
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            print(f"✅ Audio generated successfully")
            return response.content
        else:
            print(f"❌ ElevenLabs API error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Audio generation error: {e}")
        return None

def upload_audio_to_supabase(audio_data, file_path):
    """Upload audio to Supabase storage"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'audio/mpeg',
        'Cache-Control': '3600'
    }
    
    try:
        # Upload to Supabase storage
        upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{file_path}"
        
        response = requests.post(upload_url, data=audio_data, headers=headers)
        
        if response.status_code in [200, 201]:
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{file_path}"
            print(f"✅ Uploaded: {public_url}")
            return public_url
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None

def regenerate_tamil_a1_audio():
    """Regenerate audio for Tamil A1 lessons"""
    
    print("🎵 TAMIL A1 AUDIO REGENERATION")
    print("Using approved voices: Freya, Elli, Arnold, Sam")
    print("=" * 50)
    
    # Get Tamil A1 lessons
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    # Find Tamil A1 lessons
    query = """
    SELECT l.id, l.title, l.sequence_order, l.content_metadata
    FROM lessons l 
    JOIN learning_paths lp ON l.path_id = lp.id 
    WHERE lp.name LIKE '%Tamil%A1%' 
    ORDER BY l.sequence_order 
    LIMIT 3
    """
    
    response = requests.post(
        f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
        headers=headers,
        json={'query': query}
    )
    
    if response.status_code != 200:
        print(f"❌ Failed to fetch lessons: {response.status_code}")
        return
    
    lessons = response.json()
    print(f"📚 Found {len(lessons)} Tamil A1 lessons")
    
    for lesson in lessons:
        print(f"\n🔄 Processing: {lesson['title']}")
        
        content = lesson.get('content_metadata', {})
        vocabulary = content.get('vocabulary', [])
        
        if not vocabulary:
            print("  ⚠️  No vocabulary found")
            continue
        
        lesson_slug = lesson['title'].lower().replace(' ', '_').replace(':', '').replace(',', '')
        audio_urls = {}
        
        # Generate audio for first 3 vocabulary items
        for i, vocab in enumerate(vocabulary[:3]):
            word = vocab.get('word', '')
            example = vocab.get('example', '')
            
            if word:
                print(f"  🎵 Processing vocab {i+1}: {word}")
                
                # Generate word audio (female voice)
                word_audio = generate_tamil_audio(word, 'freya')
                if word_audio:
                    file_path = f"tamil/a1/{lesson_slug}/vocab_{i+1:02d}_word.mp3"
                    word_url = upload_audio_to_supabase(word_audio, file_path)
                    if word_url:
                        audio_urls[f'vocab_{i+1:02d}_word'] = word_url
                
                # Generate example audio (male voice)
                if example:
                    example_audio = generate_tamil_audio(example, 'arnold')
                    if example_audio:
                        file_path = f"tamil/a1/{lesson_slug}/vocab_{i+1:02d}_example.mp3"
                        example_url = upload_audio_to_supabase(example_audio, file_path)
                        if example_url:
                            audio_urls[f'vocab_{i+1:02d}_example'] = example_url
                
                time.sleep(3)  # Rate limiting
        
        # Update lesson with new audio URLs
        if audio_urls:
            audio_urls_json = json.dumps(audio_urls)
            update_query = f"""
            UPDATE lessons 
            SET content_metadata = content_metadata || '{{"audio_urls": {audio_urls_json}}}'::jsonb,
                has_audio = true
            WHERE id = '{lesson['id']}'
            """
            
            update_response = requests.post(
                f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
                headers=headers,
                json={'query': update_query}
            )
            
            if update_response.status_code == 200:
                print(f"  ✅ Updated lesson with {len(audio_urls)} audio URLs")
            else:
                print(f"  ❌ Failed to update lesson")
        
        print(f"  🎉 Completed: {lesson['title']}")

if __name__ == "__main__":
    regenerate_tamil_a1_audio()
