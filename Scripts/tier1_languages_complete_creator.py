#!/usr/bin/env python3
"""
🌍 TIER 1 LANGUAGES COMPLETE CREATOR
Scales Tamil lesson system to all 21 Tier 1 languages (A1-C2)

Based on:
- COMPREHENSIVE_QUALITY_CHECKLIST.md
- Complete_Lesson_Implementation_Guide.md  
- MULTI_LEVEL_CONTENT_CREATION_GUIDE.md

Creates 2,730 lessons total (21 languages × 130 lessons each)
Enhanced with OpenAI backup for rate limit resilience
"""

import os
import json
import requests
import time
import uuid
from typing import Dict, List, Any, Tuple
from datetime import datetime
import random
import concurrent.futures
from threading import Lock

# API Configurations
os.environ['GEMINI_API_KEY'] = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'
OPENAI_API_KEY = "***********************************************************************************************************************************************************************"

# Initialize Gemini
try:
    import google.generativeai as genai
    genai.configure(api_key=os.environ['GEMINI_API_KEY'])
    model = genai.GenerativeModel('gemini-2.0-flash-exp')
    print("✅ Gemini Flash 2.0 Exp initialized successfully")
    GEMINI_AVAILABLE = True
except Exception as e:
    print(f"⚠️  Gemini initialization failed: {e}")
    GEMINI_AVAILABLE = False

# OpenAI headers for backup
OPENAI_HEADERS = {
    'Authorization': f'Bearer {OPENAI_API_KEY}',
    'Content-Type': 'application/json'
}

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Thread-safe counters
api_call_lock = Lock()
api_call_count = 0
gemini_failures = 0
openai_calls = 0

# Tier 1 Languages (21 total)
TIER1_LANGUAGES = {
    # European (8)
    'english': {'name': 'English', 'code': 'en', 'family': 'Germanic'},
    'spanish': {'name': 'Spanish', 'code': 'es', 'family': 'Romance'}, 
    'french': {'name': 'French', 'code': 'fr', 'family': 'Romance'},
    'german': {'name': 'German', 'code': 'de', 'family': 'Germanic'},
    'italian': {'name': 'Italian', 'code': 'it', 'family': 'Romance'},
    'portuguese': {'name': 'Portuguese', 'code': 'pt', 'family': 'Romance'},
    'dutch': {'name': 'Dutch', 'code': 'nl', 'family': 'Germanic'},
    'russian': {'name': 'Russian', 'code': 'ru', 'family': 'Slavic'},
    
    # Asian (3)
    'chinese': {'name': 'Chinese', 'code': 'zh', 'family': 'Sino-Tibetan'},
    'japanese': {'name': 'Japanese', 'code': 'ja', 'family': 'Japonic'},
    'korean': {'name': 'Korean', 'code': 'ko', 'family': 'Koreanic'},
    
    # Middle Eastern (1)
    'arabic': {'name': 'Arabic', 'code': 'ar', 'family': 'Semitic'},
    
    # Indian (9)
    'hindi': {'name': 'Hindi', 'code': 'hi', 'family': 'Indo-Aryan'},
    'tamil': {'name': 'Tamil', 'code': 'ta', 'family': 'Dravidian'},
    'telugu': {'name': 'Telugu', 'code': 'te', 'family': 'Dravidian'},
    'kannada': {'name': 'Kannada', 'code': 'kn', 'family': 'Dravidian'},
    'malayalam': {'name': 'Malayalam', 'code': 'ml', 'family': 'Dravidian'},
    'bengali': {'name': 'Bengali', 'code': 'bn', 'family': 'Indo-Aryan'},
    'marathi': {'name': 'Marathi', 'code': 'mr', 'family': 'Indo-Aryan'},
    'punjabi': {'name': 'Punjabi', 'code': 'pa', 'family': 'Indo-Aryan'},
    'gujarati': {'name': 'Gujarati', 'code': 'gu', 'family': 'Indo-Aryan'}
}

# Level structure (130 lessons per language)
LEVEL_STRUCTURE = {
    'A1': {'count': 30, 'difficulty': 1},
    'A2': {'count': 30, 'difficulty': 2},
    'B1': {'count': 20, 'difficulty': 3},
    'B2': {'count': 20, 'difficulty': 4}, 
    'C1': {'count': 15, 'difficulty': 5},
    'C2': {'count': 15, 'difficulty': 6}
}

# Content requirements per lesson
CONTENT_REQUIREMENTS = {
    'A1': {'vocabulary': 25, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'A2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'B1': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'B2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'C1': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'C2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15}
}

class Tier1LanguageCreator:
    """Scalable creator for all Tier 1 languages A1-C2"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        self.created_paths = {}
        self.created_lessons = {}
        
    def call_openai_backup(self, prompt: str, language: str = "unknown") -> str:
        """OpenAI backup for rate-limited Gemini calls"""
        global openai_calls
        
        with api_call_lock:
            openai_calls += 1
        
        print(f"    🔄 [{language}] Using OpenAI backup (call #{openai_calls})")
        
        # Use GPT-4o-mini for cost efficiency
        data = {
            "model": "gpt-4o-mini",
            "messages": [
                {"role": "system", "content": "You are an expert language learning content creator."},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 4000,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=OPENAI_HEADERS,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
            else:
                print(f"    ❌ OpenAI error {response.status_code}: {response.text}")
                raise Exception(f"OpenAI API error: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ OpenAI backup failed: {e}")
            raise e
    
    def call_gemini_safe(self, prompt: str, language: str = "unknown") -> str:
        """Thread-safe AI API calls with Gemini primary, OpenAI backup"""
        global api_call_count, gemini_failures
        
        with api_call_lock:
            api_call_count += 1
            current_count = api_call_count
        
        # Reduced adaptive delay
        base_delay = 3 + (current_count % 5) * 1  # 3-8 seconds instead of 8-28
        delay = base_delay + random.uniform(0, 2)
        
        print(f"    🕐 [{language}] API call #{current_count}, delay: {delay:.1f}s")
        time.sleep(delay)
        
        # Try Gemini first
        if GEMINI_AVAILABLE:
            try:
                response = model.generate_content(prompt)
                return response.text
            except Exception as e:
                error_str = str(e).lower()
                if "quota" in error_str or "429" in error_str or "rate" in error_str:
                    with api_call_lock:
                        gemini_failures += 1
                    
                    print(f"    ⚠️  [{language}] Gemini rate limited (failure #{gemini_failures}) - switching to OpenAI")
                    return self.call_openai_backup(prompt, language)
                else:
                    print(f"    ❌ [{language}] Gemini error: {e}")
                    print(f"    🔄 [{language}] Trying OpenAI backup...")
                    return self.call_openai_backup(prompt, language)
        else:
            # Gemini not available, use OpenAI directly
            print(f"    🔄 [{language}] Gemini unavailable, using OpenAI")
            return self.call_openai_backup(prompt, language)
    
    def create_learning_path(self, language: str, language_info: Dict[str, str]) -> str:
        """Create learning path for a language"""
        print(f"  📁 Creating learning path for {language_info['name']}")
        
        path_data = {
            'id': str(uuid.uuid4()),
            'language_id': str(uuid.uuid4()),  # Create language entry if needed
            'agent_id': str(uuid.uuid4()),     # Create agent entry if needed
            'name': f"{language_info['name']} Complete Course (A1-C2)",
            'description': f"Complete {language_info['name']} language learning path from beginner (A1) to proficiency (C2)",
            'level': 'A1-C2',
            'estimated_hours': 300,  # Total hours across all levels
            'sequence_order': 1,
            'prerequisites': [],
            'learning_objectives': [
                f"Master {language_info['name']} from beginner to advanced level",
                "Develop speaking, listening, reading, and writing skills",
                "Understand cultural context and authentic usage",
                "Achieve CEFR C2 proficiency"
            ],
            'cultural_focus': [f"{language_info['name']} culture", "Authentic contexts", "Real-world applications"],
            'is_active': True,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # Create learning path
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/learning_paths",
            headers=self.headers,
            json=path_data
        )
        
        if response.status_code in [201, 200]:
            print(f"    ✅ Created learning path for {language_info['name']}")
            return path_data['id']
        else:
            print(f"    ❌ Failed to create path for {language}: {response.status_code}")
            return None
    
    def generate_lesson_titles(self, language: str, language_info: Dict[str, str], level: str, count: int) -> List[str]:
        """Generate lesson titles for a specific level"""
        print(f"    📝 Generating {count} lesson titles for {language_info['name']} {level}")
        
        prompt = f"""
        Generate exactly {count} unique lesson titles for {language_info['name']} language learning at {level} level.
        
        Requirements:
        - All titles in English
        - Progressive difficulty appropriate for {level} level
        - Culturally authentic to {language_info['name']} speakers
        - Cover diverse topics: daily life, culture, work, education, travel, etc.
        - No duplicates
        - Each title should be educational and engaging
        
        Format: Return only the titles, one per line, numbered 1-{count}.
        
        Example format:
        1. Daily Routine: From Morning to Night
        2. Family Traditions: Celebrating Together
        3. Shopping: At the Local Market
        """
        
        try:
            response = self.call_gemini_safe(prompt, f"{language}-{level}")
            
            # Parse titles
            titles = []
            for line in response.strip().split('\n'):
                line = line.strip()
                if line and len(line) > 3:  # Valid title
                    # Remove numbering if present
                    if '. ' in line:
                        title = line.split('. ', 1)[1]
                    else:
                        title = line
                    titles.append(title.strip())
            
            if len(titles) >= count:
                return titles[:count]
            else:
                print(f"    ⚠️  Only got {len(titles)} titles, needed {count}")
                return titles
                
        except Exception as e:
            print(f"    ❌ Failed to generate titles for {language} {level}: {e}")
            return []
    
    def create_lesson_structure(self, path_id: str, language: str, language_info: Dict[str, str], 
                              level: str, title: str, sequence_order: int) -> str:
        """Create basic lesson structure"""
        lesson_data = {
            'id': str(uuid.uuid4()),
            'path_id': path_id,
            'title': title,
            'description': f"{level} level {language_info['name']} lesson: {title}",
            'lesson_type': 'interactive',
            'difficulty_level': LEVEL_STRUCTURE[level]['difficulty'],
            'estimated_duration': 45,  # 45 minutes per lesson
            'sequence_order': sequence_order,
            'learning_objectives': [
                f"Learn {language_info['name']} vocabulary related to {title.lower()}",
                f"Practice {language_info['name']} conversations",
                f"Understand {language_info['name']} grammar concepts",
                "Apply knowledge through exercises"
            ],
            'vocabulary_focus': [title.lower().replace(':', '').split()[0]],
            'grammar_concepts': [f"{level} level grammar"],
            'cultural_notes': f"Cultural context relevant to {language_info['name']} speakers",
            'prerequisite_lessons': [],
            'content_metadata': {},  # Will be populated later
            'is_active': True,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'audio_url': None,
            'audio_metadata': {},
            'has_audio': False
        }
        
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            json=lesson_data
        )
        
        if response.status_code in [201, 200]:
            return lesson_data['id']
        else:
            print(f"    ❌ Failed to create lesson '{title}' for {language}: {response.status_code}")
            return None
    
    def generate_lesson_content(self, lesson_id: str, language: str, language_info: Dict[str, str], 
                               level: str, title: str) -> bool:
        """Generate complete lesson content"""
        print(f"      🔄 Generating content for: {title}")
        
        requirements = CONTENT_REQUIREMENTS[level]
        
        prompt = f"""
        Generate complete {language_info['name']} lesson content for: "{title}" (Level {level})
        
        Requirements:
        - {requirements['vocabulary']} vocabulary items with {language_info['name']} words, English translations, pronunciations, and examples
        - {requirements['conversations']} conversations with {language_info['name']} exchanges, English translations, and pronunciations  
        - {requirements['grammar']} grammar points with rules, explanations, and examples
        - {requirements['exercises']} exercises (multiple choice, fill-in-blank, etc.) with pronunciations for options
        
        Return as valid JSON with this exact structure:
        {{
            "vocabulary": [
                {{
                    "word": "{language_info['name']} word",
                    "translation": "English translation", 
                    "pronunciation": "romanized pronunciation",
                    "example": "{language_info['name']} example (romanization) - English translation",
                    "part_of_speech": "noun/verb/adjective",
                    "difficulty": "basic/intermediate/advanced"
                }}
            ],
            "conversations": [
                {{
                    "title": "Conversation title",
                    "scenario": "Context description",
                    "difficulty": "beginner/intermediate/advanced", 
                    "exchanges": [
                        {{
                            "speaker": "Person A/Person B",
                            "text": "{language_info['name']} text",
                            "translation": "English translation",
                            "pronunciation": "romanized pronunciation"
                        }}
                    ]
                }}
            ],
            "grammar_points": [
                {{
                    "rule": "Grammar rule name",
                    "explanation": "Clear explanation in English",
                    "examples": ["{language_info['name']} example 1", "{language_info['name']} example 2"],
                    "tips": "Learning tip"
                }}
            ],
            "exercises": [
                {{
                    "type": "multiple_choice",
                    "question": "Question in English",
                    "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
                    "options_pronunciations": ["pronunciation 1", "pronunciation 2", "pronunciation 3", "pronunciation 4"],
                    "correctAnswer": 0,
                    "explanation": "Explanation of correct answer",
                    "points": 10
                }}
            ]
        }}
        
        Ensure all {language_info['name']} text is authentic and culturally appropriate.
        Include proper pronunciations for all {language_info['name']} content.
        Make content unique and topic-specific to "{title}".
        """
        
        try:
            response = self.call_gemini_safe(prompt, f"{language}-content")
            
            # Extract JSON
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_content = response[json_start:json_end]
                new_content = json.loads(json_content)
                
                # Validate content counts
                vocab_count = len(new_content.get('vocabulary', []))
                conv_count = len(new_content.get('conversations', []))
                grammar_count = len(new_content.get('grammar_points', []))
                exercise_count = len(new_content.get('exercises', []))
                
                print(f"        📊 Content: V:{vocab_count} C:{conv_count} G:{grammar_count} E:{exercise_count}")
                
                # Update lesson
                update_response = requests.patch(
                    f"{SUPABASE_URL}/rest/v1/lessons",
                    headers=self.headers,
                    params={'id': f'eq.{lesson_id}'},
                    json={'content_metadata': new_content}
                )
                
                if update_response.status_code in [200, 204]:
                    print(f"        ✅ Content generated successfully")
                    return True
                else:
                    print(f"        ❌ Failed to update lesson content")
                    return False
            else:
                print(f"        ❌ Invalid JSON response")
                return False
                
        except Exception as e:
            print(f"        ❌ Content generation failed: {e}")
            return False
    
    def process_language_level(self, language: str, language_info: Dict[str, str], 
                              path_id: str, level: str) -> Dict[str, int]:
        """Process all lessons for a language-level combination"""
        print(f"\n  🚀 Processing {language_info['name']} {level} Level")
        print(f"  {'='*50}")
        
        lesson_count = LEVEL_STRUCTURE[level]['count']
        
        # Generate lesson titles
        titles = self.generate_lesson_titles(language, language_info, level, lesson_count)
        if not titles:
            return {'error': 'Failed to generate titles'}
        
        print(f"  📝 Generated {len(titles)} lesson titles")
        
        results = {'lessons_created': 0, 'content_generated': 0, 'total_needed': lesson_count}
        
        # Create lesson structures and generate content
        for i, title in enumerate(titles, 1):
            print(f"    📚 Lesson {i}/{lesson_count}: {title}")
            
            # Create lesson structure
            lesson_id = self.create_lesson_structure(path_id, language, language_info, level, title, i)
            if lesson_id:
                results['lessons_created'] += 1
                
                # Generate content
                if self.generate_lesson_content(lesson_id, language, language_info, level, title):
                    results['content_generated'] += 1
                
                # Brief pause between lessons
                time.sleep(2)
            
            # Progress update every 5 lessons
            if i % 5 == 0:
                print(f"    📊 Progress: {i}/{lesson_count} lessons processed")
        
        print(f"  🎉 {language_info['name']} {level} Complete: {results['content_generated']}/{lesson_count} lessons")
        return results
    
    def process_single_language(self, language: str, language_info: Dict[str, str]) -> Dict[str, Any]:
        """Process complete language (A1-C2)"""
        print(f"\n🌍 PROCESSING {language_info['name'].upper()} LANGUAGE")
        print(f"{'='*70}")
        
        start_time = datetime.now()
        
        # Create learning path
        path_id = self.create_learning_path(language, language_info)
        if not path_id:
            return {'error': 'Failed to create learning path'}
        
        self.created_paths[language] = path_id
        language_results = {'path_id': path_id}
        
        # Process each level
        total_lessons_created = 0
        total_content_generated = 0
        
        for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
            try:
                level_results = self.process_language_level(language, language_info, path_id, level)
                language_results[level] = level_results
                
                if 'error' not in level_results:
                    total_lessons_created += level_results.get('lessons_created', 0)
                    total_content_generated += level_results.get('content_generated', 0)
                
                # Longer pause between levels
                print(f"  ⏰ {level} complete. Waiting 15s before next level...")
                time.sleep(15)
                
            except Exception as e:
                print(f"  ❌ Error processing {language} {level}: {e}")
                language_results[level] = {'error': str(e)}
        
        # Final language summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        language_results['summary'] = {
            'total_lessons_created': total_lessons_created,
            'total_content_generated': total_content_generated,
            'expected_lessons': 130,  # 30+30+20+20+15+15
            'duration': str(duration),
            'completion_rate': f"{(total_content_generated/130*100):.1f}%"
        }
        
        print(f"\n🎉 {language_info['name'].upper()} COMPLETE!")
        print(f"📊 Created: {total_content_generated}/130 lessons ({(total_content_generated/130*100):.1f}%)")
        print(f"⏰ Duration: {duration}")
        
        return language_results
    
    def run_tier1_creation(self, max_workers: int = 5):
        """Run creation for all Tier 1 languages in parallel"""
        print("🌍 TIER 1 LANGUAGES COMPLETE CREATOR")
        print("="*70)
        print(f"Creating 2,730 lessons across 21 languages (A1-C2)")
        print(f"Using {max_workers} parallel workers")
        print(f"🤖 AI Models: Gemini 2.0 Flash (primary) + OpenAI GPT-4o-mini (backup)")
        print()
        
        start_time = datetime.now()
        all_results = {}
        
        # Process languages in parallel batches to manage API quota
        language_items = list(TIER1_LANGUAGES.items())
        batch_size = max_workers
        
        for i in range(0, len(language_items), batch_size):
            batch = language_items[i:i+batch_size]
            batch_languages = [lang for lang, _ in batch]
            
            print(f"\n🔄 PROCESSING BATCH {i//batch_size + 1}: {', '.join([info['name'] for _, info in batch])}")
            print("="*70)
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(batch)) as executor:
                # Submit all languages in current batch
                future_to_language = {
                    executor.submit(self.process_single_language, language, language_info): language
                    for language, language_info in batch
                }
                
                # Collect results
                for future in concurrent.futures.as_completed(future_to_language):
                    language = future_to_language[future]
                    try:
                        result = future.result()
                        all_results[language] = result
                        print(f"✅ {TIER1_LANGUAGES[language]['name']} processing complete")
                    except Exception as e:
                        all_results[language] = {'error': str(e)}
                        print(f"❌ {TIER1_LANGUAGES[language]['name']} failed: {e}")
            
            # Reduced wait between batches
            if i + batch_size < len(language_items):
                wait_time = 60  # Reduced from 300s to 60s
                print(f"\n⏰ Batch {i//batch_size + 1} complete. Waiting {wait_time}s before next batch...")
                time.sleep(wait_time)
        
        # Generate final report
        end_time = datetime.now()
        duration = end_time - start_time
        
        self.generate_final_report(all_results, duration)
    
    def generate_final_report(self, all_results: Dict[str, Any], duration):
        """Generate comprehensive final report"""
        global gemini_failures, openai_calls
        
        print("\n" + "="*70)
        print("🎉 TIER 1 LANGUAGES CREATION COMPLETE!")
        print("="*70)
        print(f"⏰ Total Duration: {duration}")
        print(f"🔧 Total API Calls: {api_call_count}")
        print(f"🤖 Gemini Failures: {gemini_failures}")
        print(f"🔄 OpenAI Backup Calls: {openai_calls}")
        print()
        
        # Summary statistics
        total_languages = len(TIER1_LANGUAGES)
        successful_languages = 0
        total_lessons_created = 0
        total_expected = total_languages * 130  # 130 lessons per language
        
        print("📊 RESULTS BY LANGUAGE:")
        print("-" * 70)
        
        for language, results in all_results.items():
            language_info = TIER1_LANGUAGES[language]
            
            if 'error' not in results and 'summary' in results:
                summary = results['summary']
                lessons_created = summary['total_content_generated']
                completion_rate = summary['completion_rate']
                
                print(f"{language_info['name']:15} | {lessons_created:3}/130 lessons | {completion_rate:6} | ✅")
                
                successful_languages += 1
                total_lessons_created += lessons_created
            else:
                error_msg = results.get('error', 'Unknown error')
                print(f"{language_info['name']:15} | ERROR: {error_msg[:40]}... | ❌")
        
        print("-" * 70)
        print(f"📈 GRAND TOTALS:")
        print(f"✅ Successful Languages: {successful_languages}/{total_languages}")
        print(f"📚 Total Lessons Created: {total_lessons_created}/{total_expected}")
        print(f"📊 Overall Completion: {(total_lessons_created/total_expected*100):.1f}%")
        print(f"🤖 API Resilience: {((api_call_count - gemini_failures)/api_call_count*100):.1f}% Gemini success")
        
        if total_lessons_created >= total_expected * 0.8:  # 80% success rate
            print("\n🎉 MISSION ACCOMPLISHED!")
            print("✅ Tier 1 languages successfully scaled to A1-C2")
            print("✅ Ready for audio generation and quality validation")
        else:
            print("\n⚠️  PARTIAL SUCCESS")
            print("🔄 Some languages need retry or manual intervention")

def main():
    """Main execution function"""
    creator = Tier1LanguageCreator()
    
    # Run with 3 parallel workers to manage API quota
    creator.run_tier1_creation(max_workers=3)

if __name__ == "__main__":
    main() 