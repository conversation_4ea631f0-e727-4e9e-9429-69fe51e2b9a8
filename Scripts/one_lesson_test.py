#!/usr/bin/env python3
"""
🧪 ONE LESSON TEST
Creates exactly ONE Tamil lesson and reports every step
"""

import os
import json
import requests
import uuid
from datetime import datetime

# API Configuration
GEMINI_API_KEY = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def test_gemini_2_flash_lite():
    """Test Gemini 2.0 Flash Lite"""
    print("🧪 Step 1: Testing Gemini 2.0 Flash Lite...")
    
    try:
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        
        # Try different model names for 2.0 Flash Lite
        model_names = [
            "gemini-2.0-flash-lite",
            "gemini-2.0-flash-lite-exp", 
            "gemini-2.0-flash-experimental",
            "gemini-1.5-flash"  # fallback
        ]
        
        model = None
        model_used = None
        
        for model_name in model_names:
            try:
                model = genai.GenerativeModel(model_name)
                model_used = model_name
                print(f"✅ Successfully initialized: {model_name}")
                break
            except Exception as e:
                print(f"❌ Failed {model_name}: {str(e)[:100]}")
                continue
        
        if not model:
            print("❌ No Gemini model available")
            return None, None
        
        return model, model_used
        
    except Exception as e:
        print(f"❌ Gemini initialization failed: {e}")
        return None, None

def create_one_lesson():
    """Create exactly one Tamil lesson"""
    print("\n🧪 Step 2: Creating ONE Tamil lesson...")
    
    # Initialize Gemini
    model, model_used = test_gemini_2_flash_lite()
    if not model:
        return False
    
    print(f"🤖 Using model: {model_used}")
    
    # Create lesson prompt
    prompt = """Create a Tamil C2 level lesson about advanced literature. Return ONLY this JSON structure:

{
    "title": "Advanced Tamil Literature Analysis",
    "description": "Comprehensive study of classical Tamil poetry and prose",
    "lesson_type": "vocabulary",
    "difficulty_level": "c2",
    "estimated_duration": 45,
    "vocabulary": [
        {"word": "இலக்கியம்", "romanization": "ilakkiyam", "pronunciation": "i-lak-ki-yam", "meaning": "literature"},
        {"word": "கவிதை", "romanization": "kavithai", "pronunciation": "ka-vi-thai", "meaning": "poetry"}
    ],
    "conversations": [
        {"speaker": "A", "text": "இந்த கவிதையின் பொருள் என்ன?", "romanization": "indha kavithayin porul enna?", "translation": "What is the meaning of this poem?"}
    ],
    "grammar": [
        {"title": "Advanced Syntax", "explanation": "Complex sentence structures in Tamil", "tamil_example": "கவிஞர் எழுதிய நூல்", "romanization": "kavignar ezhuthiya nool", "translation": "book written by the poet"}
    ],
    "exercises": [
        {"question": "Which word means literature?", "options": ["இலக்கியம்", "கவிதை", "நூல்", "கதை"], "correct_answer": 0, "explanation": "இலக்கியம் means literature"}
    ]
}

Make sure the JSON is valid and complete."""
    
    print("🔄 Step 3: Calling Gemini API...")
    try:
        response = model.generate_content(prompt)
        
        if not response or not response.text:
            print("❌ Step 3 FAILED: No response from Gemini")
            return False
        
        print(f"✅ Step 3 SUCCESS: Got response ({len(response.text)} chars)")
        
        # Clean the response
        print("\n🧹 Step 4: Cleaning JSON response...")
        content = response.text.strip()
        print(f"📝 Raw response:\n{content}")
        
        # Remove markdown
        if content.startswith('```json'):
            content = content.replace('```json', '').replace('```', '').strip()
        elif content.startswith('```'):
            content = content.replace('```', '').strip()
        
        print(f"\n🧹 Cleaned response:\n{content}")
        
        # Parse JSON
        print("\n📋 Step 5: Parsing JSON...")
        try:
            lesson_data = json.loads(content)
            print("✅ Step 5 SUCCESS: JSON parsed successfully!")
            print(f"📚 Title: {lesson_data.get('title', 'No title')}")
            print(f"📖 Description: {lesson_data.get('description', 'No description')}")
            print(f"📝 Vocabulary: {len(lesson_data.get('vocabulary', []))} items")
            print(f"💬 Conversations: {len(lesson_data.get('conversations', []))} items")
            print(f"📚 Grammar: {len(lesson_data.get('grammar', []))} items")
            print(f"🎯 Exercises: {len(lesson_data.get('exercises', []))} items")
        except json.JSONDecodeError as e:
            print(f"❌ Step 5 FAILED: JSON parsing error: {e}")
            return False
        
        # Save to database
        print("\n💾 Step 6: Saving to database...")
        
        # Use Tamil C2 path ID (from the logs)
        path_id = "5ebe8e2d-6752-48f0-a52e-34c6c586ace8"
        
        lesson_payload = {
            "id": str(uuid.uuid4()),
            "path_id": path_id,
            "title": lesson_data.get("title", "Test Lesson"),
            "description": lesson_data.get("description", "Test Description"),
            "lesson_type": lesson_data.get("lesson_type", "vocabulary"),
            "difficulty_level": lesson_data.get("difficulty_level", "c2"),
            "estimated_duration": lesson_data.get("estimated_duration", 45),
            "content": lesson_data.get("vocabulary", []),
            "content_metadata": {
                "conversations": lesson_data.get("conversations", []),
                "grammar": lesson_data.get("grammar", []),
                "exercises": lesson_data.get("exercises", [])
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        print(f"📦 Lesson payload prepared (path_id: {path_id})")
        
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=headers,
            json=lesson_payload
        )
        
        print(f"🌐 Database response: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("✅ Step 6 SUCCESS: Lesson saved to database!")
            return True
        else:
            print(f"❌ Step 6 FAILED: Database error {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False

def main():
    print("🧪 ONE LESSON TEST - DETAILED STEP BY STEP")
    print("=" * 60)
    print("Goal: Create exactly ONE Tamil C2 lesson and report every step")
    print()
    
    success = create_one_lesson()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: One lesson created successfully!")
        print("✅ Ready to scale up the main scripts")
    else:
        print("❌ FAILED: Could not create one lesson")
        print("🔧 Need to fix the issues first")

if __name__ == "__main__":
    main() 