#!/usr/bin/env python3
"""
🔧 TIER 1 PATH FIXER
Fixes 409 errors by using existing learning paths and proceeding with lesson creation
"""

import os
import json
import requests
import time
import uuid
from typing import Dict, List, Any, Tuple
from datetime import datetime
import random
import concurrent.futures
from threading import Lock

# API Configurations
os.environ['GEMINI_API_KEY'] = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'
OPENAI_API_KEY = "***********************************************************************************************************************************************************************"

# Initialize Gemini
try:
    import google.generativeai as genai
    genai.configure(api_key=os.environ['GEMINI_API_KEY'])
    model = genai.GenerativeModel('gemini-2.0-flash-exp')
    print("✅ Gemini Flash 2.0 Exp initialized successfully")
    GEMINI_AVAILABLE = True
except Exception as e:
    print(f"⚠️  Gemini initialization failed: {e}")
    GEMINI_AVAILABLE = False

# OpenAI headers for backup
OPENAI_HEADERS = {
    'Authorization': f'Bearer {OPENAI_API_KEY}',
    'Content-Type': 'application/json'
}

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Thread-safe counters
api_call_lock = Lock()
api_call_count = 0
gemini_failures = 0
openai_calls = 0

# Tier 1 Languages (21 total)
TIER1_LANGUAGES = {
    # European (8)
    'english': {'name': 'English', 'code': 'en', 'family': 'Germanic'},
    'spanish': {'name': 'Spanish', 'code': 'es', 'family': 'Romance'}, 
    'french': {'name': 'French', 'code': 'fr', 'family': 'Romance'},
    'german': {'name': 'German', 'code': 'de', 'family': 'Germanic'},
    'italian': {'name': 'Italian', 'code': 'it', 'family': 'Romance'},
    'portuguese': {'name': 'Portuguese', 'code': 'pt', 'family': 'Romance'},
    'dutch': {'name': 'Dutch', 'code': 'nl', 'family': 'Germanic'},
    'russian': {'name': 'Russian', 'code': 'ru', 'family': 'Slavic'},
    
    # Asian (3)
    'chinese': {'name': 'Chinese', 'code': 'zh', 'family': 'Sino-Tibetan'},
    'japanese': {'name': 'Japanese', 'code': 'ja', 'family': 'Japonic'},
    'korean': {'name': 'Korean', 'code': 'ko', 'family': 'Koreanic'},
    
    # Middle Eastern (1)
    'arabic': {'name': 'Arabic', 'code': 'ar', 'family': 'Semitic'},
    
    # Indian (9)
    'hindi': {'name': 'Hindi', 'code': 'hi', 'family': 'Indo-Aryan'},
    'tamil': {'name': 'Tamil', 'code': 'ta', 'family': 'Dravidian'},
    'telugu': {'name': 'Telugu', 'code': 'te', 'family': 'Dravidian'},
    'kannada': {'name': 'Kannada', 'code': 'kn', 'family': 'Dravidian'},
    'malayalam': {'name': 'Malayalam', 'code': 'ml', 'family': 'Dravidian'},
    'bengali': {'name': 'Bengali', 'code': 'bn', 'family': 'Indo-Aryan'},
    'marathi': {'name': 'Marathi', 'code': 'mr', 'family': 'Indo-Aryan'},
    'punjabi': {'name': 'Punjabi', 'code': 'pa', 'family': 'Indo-Aryan'},
    'gujarati': {'name': 'Gujarati', 'code': 'gu', 'family': 'Indo-Aryan'}
}

def get_existing_paths():
    """Get all existing learning paths and map them to languages"""
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    print("🔍 Discovering existing learning paths...")
    
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/learning_paths",
        headers=headers,
        params={'select': 'id,name,level'}
    )
    
    if response.status_code != 200:
        print(f"❌ Failed to fetch learning paths: {response.status_code}")
        return {}
    
    paths = response.json()
    language_paths = {}
    
    for path in paths:
        name = path.get('name', '').lower()
        for lang_key, lang_info in TIER1_LANGUAGES.items():
            lang_name = lang_info['name'].lower()
            if lang_name in name and ('complete' in name or 'a1-c2' in name.lower()):
                language_paths[lang_key] = path['id']
                print(f"  📁 Found {lang_info['name']}: {path['id']}")
                break
    
    print(f"✅ Discovered {len(language_paths)}/21 existing language paths")
    return language_paths

def create_missing_paths():
    """Create learning paths for languages that don't have them"""
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    existing_paths = get_existing_paths()
    
    print(f"\n🔧 Creating missing learning paths...")
    created_count = 0
    
    for lang_key, lang_info in TIER1_LANGUAGES.items():
        if lang_key not in existing_paths:
            print(f"  📁 Creating path for {lang_info['name']}")
            
            path_data = {
                'id': str(uuid.uuid4()),
                'language_id': str(uuid.uuid4()),
                'agent_id': str(uuid.uuid4()),
                'name': f"{lang_info['name']} Complete Course (A1-C2)",
                'description': f"Complete {lang_info['name']} language learning path from beginner (A1) to proficiency (C2)",
                'level': 'A1-C2',
                'estimated_hours': 300,
                'sequence_order': 1,
                'prerequisites': [],
                'learning_objectives': [
                    f"Master {lang_info['name']} from beginner to advanced level",
                    "Develop speaking, listening, reading, and writing skills",
                    "Understand cultural context and authentic usage",
                    "Achieve CEFR C2 proficiency"
                ],
                'cultural_focus': [f"{lang_info['name']} culture", "Authentic contexts", "Real-world applications"],
                'is_active': True,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/learning_paths",
                headers=headers,
                json=path_data
            )
            
            if response.status_code in [201, 200]:
                print(f"    ✅ Created path for {lang_info['name']}")
                existing_paths[lang_key] = path_data['id']
                created_count += 1
                time.sleep(1)  # Brief pause
            else:
                print(f"    ❌ Failed to create path for {lang_key}: {response.status_code}")
        else:
            print(f"  ✅ {lang_info['name']} path already exists")
    
    print(f"\n🎉 Path creation complete: {created_count} new paths created")
    print(f"📊 Total paths available: {len(existing_paths)}/21")
    
    return existing_paths

def main():
    print("🔧 TIER 1 PATH FIXER")
    print("="*60)
    print("Ensuring all Tier 1 languages have learning paths")
    print()
    
    # Get or create all needed paths
    all_paths = create_missing_paths()
    
    if len(all_paths) == 21:
        print("\n🎉 SUCCESS! All 21 Tier 1 languages have learning paths")
        print("✅ Ready to run lesson creation without 409 errors")
        
        # Save path mapping for reference
        with open('tier1_language_paths.json', 'w') as f:
            json.dump(all_paths, f, indent=2)
        print("💾 Path mapping saved to tier1_language_paths.json")
    else:
        print(f"\n⚠️  {len(all_paths)}/21 paths available")
        print("🔄 Some paths may need manual intervention")

if __name__ == "__main__":
    main() 