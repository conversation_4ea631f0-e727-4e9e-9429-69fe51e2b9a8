#!/usr/bin/env python3
"""
🎵 FIXED TAMIL AUDIO SYSTEM
Comprehensive fix for all audio issues:
1. Uses NATIVE Tamil voice from ElevenLabs
2. Fixes caching issues with proper file naming
3. Generates ALL content types (vocab, explanations, exercises)
4. Uses better models and voice settings
"""

import os
import json
import requests
import time
import hashlib
from typing import Dict, Any, Optional

# Configuration
ELEVENLABS_API_KEY = "***************************************************"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# FIXED VOICE CONFIGURATION
# Using "Nila" - The OFFICIAL Tamil voice from ElevenLabs
TAMIL_VOICES = {
    'nila_tamil': 'C2RGMrNBTZaNfddRPeRH',  # Nila - Official Tamil voice
    'aria_multi': 'MF3mGyEYCl7XYWbV9V6O',  # Aria - Good for multilingual
    'rachel_clear': 'TxGEqnHWrfWFTfGW9XjX'  # Rachel - Clear pronunciation
}

# IMPROVED VOICE SETTINGS for Tamil
VOICE_SETTINGS = {
    'C2RGMrNBTZaNfddRPeRH': {  # Nila - Tamil native
        'stability': 0.8,          # More stable for Tamil
        'similarity_boost': 0.9,   # High similarity for accent consistency  
        'style': 0.3,              # Moderate style for natural Tamil flow
        'use_speaker_boost': True
    },
    'MF3mGyEYCl7XYWbV9V6O': {  # Aria - Multilingual backup
        'stability': 0.7,
        'similarity_boost': 0.8,
        'style': 0.4,
        'use_speaker_boost': True
    }
}

# Headers
SUPABASE_HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

ELEVENLABS_HEADERS = {
    'Accept': 'audio/mpeg',
    'Content-Type': 'application/json',
    'xi-api-key': ELEVENLABS_API_KEY
}

class FixedTamilAudioSystem:
    """Fixed Tamil audio system with proper voice and caching"""
    
    def __init__(self):
        self.audio_count = 0
        self.success_count = 0
        self.error_count = 0
        
    def generate_audio(self, text: str, voice_id: str = 'C2RGMrNBTZaNfddRPeRH') -> Optional[bytes]:
        """Generate audio using PROPER Tamil voice"""
        
        # Use the latest Turbo model for faster, better quality
        data = {
            'text': text,
            'model_id': 'eleven_turbo_v2_5',  # Faster, better model
            'voice_settings': VOICE_SETTINGS.get(voice_id, VOICE_SETTINGS['C2RGMrNBTZaNfddRPeRH'])
        }
        
        try:
            response = requests.post(
                f'https://api.elevenlabs.io/v1/text-to-speech/{voice_id}',
                json=data,
                headers=ELEVENLABS_HEADERS,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✅ Generated Tamil audio: {text[:30]}... ({len(text)} chars)")
                self.success_count += 1
                return response.content
            else:
                print(f"❌ ElevenLabs error {response.status_code}: {response.text}")
                self.error_count += 1
                return None
                
        except Exception as e:
            print(f"❌ Audio generation failed: {e}")
            self.error_count += 1
            return None
    
    def create_proper_filename(self, lesson_id: str, content_type: str, index: int, audio_type: str) -> str:
        """Create proper filename to avoid caching conflicts"""
        # Include lesson_id to prevent cross-lesson caching issues
        filename = f"{lesson_id}_{content_type}_{index:02d}_{audio_type}.mp3"
        return filename
    
    def upload_to_supabase(self, audio_data: bytes, file_path: str) -> Optional[str]:
        """Upload audio to Supabase storage"""
        try:
            # Upload to Supabase Storage
            upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{file_path}"
            
            response = requests.post(
                upload_url,
                data=audio_data,
                headers={
                    'apikey': SUPABASE_KEY,
                    'Authorization': f'Bearer {SUPABASE_KEY}',
                    'Content-Type': 'audio/mpeg',
                    'Cache-Control': 'max-age=3600'
                }
            )
            
            if response.status_code in [200, 201]:
                public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{file_path}"
                print(f"✅ Uploaded: {file_path}")
                return public_url
            else:
                print(f"❌ Upload failed {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Upload error: {e}")
            return None
    
    def generate_comprehensive_audio(self, lesson_id: str) -> Dict[str, str]:
        """Generate ALL audio for a lesson - vocab, explanations, exercises"""
        print(f"🎵 Generating comprehensive audio for lesson: {lesson_id}")
        audio_urls = {}
        
        # Get lesson content
        lesson_data = self.get_lesson_content(lesson_id)
        if not lesson_data:
            print(f"❌ Could not fetch lesson content")
            return {}
        
        content_metadata = lesson_data.get('content_metadata', {})
        lesson_title = lesson_data.get('title', 'unknown')
        lesson_slug = lesson_title.lower().replace(' ', '_').replace(',', '').replace(':', '')
        
        print(f"📚 Processing: {lesson_title}")
        
        # 1. VOCABULARY AUDIO
        vocabulary = content_metadata.get('vocabulary', [])
        print(f"📝 Generating {len(vocabulary)} vocabulary items...")
        
        for i, vocab in enumerate(vocabulary, 1):
            word = vocab.get('word', '')
            meaning = vocab.get('meaning', '')
            example = vocab.get('example', '')
            
            if word:
                # Word audio
                filename = self.create_proper_filename(lesson_slug, 'vocab', i, 'word')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(word, 'C2RGMrNBTZaNfddRPeRH')  # Native Tamil voice
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'vocab_{i:02d}_word'] = url
                
                time.sleep(1)  # Rate limiting
            
            if example:
                # Example sentence audio
                filename = self.create_proper_filename(lesson_slug, 'vocab', i, 'example')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(example, 'C2RGMrNBTZaNfddRPeRH')
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'vocab_{i:02d}_example'] = url
                
                time.sleep(1)
            
            if meaning:
                # Meaning/explanation audio
                filename = self.create_proper_filename(lesson_slug, 'vocab', i, 'meaning')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(meaning, 'C2RGMrNBTZaNfddRPeRH')
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'vocab_{i:02d}_meaning'] = url
                
                time.sleep(1)
        
        # 2. EXERCISE AUDIO
        exercises = content_metadata.get('exercises', [])
        print(f"🎯 Generating {len(exercises)} exercise items...")
        
        for i, exercise in enumerate(exercises, 1):
            question = exercise.get('question', '')
            options = exercise.get('options', [])
            
            if question:
                # Question audio
                filename = self.create_proper_filename(lesson_slug, 'exercise', i, 'question')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(question, 'C2RGMrNBTZaNfddRPeRH')
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'exercise_{i:02d}_question'] = url
                
                time.sleep(1)
            
            # Option audio
            for j, option in enumerate(options[:4], 1):  # A, B, C, D
                if option:
                    option_letter = chr(ord('a') + j - 1)  # a, b, c, d
                    filename = self.create_proper_filename(lesson_slug, 'exercise', i, f'option_{option_letter}')
                    file_path = f"tamil/a1/{lesson_slug}/{filename}"
                    
                    audio_data = self.generate_audio(option, 'C2RGMrNBTZaNfddRPeRH')
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, file_path)
                        if url:
                            audio_urls[f'exercise_{i:02d}_option_{option_letter}'] = url
                    
                    time.sleep(1)
        
        # 3. GRAMMAR EXPLANATIONS
        grammar_points = content_metadata.get('grammar_points', [])
        print(f"📖 Generating {len(grammar_points)} grammar explanations...")
        
        for i, grammar in enumerate(grammar_points, 1):
            explanation = grammar.get('explanation', '')
            examples = grammar.get('examples', [])
            
            if explanation:
                filename = self.create_proper_filename(lesson_slug, 'grammar', i, 'explanation')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(explanation, 'C2RGMrNBTZaNfddRPeRH')
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'grammar_{i:02d}_explanation'] = url
                
                time.sleep(1)
            
            for j, example in enumerate(examples, 1):
                if example:
                    filename = self.create_proper_filename(lesson_slug, 'grammar', i, f'example_{j}')
                    file_path = f"tamil/a1/{lesson_slug}/{filename}"
                    
                    audio_data = self.generate_audio(example, 'C2RGMrNBTZaNfddRPeRH')
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, file_path)
                        if url:
                            audio_urls[f'grammar_{i:02d}_example_{j}'] = url
                    
                    time.sleep(1)
        
        print(f"✅ Generated {len(audio_urls)} audio files")
        print(f"📊 Success: {self.success_count}, Errors: {self.error_count}")
        
        return audio_urls
    
    def get_lesson_content(self, lesson_id: str) -> Optional[Dict]:
        """Get lesson content from database"""
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=SUPABASE_HEADERS,
                params={
                    'id': f'eq.{lesson_id}',
                    'select': 'id,title,content_metadata'
                }
            )
            
            if response.status_code == 200:
                lessons = response.json()
                return lessons[0] if lessons else None
            else:
                print(f"❌ Failed to fetch lesson: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error fetching lesson: {e}")
            return None
    
    def update_lesson_audio_urls(self, lesson_id: str, audio_urls: Dict[str, str]) -> bool:
        """Update lesson with generated audio URLs"""
        try:
            # Update the content_metadata with audio_urls
            response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=SUPABASE_HEADERS,
                params={'id': f'eq.{lesson_id}'},
                json={
                    'content_metadata': {
                        'audio_urls': audio_urls
                    }
                }
            )
            
            if response.status_code in [200, 204]:
                print(f"✅ Updated lesson with {len(audio_urls)} audio URLs")
                return True
            else:
                print(f"❌ Failed to update lesson: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating lesson: {e}")
            return False

def test_single_lesson():
    """Test with a single lesson"""
    print("🧪 TESTING FIXED TAMIL AUDIO SYSTEM")
    print("=" * 50)
    
    # Use the Colors and Descriptions lesson from Tamil A1
    test_lesson_id = "d5ef89df-5cd2-453e-8809-502440812f5d"  # Tamil A1: Colors and Descriptions
    
    audio_system = FixedTamilAudioSystem()
    
    # Generate comprehensive audio
    audio_urls = audio_system.generate_comprehensive_audio(test_lesson_id)
    
    if audio_urls:
        # Update lesson with audio URLs
        audio_system.update_lesson_audio_urls(test_lesson_id, audio_urls)
        
        # Save results for verification
        with open('fixed_audio_results.json', 'w') as f:
            json.dump(audio_urls, f, indent=2)
        
        print(f"\n🎉 SUCCESS!")
        print(f"📊 Generated {len(audio_urls)} audio files")
        print(f"📁 Results saved to: fixed_audio_results.json")
        print(f"🎯 Using native Tamil voice (Nila: C2RGMrNBTZaNfddRPeRH)")
        print(f"🚀 Using Turbo v2.5 model for better quality")
        print(f"✅ Proper caching with unique filenames")
        print(f"🎵 Comprehensive content (vocab + explanations + exercises)")
    else:
        print(f"\n❌ FAILED to generate audio")

if __name__ == "__main__":
    test_single_lesson() 