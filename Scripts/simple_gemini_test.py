#!/usr/bin/env python3
"""
🧪 SIMPLE GEMINI FLASH 2.0 TEST
Tests Gemini Flash 2.0 for lesson creation with proper JSON handling
"""

import os
import json
import time

# Gemini Setup
GEMINI_API_KEY = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'

def test_gemini_simple():
    """Test Gemini Flash 2.0 with simple lesson creation"""
    print("🧪 Testing Gemini Flash 2.0...")
    
    try:
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        
        # Use the correct model name
        model = genai.GenerativeModel("gemini-1.5-flash")
        print("✅ Gemini Flash 1.5 initialized")
        
        prompt = """Create a Tamil A1 lesson about greetings. Return ONLY valid JSON with this exact structure:
{
    "title": "Basic Tamil Greetings",
    "description": "Learn essential Tamil greetings",
    "vocabulary": [
        {"word": "வணக்கம்", "pronunciation": "vanakkam", "meaning": "hello/goodbye"},
        {"word": "நன்றி", "pronunciation": "nandri", "meaning": "thank you"}
    ]
}"""
        
        print("🔄 Generating content...")
        response = model.generate_content(prompt)
        
        if response and response.text:
            print(f"✅ Got response: {len(response.text)} chars")
            
            # Clean the response
            content = response.text.strip()
            print(f"📝 Raw response:\n{content}")
            
            # Remove markdown if present
            if content.startswith('```json'):
                content = content.replace('```json', '').replace('```', '').strip()
            elif content.startswith('```'):
                content = content.replace('```', '').strip()
            
            print(f"🧹 Cleaned response:\n{content}")
            
            # Try to parse JSON
            try:
                lesson_data = json.loads(content)
                print("✅ JSON parsing successful!")
                print(f"📚 Lesson title: {lesson_data.get('title', 'No title')}")
                print(f"📖 Description: {lesson_data.get('description', 'No description')}")
                print(f"📝 Vocabulary count: {len(lesson_data.get('vocabulary', []))}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                return False
        else:
            print("❌ No response from Gemini")
            return False
            
    except Exception as e:
        print(f"❌ Gemini error: {e}")
        return False

def test_multiple_calls():
    """Test multiple calls with rate limiting"""
    print("\n🧪 Testing multiple calls with rate limiting...")
    
    try:
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        model = genai.GenerativeModel("gemini-1.5-flash")
        
        success_count = 0
        total_calls = 3
        
        for i in range(total_calls):
            print(f"\n🔄 Call {i+1}/{total_calls}")
            
            prompt = f"""Create a Tamil A1 lesson #{i+1}. Return ONLY valid JSON:
{{
    "title": "Tamil Lesson {i+1}",
    "description": "Basic Tamil lesson",
    "vocabulary": [
        {{"word": "example", "pronunciation": "example", "meaning": "example"}}
    ]
}}"""
            
            try:
                response = model.generate_content(prompt)
                if response and response.text:
                    content = response.text.strip()
                    if content.startswith('```json'):
                        content = content.replace('```json', '').replace('```', '').strip()
                    
                    json.loads(content)  # Test parsing
                    print(f"✅ Call {i+1} successful")
                    success_count += 1
                else:
                    print(f"❌ Call {i+1} failed - no response")
            except Exception as e:
                print(f"❌ Call {i+1} failed: {str(e)[:100]}")
            
            # Rate limiting
            if i < total_calls - 1:
                print("⏳ Waiting 3 seconds...")
                time.sleep(3)
        
        print(f"\n📊 Success rate: {success_count}/{total_calls} ({success_count/total_calls*100:.1f}%)")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Multiple calls test failed: {e}")
        return False

def main():
    print("🧪 GEMINI FLASH 1.5 SIMPLE TEST")
    print("=" * 50)
    
    # Test 1: Simple call
    simple_works = test_gemini_simple()
    
    # Test 2: Multiple calls
    if simple_works:
        multiple_works = test_multiple_calls()
    else:
        multiple_works = False
    
    print("\n📊 FINAL RESULTS:")
    print(f"Simple test: {'✅ PASS' if simple_works else '❌ FAIL'}")
    print(f"Multiple test: {'✅ PASS' if multiple_works else '❌ FAIL'}")
    
    if simple_works:
        print("\n✅ Gemini Flash 1.5 is working!")
        print("🚀 Ready to fix main scripts")
    else:
        print("\n❌ Gemini Flash 1.5 not working")
        print("🔧 Need to investigate")

if __name__ == "__main__":
    main() 