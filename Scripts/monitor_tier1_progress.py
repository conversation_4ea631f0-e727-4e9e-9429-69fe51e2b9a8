#!/usr/bin/env python3
"""
📊 TIER 1 PROGRESS MONITOR
Tracks progress in real-time and shows recent activity
"""

import requests
import time
from datetime import datetime, timedelta

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def get_lesson_count():
    """Get total lesson count"""
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={'select': 'id'}
    )
    
    if response.status_code == 200:
        return len(response.json())
    return 0

def get_recent_lessons(minutes=5):
    """Get lessons created in last N minutes"""
    cutoff = (datetime.now() - timedelta(minutes=minutes)).isoformat()
    
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'created_at': f'gte.{cutoff}',
            'select': 'id,title,created_at',
            'order': 'created_at.desc'
        }
    )
    
    if response.status_code == 200:
        return response.json()
    return []

def main():
    print("📊 TIER 1 PROGRESS MONITOR")
    print("="*60)
    print("Monitoring lesson creation every 2 minutes...")
    print("Press Ctrl+C to stop")
    print()
    
    last_count = get_lesson_count()
    print(f"🚀 Starting count: {last_count} lessons")
    
    try:
        while True:
            time.sleep(120)  # Check every 2 minutes
            
            current_count = get_lesson_count()
            new_lessons = current_count - last_count
            
            # Get recent activity
            recent = get_recent_lessons(minutes=5)
            
            print(f"\n🕐 {datetime.now().strftime('%H:%M:%S')}")
            print(f"📚 Total Lessons: {current_count} (+{new_lessons} in last 2 min)")
            print(f"📊 Progress: {(current_count/2730*100):.1f}% of 2,730 target")
            
            if recent:
                print(f"🔥 Recent Activity ({len(recent)} lessons in last 5 min):")
                for lesson in recent[:3]:
                    title = lesson.get('title', 'Unknown')[:40]
                    created = lesson.get('created_at', '')[-8:-3]  # Just time
                    print(f"  📚 {created}: {title}...")
            else:
                print("😴 No recent activity")
            
            last_count = current_count
            
    except KeyboardInterrupt:
        print(f"\n\n📊 Final Count: {get_lesson_count()} lessons")
        print("👋 Monitoring stopped")

if __name__ == "__main__":
    main() 