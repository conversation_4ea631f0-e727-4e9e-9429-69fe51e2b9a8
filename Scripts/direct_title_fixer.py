#!/usr/bin/env python3
"""
Direct Title Fixer - Extract English titles from Tamil titles
"""

import requests
import re

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

TAMIL_PATHS = {
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

def extract_english_title(tamil_title):
    """Extract English title from Tamil title with parentheses"""
    # Look for text in parentheses
    match = re.search(r'\(([^)]+)\)', tamil_title)
    if match:
        return match.group(1).strip()
    else:
        # If no parentheses, return the original title
        return tamil_title

def fix_titles_for_level(level, path_id):
    """Fix titles for a specific level"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    print(f"\n🔧 FIXING {level} TITLES")
    print("=" * 40)
    
    # Get all lessons for this level
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'path_id': f'eq.{path_id}',
            'select': 'id,title,sequence_order',
            'order': 'sequence_order'
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Failed to fetch {level} lessons")
        return 0
    
    lessons = response.json()
    updated_count = 0
    
    for lesson in lessons:
        old_title = lesson['title']
        new_title = extract_english_title(old_title)
        
        if new_title != old_title:
            print(f"📝 Lesson {lesson['sequence_order']}: {old_title[:30]}... → {new_title}")
            
            # Update the lesson title
            update_response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=headers,
                params={'id': f'eq.{lesson["id"]}'},
                json={'title': new_title}
            )
            
            if update_response.status_code == 204:
                updated_count += 1
                print(f"   ✅ Updated")
            else:
                print(f"   ❌ Failed to update")
        else:
            print(f"📝 Lesson {lesson['sequence_order']}: Already in English format")
    
    print(f"\n📊 {level} RESULTS: {updated_count}/{len(lessons)} titles updated")
    return updated_count

def main():
    """Main function"""
    
    print("🔧 DIRECT TITLE FIXER")
    print("Extracting English titles from Tamil titles")
    print("=" * 50)
    
    total_updated = 0
    
    for level, path_id in TAMIL_PATHS.items():
        updated = fix_titles_for_level(level, path_id)
        total_updated += updated
    
    print(f"\n🎉 TOTAL RESULTS: {total_updated} titles updated")
    print("✅ Tamil titles converted to English successfully!")

if __name__ == "__main__":
    main()
