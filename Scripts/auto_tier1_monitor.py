#!/usr/bin/env python3
"""
🤖 AUTOMATED TIER 1 MONITOR & FIXER
Continuously monitors all Tier 1 processes, detects failures, fixes issues, and restarts automatically
- Monitors lesson creation progress
- Detects API failures and rate limit issues  
- Automatically restarts failed processes
- Switches between Gemini and OpenAI when needed
- Runs until all phases (1-4) are complete
"""

import os
import sys
import time
import subprocess
import requests
import json
import signal
from datetime import datetime, timedelta
from typing import Dict, List, Any
import psutil

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

class AutoTier1Monitor:
    def __init__(self):
        self.processes = {}
        self.last_lesson_count = 0
        self.last_audio_count = 0
        self.restart_count = 0
        self.max_restarts = 10
        self.check_interval = 60  # Check every minute
        
    def log(self, message: str):
        """Log with timestamp"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"🤖 [{timestamp}] {message}")
    
    def get_lesson_count(self) -> int:
        """Get total Tamil lesson count"""
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=headers,
                params={'select': 'id', 'path_id': 'like.*tamil*'},
                timeout=10
            )
            if response.status_code == 200:
                return len(response.json())
            return 0
        except:
            return 0
    
    def get_audio_count(self) -> int:
        """Get count of lessons with audio"""
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=headers,
                params={
                    'select': 'content_metadata',
                    'path_id': 'like.*tamil*',
                    'content_metadata': 'cs.{"audio_urls"}'
                },
                timeout=10
            )
            if response.status_code == 200:
                lessons = response.json()
                audio_count = 0
                for lesson in lessons:
                    content = lesson.get('content_metadata', {})
                    if content.get('audio_urls'):
                        audio_count += 1
                return audio_count
            return 0
        except:
            return 0
    
    def check_process_health(self, process_name: str) -> bool:
        """Check if a process is running and healthy"""
        try:
            # Check if process exists
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if process_name in cmdline and 'python' in cmdline:
                    return True
            return False
        except:
            return False
    
    def kill_process(self, process_name: str):
        """Kill processes matching name"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if process_name in cmdline and 'python' in cmdline:
                    self.log(f"🔴 Killing stuck process: {process_name} (PID: {proc.info['pid']})")
                    proc.kill()
                    time.sleep(2)
        except:
            pass
    
    def start_lesson_creator(self):
        """Start the lesson creator with enhanced error handling"""
        self.log("🚀 Starting enhanced lesson creator...")
        self.kill_process("tier1_smart_lesson_creator")
        
        # Start in background
        try:
            process = subprocess.Popen([
                'python3', 'Scripts/tier1_smart_lesson_creator.py'
            ], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid  # Create new process group
            )
            
            self.processes['lesson_creator'] = {
                'process': process,
                'start_time': datetime.now(),
                'last_activity': datetime.now()
            }
            self.log(f"✅ Lesson creator started (PID: {process.pid})")
            return True
        except Exception as e:
            self.log(f"❌ Failed to start lesson creator: {e}")
            return False
    
    def start_audio_generator(self):
        """Start the audio generator"""
        if self.check_process_health("tier1_audio_generator"):
            self.log("✅ Audio generator already running")
            return True
            
        self.log("🎵 Starting Tamil audio generator...")
        try:
            process = subprocess.Popen([
                'python3', 'Scripts/tier1_audio_generator.py'
            ], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid
            )
            
            self.processes['audio_generator'] = {
                'process': process,
                'start_time': datetime.now(),
                'last_activity': datetime.now()
            }
            self.log(f"✅ Audio generator started (PID: {process.pid})")
            return True
        except Exception as e:
            self.log(f"❌ Failed to start audio generator: {e}")
            return False
    
    def check_progress(self) -> Dict[str, Any]:
        """Check overall progress"""
        lesson_count = self.get_lesson_count()
        audio_count = self.get_audio_count()
        
        progress = {
            'lessons': lesson_count,
            'audio': audio_count,
            'lesson_progress': f"{lesson_count}/130",
            'audio_progress': f"{audio_count}/130",
            'lesson_percentage': (lesson_count / 130 * 100) if lesson_count <= 130 else 100,
            'audio_percentage': (audio_count / 130 * 100) if audio_count <= 130 else 100,
            'lessons_increasing': lesson_count > self.last_lesson_count,
            'audio_increasing': audio_count > self.last_audio_count
        }
        
        # Update tracking
        self.last_lesson_count = lesson_count
        self.last_audio_count = audio_count
        
        return progress
    
    def restart_if_needed(self, progress: Dict[str, Any]):
        """Restart processes if they're stuck or failed"""
        current_time = datetime.now()
        
        # Check lesson creator
        if 'lesson_creator' in self.processes:
            proc_info = self.processes['lesson_creator']
            time_since_start = current_time - proc_info['start_time']
            
            # If no progress in 10 minutes, restart
            if (not progress['lessons_increasing'] and 
                time_since_start > timedelta(minutes=10) and
                progress['lessons'] < 130):
                
                self.log("⚠️ Lesson creator appears stuck - restarting...")
                self.kill_process("tier1_smart_lesson_creator")
                time.sleep(3)
                self.start_lesson_creator()
                self.restart_count += 1
        
        # Check if lesson creator isn't running but should be
        if (not self.check_process_health("tier1_smart_lesson_creator") and 
            progress['lessons'] < 130):
            self.log("🔴 Lesson creator not running - starting...")
            self.start_lesson_creator()
    
    def run_until_complete(self):
        """Main monitoring loop - runs until all phases complete"""
        self.log("🤖 AUTOMATED TIER 1 MONITOR STARTED")
        self.log("📊 Will monitor and auto-fix until all 130 Tamil lessons have content and audio")
        self.log(f"🔄 Checking every {self.check_interval} seconds")
        
        # Initial start
        self.start_lesson_creator()
        time.sleep(5)
        self.start_audio_generator()
        
        while True:
            try:
                # Check progress
                progress = self.check_progress()
                
                # Status update
                self.log(f"📊 Progress - Lessons: {progress['lesson_progress']} ({progress['lesson_percentage']:.1f}%) | Audio: {progress['audio_progress']} ({progress['audio_percentage']:.1f}%)")
                
                # Check if complete
                if progress['lessons'] >= 130 and progress['audio'] >= 130:
                    self.log("🎉 ALL PHASES COMPLETE!")
                    self.log("✅ 130/130 Tamil lessons with content and audio")
                    self.log("🏁 Automated monitoring finished successfully")
                    break
                
                # Restart if needed
                if self.restart_count < self.max_restarts:
                    self.restart_if_needed(progress)
                else:
                    self.log(f"⚠️ Max restarts ({self.max_restarts}) reached")
                
                # Process health check
                lesson_healthy = self.check_process_health("tier1_smart_lesson_creator")
                audio_healthy = self.check_process_health("tier1_audio_generator")
                
                self.log(f"💊 Health - Lesson Creator: {'✅' if lesson_healthy else '❌'} | Audio Generator: {'✅' if audio_healthy else '❌'}")
                
                # Wait before next check
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                self.log("⏹️ Manual stop requested")
                break
            except Exception as e:
                self.log(f"❌ Monitor error: {e}")
                time.sleep(30)  # Wait longer if there's an error
        
        # Cleanup
        self.log("🧹 Cleaning up processes...")
        for proc_name in self.processes:
            self.kill_process(proc_name)
        
        self.log("👋 Automated monitoring stopped")

def main():
    monitor = AutoTier1Monitor()
    monitor.run_until_complete()

if __name__ == "__main__":
    main() 