#!/usr/bin/env python3
"""
🎯 TIER 1 SMART LESSON CREATOR
Works with existing learning paths to create comprehensive lesson content
Enhanced with OpenAI backup for maximum reliability
"""

import os
import json
import requests
import time
import uuid
from typing import Dict, List, Any, Tuple
from datetime import datetime
import random
import concurrent.futures
from threading import Lock

# API Configurations
os.environ['GEMINI_API_KEY'] = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'

# Initialize Gemini
try:
    import google.generativeai as genai
    genai.configure(api_key=os.environ['GEMINI_API_KEY'])
    model = genai.GenerativeModel('gemini-2.0-flash-lite')
    print("✅ Gemini 2.0 Flash Lite initialized successfully")
    GEMINI_AVAILABLE = True
except Exception as e:
    print(f"⚠️  Gemini initialization failed: {e}")
    GEMINI_AVAILABLE = False

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Thread-safe counters
api_call_lock = Lock()
api_call_count = 0
gemini_failures = 0
openai_calls = 0
lessons_created = 0

# Tier 1 Languages we're targeting
TIER1_LANGUAGES = {
    'english': 'English', 'spanish': 'Spanish', 'french': 'French', 'german': 'German',
    'italian': 'Italian', 'portuguese': 'Portuguese', 'dutch': 'Dutch', 'russian': 'Russian',
    'chinese': 'Chinese', 'japanese': 'Japanese', 'korean': 'Korean', 'arabic': 'Arabic',
    'hindi': 'Hindi', 'tamil': 'Tamil', 'telugu': 'Telugu', 'kannada': 'Kannada',
    'malayalam': 'Malayalam', 'bengali': 'Bengali', 'marathi': 'Marathi', 
    'punjabi': 'Punjabi', 'gujarati': 'Gujarati'
}

class SmartLessonCreator:
    """Smart lesson creator that works with existing paths"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def clean_json_response(self, response: str) -> str:
        """Clean JSON response by removing markdown formatting and validating JSON"""
        try:
            # Remove markdown code blocks
            cleaned = response.strip()
            if cleaned.startswith('```json'):
                cleaned = cleaned.replace('```json', '').replace('```', '').strip()
            elif cleaned.startswith('```'):
                cleaned = cleaned.replace('```', '').strip()
            
            # Try to parse as JSON to validate
            import json
            json.loads(cleaned)
            return cleaned
            
        except json.JSONDecodeError:
            print(f"    ❌ Invalid JSON response: {response[:200]}")
            return ""
        except Exception as e:
            print(f"    ❌ Error cleaning JSON: {e}")
            return ""
    
    def call_ai_safe(self, prompt: str, language: str = "unknown") -> str:
        """Call Gemini 1.5 Flash with safe error handling"""
        global api_call_count
        
        with api_call_lock:
            api_call_count += 1
            delay = random.uniform(2, 4)  # Slower for stability
            print(f"    🕐 [{language}] API call #{api_call_count}, delay: {delay:.1f}s")
            time.sleep(delay)
        
        # Only use Gemini 1.5 Flash
        if not GEMINI_AVAILABLE or not model:
            print(f"    ❌ Gemini not available for {language}")
            return ""
        
        try:
            response = model.generate_content(prompt)
            if response and response.text and response.text.strip():
                cleaned_content = self.clean_json_response(response.text.strip())
                if cleaned_content:
                    return cleaned_content
                else:
                    print(f"    ⚠️ Gemini returned non-JSON response for {language}")
                    return ""
            else:
                print(f"    ⚠️ Gemini returned empty response for {language}")
                return ""
        except Exception as e:
            print(f"    ❌ Gemini error for {language}: {str(e)[:100]}")
            # Wait longer if rate limited
            if "429" in str(e) or "quota" in str(e).lower():
                print(f"    ⏰ Rate limited, waiting 60s...")
                time.sleep(60)
            return ""
    
    def get_language_paths(self):
        """Get all existing learning paths organized by language and level"""
        print("🔍 Discovering existing learning paths...")
        
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/learning_paths",
            headers=self.headers,
            params={'select': 'id,name,level'}
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch learning paths: {response.status_code}")
            return {}
        
        paths = response.json()
        language_paths = {}
        
        for path in paths:
            name = path.get('name', '').lower()
            level = path.get('level', '').upper()
            
            # Match languages
            for lang_key, lang_name in TIER1_LANGUAGES.items():
                if lang_name.lower() in name:
                    if lang_key not in language_paths:
                        language_paths[lang_key] = {}
                    
                    # Determine level
                    detected_level = None
                    if level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
                        detected_level = level
                    elif 'a1' in name:
                        detected_level = 'A1'
                    elif 'a2' in name:
                        detected_level = 'A2'
                    elif 'b1' in name:
                        detected_level = 'B1'
                    elif 'b2' in name:
                        detected_level = 'B2'
                    elif 'c1' in name:
                        detected_level = 'C1'
                    elif 'c2' in name:
                        detected_level = 'C2'
                    
                    if detected_level:
                        language_paths[lang_key][detected_level] = {
                            'id': path['id'],
                            'name': path['name']
                        }
                        print(f"  📁 {lang_name} {detected_level}: {path['id']}")
                    break
        
        # Summary
        print(f"\n📊 PATH DISCOVERY SUMMARY:")
        for lang_key, levels in language_paths.items():
            lang_name = TIER1_LANGUAGES[lang_key]
            level_count = len(levels)
            level_list = ', '.join(sorted(levels.keys()))
            print(f"  {lang_name}: {level_count} levels ({level_list})")
        
        return language_paths
    
    def check_path_lessons(self, path_id: str) -> int:
        """Check how many lessons exist for a path"""
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={
                'path_id': f'eq.{path_id}',
                'select': 'id'
            }
        )
        
        if response.status_code == 200:
            return len(response.json())
        return 0
    
    def generate_lesson_content(self, language: str, level: str, lesson_num: int) -> bool:
        """Generate lesson content and save to database"""
        # Get language path
        language_paths = self.get_language_paths()
        if language not in language_paths or level not in language_paths[language]:
            print(f"    ❌ No path found for {language} {level}")
            return False
        
        path_id = language_paths[language][level]['id']
        
        # Generate content prompt
        prompt = self.get_lesson_prompt(language, level, lesson_num)
        
        # Get content using improved AI call
        content_json = self.call_ai_safe(prompt, f"{language}-{level}")
        if not content_json:
            print(f"    ❌ Failed to generate content for {language} {level} lesson {lesson_num}")
            return False
        
        try:
            import json
            content_data = json.loads(content_json)
            
            # Create lesson structure
            lesson_data = {
                'path_id': path_id,
                'title': content_data.get('title', f'{language} {level} Lesson {lesson_num}'),
                'description': content_data.get('description', f'Comprehensive {language} lesson for {level} level'),
                'difficulty_level': {'A1': 1, 'A2': 2, 'B1': 3, 'B2': 4, 'C1': 5, 'C2': 6}.get(level, 1),
                'lesson_type': 'comprehensive',
                'sequence_order': lesson_num,
                'estimated_duration': 45,
                'learning_objectives': [
                    f"Master {language} {level} vocabulary and expressions",
                    f"Understand {language} grammar concepts",
                    f"Practice {language} conversations",
                    f"Complete {language} exercises successfully"
                ],
                'vocabulary_focus': None,
                'grammar_concepts': [g.get('rule', 'Grammar') for g in content_data.get('grammar_points', [])[:3]],
                'cultural_notes': f"Authentic {language} cultural context and usage",
                'prerequisite_lessons': [],
                'content_metadata': {
                    'title': content_data.get('title', f'{language} {level} Lesson {lesson_num}'),
                    'description': content_data.get('description', f'Comprehensive {language} lesson for {level} level'),
                    'vocabulary': content_data.get('vocabulary', []),
                    'conversations': content_data.get('conversations', []),
                    'grammar_points': content_data.get('grammar_points', []),
                    'exercises': content_data.get('exercises', []),
                    'estimated_duration': 45,
                    'learning_objectives': [
                        f"Master {language} {level} vocabulary and expressions",
                        f"Understand {language} grammar concepts",
                        f"Practice {language} conversations",
                        f"Complete {language} exercises successfully"
                    ]
                },
                'is_active': True,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'audio_url': None,
                'audio_metadata': {},
                'has_audio': False
            }
            
            # Save to database
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=self.headers,
                json=lesson_data
            )
            
            if response.status_code in [201, 200]:
                global lessons_created
                with api_call_lock:
                    lessons_created += 1
                print(f"    ✅ Created: {content_data.get('title', 'Unknown title')}")
                return True
            else:
                print(f"    ❌ Failed to save lesson: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"    ❌ Error processing lesson: {e}")
            return False
    
    def get_lesson_prompt(self, language: str, level: str, lesson_num: int) -> str:
        """Generate lesson creation prompt"""
        # Get level requirements
        level_requirements = {
            'A1': {'vocabulary': 25, 'conversations': 15, 'grammar': 10, 'exercises': 15},
            'A2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
            'B1': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
            'B2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
            'C1': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
            'C2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15}
        }
        
        requirements = level_requirements.get(level, level_requirements['A1'])
        
        return f"""
Create a comprehensive {language} language lesson for {level} level (lesson {lesson_num}).

Requirements:
- Generate an engaging English title
- Create {requirements['vocabulary']} vocabulary items with translations and pronunciations
- Develop {requirements['conversations']} conversation exchanges between speakers
- Design {requirements['grammar']} grammar points with explanations and examples
- Create {requirements['exercises']} interactive exercises with multiple choice options

Return as JSON with this exact structure:
{{
    "title": "English lesson title",
    "description": "Brief lesson description in English",
    "vocabulary": [
        {{"word": "{language} word", "pronunciation": "pronunciation", "translation": "English translation", "example": "{language} example - English translation"}}
    ],
    "conversations": [
        {{
            "title": "Conversation title",
            "exchanges": [
                {{"speaker": "Speaker A", "text": "{language} text", "translation": "English translation"}},
                {{"speaker": "Speaker B", "text": "{language} text", "translation": "English translation"}}
            ]
        }}
    ],
    "grammar_points": [
        {{"rule": "Grammar rule name", "explanation": "English explanation", "examples": ["{language} example 1", "{language} example 2"]}}
    ],
    "exercises": [
        {{"question": "English question", "options": ["{language} option 1", "{language} option 2", "{language} option 3", "{language} option 4"], "correct": 0}}
    ]
}}

Focus on practical, culturally authentic content appropriate for {level} level.
"""
    
    def process_language_level(self, language: str, level: str, path_info: Dict) -> Dict:
        """Process all lessons for a language-level combination"""
        path_id = path_info['id']
        path_name = path_info['name']
        
        print(f"\n  🚀 Processing {language} {level}")
        print(f"  📁 Path: {path_name}")
        
        # Check existing lessons
        existing_count = self.check_path_lessons(path_id)
        print(f"  📚 Existing lessons: {existing_count}")
        
        # Determine how many lessons to create
        target_counts = {'A1': 30, 'A2': 30, 'B1': 20, 'B2': 20, 'C1': 15, 'C2': 15}
        target = target_counts.get(level, 20)
        needed = max(0, target - existing_count)
        
        if needed == 0:
            print(f"  ✅ Complete: {existing_count}/{target} lessons")
            return {'lessons_created': 0, 'total_lessons': existing_count, 'target': target}
        
        print(f"  🎯 Creating {needed} lessons ({existing_count + needed}/{target})")
        
        # Create lessons
        created_count = 0
        for i in range(existing_count + 1, existing_count + needed + 1):
            if self.generate_lesson_content(language, level, i):
                created_count += 1
            
            # Brief pause
            time.sleep(1)
            
            # Progress update
            if created_count % 5 == 0:
                print(f"  📊 Progress: {created_count}/{needed} lessons created")
        
        print(f"  🎉 {language} {level} Complete: {created_count}/{needed} new lessons")
        return {'lessons_created': created_count, 'total_lessons': existing_count + created_count, 'target': target}
    
    def run_smart_creation(self, max_workers: int = 2):
        """Run smart lesson creation for all available paths"""
        print("🎯 TIER 1 SMART LESSON CREATOR")
        print("="*70)
        print(f"Creating lessons for existing learning paths")
        print(f"Using AI Models: Gemini 2.0 Flash Lite")
        print()
        
        start_time = datetime.now()
        
        # Get all language paths
        language_paths = self.get_language_paths()
        
        if not language_paths:
            print("❌ No language paths found")
            return
        
        # Process languages in small batches
        all_results = {}
        
        for language, levels in language_paths.items():
            if language not in TIER1_LANGUAGES:
                continue
                
            print(f"\n🌍 PROCESSING {TIER1_LANGUAGES[language].upper()}")
            print("="*60)
            
            language_results = {}
            
            # Process each level for this language
            for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
                if level in levels:
                    try:
                        result = self.process_language_level(language, level, levels[level])
                        language_results[level] = result
                        
                        # Short pause between levels
                        time.sleep(5)
                        
                    except Exception as e:
                        print(f"  ❌ Error processing {language} {level}: {e}")
                        language_results[level] = {'error': str(e)}
                else:
                    print(f"  ⚠️  No path found for {language} {level}")
                    language_results[level] = {'error': 'No path found'}
            
            all_results[language] = language_results
            
            # Pause between languages
            print(f"⏰ {TIER1_LANGUAGES[language]} complete. Waiting 10s...")
            time.sleep(10)
        
        # Generate final report
        end_time = datetime.now()
        duration = end_time - start_time
        
        self.generate_final_report(all_results, duration)
    
    def generate_final_report(self, all_results: Dict, duration):
        """Generate comprehensive final report"""
        global lessons_created, gemini_failures, openai_calls
        
        print("\n" + "="*70)
        print("🎉 SMART LESSON CREATION COMPLETE!")
        print("="*70)
        print(f"⏰ Total Duration: {duration}")
        print(f"🔧 Total API Calls: {api_call_count}")
        print(f"🤖 Gemini Failures: {gemini_failures}")
        print(f"🔄 OpenAI Backup Calls: {openai_calls}")
        print(f"📚 Total Lessons Created: {lessons_created}")
        print()
        
        print("📊 RESULTS BY LANGUAGE:")
        print("-" * 70)
        print(f"{'Language':<15} | {'A1':<4} | {'A2':<4} | {'B1':<4} | {'B2':<4} | {'C1':<4} | {'C2':<4} | {'Total'}")
        print("-" * 70)
        
        total_lessons_created = 0
        
        for language, levels in all_results.items():
            if language in TIER1_LANGUAGES:
                lang_name = TIER1_LANGUAGES[language]
                row = f"{lang_name:<15} |"
                
                lang_total = 0
                for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
                    if level in levels and 'lessons_created' in levels[level]:
                        created = levels[level]['lessons_created']
                        row += f" {created:^3} |"
                        lang_total += created
                    else:
                        row += f" {'?':^3} |"
                
                row += f" {lang_total:^5}"
                print(row)
                total_lessons_created += lang_total
        
        print("-" * 70)
        print(f"📈 GRAND TOTALS:")
        print(f"✅ Total New Lessons: {total_lessons_created}")
        print(f"🤖 API Success Rate: {((api_call_count - gemini_failures)/max(api_call_count, 1)*100):.1f}%")
        
        if total_lessons_created >= 100:
            print("\n🎉 EXCELLENT PROGRESS!")
            print("✅ Significant lesson content created")
            print("✅ Ready for quality validation and audio generation")
        else:
            print("\n🔄 GOOD START!")
            print("📚 Continue running to create more lessons")

def main():
    """Main execution function"""
    creator = SmartLessonCreator()
    
    # Run with conservative parallelism
    creator.run_smart_creation(max_workers=2)

if __name__ == "__main__":
    main() 