#!/usr/bin/env python3
"""
📊 TAMIL PHASES REAL-TIME MONITOR
Continuously monitors Tamil completion across all 4 phases
"""

import requests
import time
import json
from datetime import datetime, timedelta

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def get_tamil_paths():
    """Get all Tamil learning paths"""
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/learning_paths",
        headers=headers,
        params={'select': 'id,name,level', 'limit': '1000'}
    )
    
    paths = {}
    if response.status_code == 200:
        for path in response.json():
            name = path['name'].lower()
            if 'tamil' in name:
                level = None
                if 'a1' in name:
                    level = 'A1'
                elif 'a2' in name or 'intermediate' in name:
                    level = 'A2'
                elif 'b1' in name:
                    level = 'B1'
                elif 'b2' in name:
                    level = 'B2'
                elif 'c1' in name:
                    level = 'C1'
                elif 'c2' in name:
                    level = 'C2'
                
                if level and level not in paths:
                    paths[level] = path['id']
    
    return paths

def get_lesson_details(path_id):
    """Get detailed lesson info including audio status"""
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'select': 'id,title,content_metadata,created_at',
            'path_id': f'eq.{path_id}',
            'order': 'created_at.desc'
        }
    )
    
    lessons = []
    audio_count = 0
    
    if response.status_code == 200:
        lessons = response.json()
        for lesson in lessons:
            content = lesson.get('content_metadata', {})
            if content.get('audio_urls'):
                audio_count += 1
    
    return len(lessons), audio_count, lessons

def get_phase_status():
    """Get current status of all 4 phases"""
    paths = get_tamil_paths()
    
    if not paths:
        return None
    
    total_lessons = 0
    total_audio = 0
    total_expected = 130
    
    level_status = {}
    targets = {'A1': 30, 'A2': 30, 'B1': 20, 'B2': 20, 'C1': 15, 'C2': 15}
    
    for level, path_id in paths.items():
        if level in targets:
            lesson_count, audio_count, recent_lessons = get_lesson_details(path_id)
            level_status[level] = {
                'lessons': lesson_count,
                'audio': audio_count,
                'target': targets[level],
                'recent': recent_lessons[:3]  # 3 most recent
            }
            total_lessons += lesson_count
            total_audio += audio_count
    
    return {
        'total_lessons': total_lessons,
        'total_audio': total_audio,
        'expected': total_expected,
        'lesson_percentage': (total_lessons / total_expected * 100),
        'audio_percentage': (total_audio / total_expected * 100),
        'levels': level_status,
        'phase_1_complete': total_lessons >= total_expected,
        'phase_2_complete': total_audio >= total_expected,
        'all_complete': total_lessons >= total_expected and total_audio >= total_expected
    }

def display_status(status):
    """Display comprehensive status"""
    if not status:
        print("❌ Unable to fetch status")
        return
    
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"\n🎯 TAMIL PHASES STATUS - {timestamp}")
    print("=" * 70)
    
    # Overall progress
    print(f"📊 OVERALL PROGRESS:")
    print(f"  📚 Lessons: {status['total_lessons']}/130 ({status['lesson_percentage']:.1f}%)")
    print(f"  🎵 Audio:   {status['total_audio']}/130 ({status['audio_percentage']:.1f}%)")
    
    # Phase status
    print(f"\n🔄 PHASE STATUS:")
    phase1_status = "✅ COMPLETE" if status['phase_1_complete'] else "🔄 IN PROGRESS"
    phase2_status = "✅ COMPLETE" if status['phase_2_complete'] else "🔄 IN PROGRESS"
    phase3_status = "⏳ WAITING" if not status['phase_2_complete'] else "🔄 READY"
    phase4_status = "⏳ WAITING" if not status['all_complete'] else "🔄 READY"
    
    print(f"  Phase 1 (Lessons):    {phase1_status}")
    print(f"  Phase 2 (Audio):      {phase2_status}")
    print(f"  Phase 3 (Validation): {phase3_status}")
    print(f"  Phase 4 (Optimization): {phase4_status}")
    
    # Level breakdown
    print(f"\n📚 LEVEL BREAKDOWN:")
    for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
        if level in status['levels']:
            info = status['levels'][level]
            lessons_bar = "█" * int(info['lessons'] / info['target'] * 10)
            audio_bar = "█" * int(info['audio'] / info['target'] * 10)
            
            print(f"  {level}: Lessons: {info['lessons']:2d}/{info['target']:2d} [{lessons_bar:10s}] Audio: {info['audio']:2d}/{info['target']:2d} [{audio_bar:10s}]")
    
    # Recent activity
    print(f"\n🕐 RECENT ACTIVITY:")
    recent_found = False
    for level, info in status['levels'].items():
        for lesson in info['recent']:
            if lesson.get('created_at'):
                created = lesson['created_at'][:19]  # Remove timezone
                title = lesson.get('title', 'Untitled')[:40]
                print(f"  📚 {created}: {level} - {title}")
                recent_found = True
    
    if not recent_found:
        print("  No recent activity found")
    
    # Next steps
    print(f"\n🎯 NEXT STEPS:")
    if not status['phase_1_complete']:
        remaining = 130 - status['total_lessons']
        print(f"  📚 Complete {remaining} more lessons")
    elif not status['phase_2_complete']:
        remaining = 130 - status['total_audio']
        print(f"  🎵 Generate audio for {remaining} more lessons")
    elif not status['all_complete']:
        print(f"  ✅ Run quality validation")
    else:
        print(f"  🎉 All phases complete!")

def monitor_continuously():
    """Monitor Tamil phases continuously"""
    print("🎯 TAMIL PHASES REAL-TIME MONITOR")
    print("📊 Monitoring all 4 phases - Press Ctrl+C to stop")
    print("🔄 Updates every 30 seconds")
    
    try:
        while True:
            status = get_phase_status()
            display_status(status)
            
            if status and status['all_complete']:
                print("\n🎉 ALL PHASES COMPLETE! Monitoring stopped.")
                break
            
            print(f"\n⏳ Next update in 30 seconds...")
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped by user")

def main():
    monitor_continuously()

if __name__ == "__main__":
    main() 