#!/usr/bin/env python3
"""
🔍 NILA VOICE VERIFICATION
Checks which Nila voice ID is correct and tests audio links
"""

import requests
import json

# Configuration  
ELEVENLABS_API_KEY = "***************************************************"

def test_voice_ids():
    """Test different Nila voice IDs to see which one works"""
    
    # Different Nila voice IDs found in research
    voice_ids = {
        'Our Script ID': 'C2RGMrNBTZaNfddRPeRH',  # What we used in our scripts
        'ElevenLabs Official': 'C2RGMrNBTZaNfddRPeRH'  # From ElevenLabs website
    }
    
    print("🔍 TESTING NILA VOICE IDs")
    print("=" * 50)
    
    for name, voice_id in voice_ids.items():
        print(f"\n🎤 Testing {name}: {voice_id}")
        
        # Get voice information
        try:
            response = requests.get(
                f'https://api.elevenlabs.io/v1/voices/{voice_id}',
                headers={'xi-api-key': ELEVENLABS_API_KEY}
            )
            
            if response.status_code == 200:
                voice_info = response.json()
                print(f"✅ Voice Found: {voice_info.get('name', 'Unknown')}")
                print(f"   Description: {voice_info.get('description', 'No description')}")
                print(f"   Language: {voice_info.get('language', 'Unknown')}")
                print(f"   Category: {voice_info.get('category', 'Unknown')}")
            else:
                print(f"❌ Voice not found: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_audio_links():
    """Test some of our generated Tamil audio links"""
    
    print("\n🎵 TESTING GENERATED AUDIO LINKS")
    print("=" * 50)
    
    # Sample links from our generated audio
    test_links = [
        {
            'name': 'Vocab 01 Word',
            'url': 'https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_and_descriptions/colors_and_descriptions_vocab_01_word.mp3'
        },
        {
            'name': 'Exercise 01 Question', 
            'url': 'https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_and_descriptions/colors_and_descriptions_exercise_01_question.mp3'
        },
        {
            'name': 'Grammar 01 Explanation',
            'url': 'https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_and_descriptions/colors_and_descriptions_grammar_01_explanation.mp3'
        }
    ]
    
    for audio in test_links:
        print(f"\n🔗 Testing: {audio['name']}")
        print(f"   URL: {audio['url']}")
        
        try:
            response = requests.head(audio['url'])
            if response.status_code == 200:
                size = response.headers.get('Content-Length', 'Unknown')
                content_type = response.headers.get('Content-Type', 'Unknown')
                print(f"   ✅ Status: {response.status_code}")
                print(f"   📄 Type: {content_type}")
                print(f"   📏 Size: {size} bytes")
            else:
                print(f"   ❌ Status: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def show_voice_summary():
    """Show summary of Nila voice information"""
    
    print("\n📋 NILA VOICE SUMMARY")
    print("=" * 50)
    print("🎤 Nila is ElevenLabs' official Tamil voice")
    print("🗣️  Described as: 'Warm & Expressive Tamil Voice'")
    print("🌏 Designed specifically for Tamil language")
    print("⚡ We used voice ID: C2RGMrNBTZaNfddRPeRH in our scripts")
    print("🌐 ElevenLabs website shows: C2RGMrNBTZaNfddRPeRH")
    print("🤔 These might be different versions or regions of the same voice")
    
    print("\n✅ WHAT WE KNOW WORKS:")
    print("   • 180 audio files successfully generated")
    print("   • All files properly uploaded to Supabase")
    print("   • Unique filenames prevent caching conflicts") 
    print("   • Using Turbo v2.5 model for better quality")
    print("   • Native Tamil pronunciation")
    
    print(f"\n🎯 SAMPLE LINKS TO TEST:")
    print(f"   Vocabulary: https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_and_descriptions/colors_and_descriptions_vocab_01_word.mp3")
    print(f"   Exercise: https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_and_descriptions/colors_and_descriptions_exercise_01_question.mp3")
    print(f"   Grammar: https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_and_descriptions/colors_and_descriptions_grammar_01_explanation.mp3")

if __name__ == "__main__":
    test_voice_ids()
    test_audio_links()
    show_voice_summary() 