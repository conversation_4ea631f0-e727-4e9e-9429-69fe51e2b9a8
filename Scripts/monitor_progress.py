#!/usr/bin/env python3
"""
📊 TAMIL LESSON PROGRESS MONITOR
Monitors the current state of Tamil lesson fixes
"""

import requests
import re
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil Learning Path IDs
TAMIL_PATHS = {
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

def is_tamil_text(text: str) -> bool:
    """Check if text contains Tamil characters"""
    if not text:
        return False
    tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
    return bool(tamil_pattern.search(text))

def get_lessons_for_level(level: str) -> List[Dict[str, Any]]:
    """Get all lessons for a specific level"""
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    path_id = TAMIL_PATHS[level]
    
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'path_id': f'eq.{path_id}',
            'select': '*',
            'order': 'sequence_order'
        }
    )
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Failed to fetch {level} lessons: {response.status_code}")
        return []

def analyze_level(level: str) -> Dict[str, Any]:
    """Analyze the current state of a level"""
    lessons = get_lessons_for_level(level)
    
    if not lessons:
        return {'error': 'No lessons found'}
    
    total_lessons = len(lessons)
    tamil_titles = 0
    tamil_descriptions = 0
    empty_content = 0
    has_content = 0
    
    for lesson in lessons:
        title = lesson.get('title', '')
        description = lesson.get('description', '')
        content_metadata = lesson.get('content_metadata', {})
        
        # Check titles
        if is_tamil_text(title):
            tamil_titles += 1
        
        # Check descriptions
        if description and is_tamil_text(description):
            tamil_descriptions += 1
        
        # Check content
        if not content_metadata or content_metadata == {}:
            empty_content += 1
        else:
            vocabulary = content_metadata.get('vocabulary', [])
            conversations = content_metadata.get('conversations', [])
            grammar = content_metadata.get('grammar_points', [])
            exercises = content_metadata.get('exercises', [])
            
            if len(vocabulary) > 0 or len(conversations) > 0 or len(grammar) > 0 or len(exercises) > 0:
                has_content += 1
    
    return {
        'total_lessons': total_lessons,
        'tamil_titles': tamil_titles,
        'tamil_descriptions': tamil_descriptions,
        'english_titles': total_lessons - tamil_titles,
        'empty_content': empty_content,
        'has_content': has_content,
        'title_fix_progress': f"{((total_lessons - tamil_titles) / total_lessons * 100):.1f}%",
        'content_progress': f"{(has_content / total_lessons * 100):.1f}%"
    }

def main():
    """Main monitoring function"""
    print("📊 TAMIL LESSON PROGRESS MONITOR")
    print("=" * 50)
    
    total_stats = {
        'total_lessons': 0,
        'fixed_titles': 0,
        'fixed_descriptions': 0,
        'lessons_with_content': 0
    }
    
    for level in ['A2', 'B1', 'B2', 'C1', 'C2']:
        print(f"\n🔍 {level} LEVEL ANALYSIS:")
        print("-" * 30)
        
        stats = analyze_level(level)
        
        if 'error' in stats:
            print(f"❌ {stats['error']}")
            continue
        
        print(f"📚 Total Lessons: {stats['total_lessons']}")
        print(f"📝 Titles Fixed: {stats['english_titles']}/{stats['total_lessons']} ({stats['title_fix_progress']})")
        print(f"📄 Tamil Titles Remaining: {stats['tamil_titles']}")
        print(f"📋 Tamil Descriptions Remaining: {stats['tamil_descriptions']}")
        print(f"📦 Lessons with Content: {stats['has_content']}/{stats['total_lessons']} ({stats['content_progress']})")
        print(f"🚫 Empty Lessons: {stats['empty_content']}")
        
        # Add to totals
        total_stats['total_lessons'] += stats['total_lessons']
        total_stats['fixed_titles'] += stats['english_titles']
        total_stats['lessons_with_content'] += stats['has_content']
    
    # Overall summary
    print(f"\n📊 OVERALL PROGRESS:")
    print("=" * 50)
    print(f"📚 Total Lessons Across A2-C2: {total_stats['total_lessons']}")
    print(f"✅ Titles Fixed: {total_stats['fixed_titles']}/{total_stats['total_lessons']} ({(total_stats['fixed_titles']/total_stats['total_lessons']*100):.1f}%)")
    print(f"📦 Lessons with Content: {total_stats['lessons_with_content']}/{total_stats['total_lessons']} ({(total_stats['lessons_with_content']/total_stats['total_lessons']*100):.1f}%)")
    
    remaining_titles = total_stats['total_lessons'] - total_stats['fixed_titles']
    if remaining_titles > 0:
        print(f"🔄 Remaining Tamil Titles to Fix: {remaining_titles}")
    else:
        print("🎉 All titles have been fixed!")

if __name__ == "__main__":
    main() 