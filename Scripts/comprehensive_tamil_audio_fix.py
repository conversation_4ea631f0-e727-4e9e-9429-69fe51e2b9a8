#!/usr/bin/env python3
"""
🎵 COMPREHENSIVE TAMIL AUDIO FIX
Regenerates ALL Tamil lesson audio with:
1. Native Tamil voice (Nila)
2. Fixed caching with lesson-specific filenames
3. Comprehensive content (vocab + explanations + exercises + grammar)
4. Better voice settings and Turbo v2.5 model
"""

import os
import json
import requests
import time
from typing import Dict, Any, Optional, List
from Scripts.fixed_tamil_audio_system import FixedTamilAudioSystem

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

SUPABASE_HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

class ComprehensiveTamilAudioFix:
    """Complete Tamil audio regeneration system"""
    
    def __init__(self):
        self.audio_system = FixedTamilAudioSystem()
        self.processed_lessons = []
        self.failed_lessons = []
        self.total_audio_generated = 0
        
    def get_all_tamil_lessons(self) -> List[Dict]:
        """Get all Tamil lessons from all difficulty levels"""
        print("🔍 Discovering Tamil lessons...")
        
        try:
            # First, find Tamil learning paths
            paths_response = requests.get(
                f"{SUPABASE_URL}/rest/v1/learning_paths",
                headers=SUPABASE_HEADERS,
                params={
                    'name': 'like.*Tamil*',
                    'select': 'id,name,level'
                }
            )
            
            if paths_response.status_code != 200:
                print(f"❌ Failed to fetch Tamil paths: {paths_response.status_code}")
                return []
            
            tamil_paths = paths_response.json()
            print(f"📁 Found {len(tamil_paths)} Tamil learning paths")
            
            all_lessons = []
            for path in tamil_paths:
                print(f"  🔍 Checking: {path['name']}")
                
                # Get lessons for this path
                lessons_response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/lessons",
                    headers=SUPABASE_HEADERS,
                    params={
                        'path_id': f"eq.{path['id']}",
                        'select': 'id,title,sequence_order,content_metadata',
                        'order': 'sequence_order'
                    }
                )
                
                if lessons_response.status_code == 200:
                    lessons = lessons_response.json()
                    print(f"    📚 Found {len(lessons)} lessons in {path['name']}")
                    
                    for lesson in lessons:
                        lesson['path_name'] = path['name']
                        lesson['level'] = path.get('level', 'A1')
                        all_lessons.append(lesson)
                else:
                    print(f"    ❌ Failed to fetch lessons for {path['name']}")
            
            print(f"✅ Total Tamil lessons found: {len(all_lessons)}")
            return all_lessons
            
        except Exception as e:
            print(f"❌ Error fetching Tamil lessons: {e}")
            return []
    
    def regenerate_all_audio(self, limit: Optional[int] = None):
        """Regenerate audio for all Tamil lessons"""
        print("🎵 COMPREHENSIVE TAMIL AUDIO REGENERATION")
        print("=" * 60)
        print("🎯 Using NATIVE Tamil voice (Nila)")
        print("🚀 Turbo v2.5 model for better quality")
        print("✅ Fixed caching with lesson-specific filenames")
        print("🎵 Comprehensive content generation")
        print("=" * 60)
        
        lessons = self.get_all_tamil_lessons()
        if not lessons:
            print("❌ No Tamil lessons found")
            return
        
        # Apply limit if specified
        if limit:
            lessons = lessons[:limit]
            print(f"🎯 Processing first {limit} lessons for testing")
        
        print(f"\n🚀 Starting audio generation for {len(lessons)} lessons...")
        
        for i, lesson in enumerate(lessons, 1):
            print(f"\n{'='*20} LESSON {i}/{len(lessons)} {'='*20}")
            print(f"📚 {lesson['title']}")
            print(f"📁 Path: {lesson['path_name']}")
            print(f"🎯 Level: {lesson['level']}")
            
            try:
                # Generate comprehensive audio for this lesson
                audio_urls = self.audio_system.generate_comprehensive_audio(lesson['id'])
                
                if audio_urls:
                    # Update lesson with audio URLs
                    if self.audio_system.update_lesson_audio_urls(lesson['id'], audio_urls):
                        self.processed_lessons.append({
                            'id': lesson['id'],
                            'title': lesson['title'],
                            'path': lesson['path_name'],
                            'audio_count': len(audio_urls)
                        })
                        self.total_audio_generated += len(audio_urls)
                        
                        print(f"✅ SUCCESS: {len(audio_urls)} audio files generated")
                    else:
                        print(f"⚠️  Audio generated but database update failed")
                else:
                    self.failed_lessons.append({
                        'id': lesson['id'],
                        'title': lesson['title'],
                        'path': lesson['path_name'],
                        'error': 'No audio generated'
                    })
                    print(f"❌ FAILED: No audio generated")
                
                # Rate limiting between lessons
                print(f"⏱️  Waiting 10 seconds before next lesson...")
                time.sleep(10)
                
            except Exception as e:
                print(f"❌ ERROR processing lesson: {e}")
                self.failed_lessons.append({
                    'id': lesson['id'],
                    'title': lesson['title'],
                    'path': lesson['path_name'],
                    'error': str(e)
                })
        
        self.print_final_summary()
    
    def print_final_summary(self):
        """Print final summary of the regeneration process"""
        print("\n" + "="*60)
        print("🎉 TAMIL AUDIO REGENERATION COMPLETE")
        print("="*60)
        
        print(f"📊 SUMMARY:")
        print(f"  ✅ Successful lessons: {len(self.processed_lessons)}")
        print(f"  ❌ Failed lessons: {len(self.failed_lessons)}")
        print(f"  🎵 Total audio files: {self.total_audio_generated}")
        print(f"  🗣️  Voice used: Nila (Native Tamil)")
        print(f"  🚀 Model used: Turbo v2.5")
        
        if self.processed_lessons:
            print(f"\n✅ SUCCESSFUL LESSONS:")
            for lesson in self.processed_lessons:
                print(f"  📚 {lesson['title']} ({lesson['audio_count']} audio files)")
        
        if self.failed_lessons:
            print(f"\n❌ FAILED LESSONS:")
            for lesson in self.failed_lessons:
                print(f"  📚 {lesson['title']} - {lesson['error']}")
        
        # Save detailed results
        results = {
            'summary': {
                'successful_lessons': len(self.processed_lessons),
                'failed_lessons': len(self.failed_lessons),
                'total_audio_generated': self.total_audio_generated,
                'voice_used': 'Nila (C2RGMrNBTZaNfddRPeRH)',
                'model_used': 'eleven_turbo_v2_5'
            },
            'successful_lessons': self.processed_lessons,
            'failed_lessons': self.failed_lessons
        }
        
        with open('comprehensive_tamil_audio_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📁 Detailed results saved to: comprehensive_tamil_audio_results.json")
        
        # Test instructions
        print(f"\n🧪 TESTING INSTRUCTIONS:")
        print(f"1. Open your app and navigate to any Tamil lesson")
        print(f"2. Try playing vocabulary words - should hear native Tamil voice")
        print(f"3. Check different lessons - should hear unique audio (no caching conflicts)")
        print(f"4. Try exercises and explanations - should have audio for all content")
        print(f"5. Verify audio quality - should sound natural, not like English speaker")

def main():
    """Main function with options"""
    print("🎵 COMPREHENSIVE TAMIL AUDIO FIX")
    print("Choose an option:")
    print("1. Test with 3 lessons (recommended for testing)")
    print("2. Process ALL Tamil lessons (full regeneration)")
    print("3. Process specific lesson by ID")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    audio_fix = ComprehensiveTamilAudioFix()
    
    if choice == "1":
        print("\n🧪 Testing with first 3 lessons...")
        audio_fix.regenerate_all_audio(limit=3)
    elif choice == "2":
        print("\n🚀 Processing ALL Tamil lessons...")
        confirm = input("This will regenerate audio for ALL Tamil lessons. Continue? (y/N): ").strip().lower()
        if confirm == 'y':
            audio_fix.regenerate_all_audio()
        else:
            print("❌ Cancelled")
    elif choice == "3":
        lesson_id = input("Enter lesson ID: ").strip()
        if lesson_id:
            print(f"\n🎯 Processing single lesson: {lesson_id}")
            audio_urls = audio_fix.audio_system.generate_comprehensive_audio(lesson_id)
            if audio_urls:
                audio_fix.audio_system.update_lesson_audio_urls(lesson_id, audio_urls)
                print(f"✅ Generated {len(audio_urls)} audio files")
            else:
                print("❌ Failed to generate audio")
        else:
            print("❌ No lesson ID provided")
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main() 