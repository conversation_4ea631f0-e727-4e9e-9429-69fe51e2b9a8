#!/usr/bin/env python3
"""
Scalable Multi-Language Creator for NIRA
Creates A1-C2 lessons for all Tier 1 languages in parallel

This script enables parallel processing across multiple terminals for:
- 21 Tier 1 languages
- A1-C2 levels (A1: 30, A2: 30, B1: 20, B2: 20, C1: 15, C2: 15 lessons each)
- Systematic approach with quality validation
- English titles (fixing the Tamil title issue)

Tier 1 Languages:
English, Spanish, French, German, Italian, Portuguese, Chinese, Japanese, Korean, Arabic,
Hindi, Tamil, Telugu, Kannada, Malayalam, Bengali, Marathi, Punjabi, Gujarati, Russian, Dutch
"""

import json
import requests
import time
import subprocess
import sys
import os
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tier 1 Languages Configuration
TIER1_LANGUAGES = {
    'english': {'code': 'en', 'name': 'English'},
    'spanish': {'code': 'es', 'name': 'Spanish'},
    'french': {'code': 'fr', 'name': 'French'},
    'german': {'code': 'de', 'name': 'German'},
    'italian': {'code': 'it', 'name': 'Italian'},
    'portuguese': {'code': 'pt', 'name': 'Portuguese'},
    'chinese': {'code': 'zh', 'name': 'Chinese'},
    'japanese': {'code': 'ja', 'name': 'Japanese'},
    'korean': {'code': 'ko', 'name': 'Korean'},
    'arabic': {'code': 'ar', 'name': 'Arabic'},
    'hindi': {'code': 'hi', 'name': 'Hindi'},
    'tamil': {'code': 'ta', 'name': 'Tamil'},
    'telugu': {'code': 'te', 'name': 'Telugu'},
    'kannada': {'code': 'kn', 'name': 'Kannada'},
    'malayalam': {'code': 'ml', 'name': 'Malayalam'},
    'bengali': {'code': 'bn', 'name': 'Bengali'},
    'marathi': {'code': 'mr', 'name': 'Marathi'},
    'punjabi': {'code': 'pa', 'name': 'Punjabi'},
    'gujarati': {'code': 'gu', 'name': 'Gujarati'},
    'russian': {'code': 'ru', 'name': 'Russian'},
    'dutch': {'code': 'nl', 'name': 'Dutch'}
}

# Level Configuration
LEVEL_CONFIG = {
    'A1': {'difficulty_level': 1, 'lesson_count': 30, 'duration': 30},
    'A2': {'difficulty_level': 2, 'lesson_count': 30, 'duration': 45},
    'B1': {'difficulty_level': 3, 'lesson_count': 20, 'duration': 60},
    'B2': {'difficulty_level': 4, 'lesson_count': 20, 'duration': 75},
    'C1': {'difficulty_level': 5, 'lesson_count': 15, 'duration': 90},
    'C2': {'difficulty_level': 6, 'lesson_count': 15, 'duration': 120}
}

class ScalableMultiLanguageCreator:
    """Scalable creator for all Tier 1 languages"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def create_learning_paths(self, language_key: str) -> Dict[str, str]:
        """Create learning paths for all levels of a language"""
        
        language_info = TIER1_LANGUAGES[language_key]
        language_name = language_info['name']
        language_code = language_info['code']
        
        print(f"\n🌍 Creating learning paths for {language_name}")
        
        paths = {}
        
        for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
            path_data = {
                'name': f'{language_name} {level} Course',
                'level': level,
                'description': f'Complete {level} level course for {language_name} language learning',
                'language_code': language_code,
                'sequence_order': ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'].index(level) + 1,
                'is_active': True,
                'estimated_duration': LEVEL_CONFIG[level]['lesson_count'] * LEVEL_CONFIG[level]['duration']
            }
            
            try:
                response = requests.post(
                    f"{SUPABASE_URL}/rest/v1/learning_paths",
                    headers=self.headers,
                    json=path_data
                )
                
                if response.status_code == 201:
                    path_id = response.json()[0]['id']
                    paths[level] = path_id
                    print(f"✅ Created {language_name} {level} path: {path_id}")
                else:
                    print(f"❌ Failed to create {language_name} {level} path: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error creating {language_name} {level} path: {e}")
        
        return paths
    
    def generate_english_lesson_titles(self, level: str, language_name: str) -> List[str]:
        """Generate English lesson titles for any language and level"""
        
        config = LEVEL_CONFIG[level]
        lesson_count = config['lesson_count']
        
        prompt = f"""
        Generate exactly {lesson_count} lesson titles in ENGLISH for {language_name} {level} level.
        
        Requirements:
        - Titles must be in ENGLISH (not in {language_name})
        - {level} difficulty level appropriate topics
        - Progressive complexity from basic to advanced within the level
        - Practical, real-world topics
        - Cultural relevance to {language_name} speakers
        - No duplicates
        
        For {level} level, focus on:
        {self._get_level_focus(level)}
        
        Return as a simple numbered list:
        1. English Title
        2. English Title
        ...
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            titles = []
            
            for line in response.text.strip().split('\n'):
                if line.strip() and '. ' in line:
                    title = line.split('. ', 1)[1].strip()
                    if title:
                        titles.append(title)
            
            print(f"✅ Generated {len(titles)} English lesson titles for {language_name} {level}")
            return titles[:lesson_count]  # Ensure exact count
            
        except Exception as e:
            print(f"❌ Failed to generate titles for {language_name} {level}: {e}")
            return []
    
    def _get_level_focus(self, level: str) -> str:
        """Get focus areas for each level"""
        focus_areas = {
            'A1': 'Basic greetings, family, numbers, colors, food, daily activities, simple conversations',
            'A2': 'Complex daily activities, workplace basics, travel, shopping, health, education, hobbies',
            'B1': 'Professional contexts, abstract concepts, opinions, future plans, cultural topics, media',
            'B2': 'Specialized fields, academic topics, business, technology, social issues, literature',
            'C1': 'Professional expertise, academic research, complex analysis, cultural criticism, philosophy',
            'C2': 'Literary mastery, classical texts, linguistic theory, cultural heritage, expert discourse'
        }
        return focus_areas.get(level, 'General topics')
    
    def create_language_script(self, language_key: str) -> str:
        """Create a dedicated script for a specific language"""
        
        language_info = TIER1_LANGUAGES[language_key]
        language_name = language_info['name']
        
        script_content = f'''#!/usr/bin/env python3
"""
{language_name} Multi-Level Creator
Creates A1-C2 lessons for {language_name}
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scalable_multilanguage_creator import ScalableMultiLanguageCreator

def main():
    print("🚀 {language_name.upper()} MULTI-LEVEL CREATOR")
    print("=" * 60)
    
    creator = ScalableMultiLanguageCreator()
    
    # Create learning paths
    paths = creator.create_learning_paths('{language_key}')
    
    if not paths:
        print("❌ Failed to create learning paths")
        return
    
    print(f"\\n📊 {language_name.upper()} RESULTS:")
    for level, path_id in paths.items():
        print(f"  ✅ {level}: {path_id}")
    
    print(f"\\n🎉 {language_name} learning paths created successfully!")
    print("📋 Next: Run lesson structure and content generation")

if __name__ == "__main__":
    main()
'''
        
        script_path = f"Scripts/create_{language_key}_paths.py"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(script_path, 0o755)
        
        return script_path
    
    def create_all_language_scripts(self) -> List[str]:
        """Create scripts for all Tier 1 languages"""
        
        print("\n🛠️  CREATING LANGUAGE-SPECIFIC SCRIPTS")
        print("=" * 60)
        
        scripts = []
        
        for language_key in TIER1_LANGUAGES.keys():
            if language_key != 'tamil':  # Skip Tamil as it's already done
                script_path = self.create_language_script(language_key)
                scripts.append(script_path)
                print(f"✅ Created script: {script_path}")
        
        return scripts
    
    def run_parallel_creation(self, max_parallel: int = 10) -> None:
        """Run parallel creation for multiple languages"""
        
        print(f"\n🚀 STARTING PARALLEL CREATION ({max_parallel} terminals)")
        print("=" * 60)
        
        # Create all scripts first
        scripts = self.create_all_language_scripts()
        
        # Run scripts in parallel batches
        processes = []
        
        for i, script in enumerate(scripts):
            if i >= max_parallel:
                break
                
            language_key = script.split('_')[1]  # Extract language from filename
            language_name = TIER1_LANGUAGES[language_key]['name']
            
            print(f"🌍 Starting {language_name} creation...")
            
            try:
                process = subprocess.Popen([
                    sys.executable, script
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                
                processes.append({
                    'process': process,
                    'language': language_name,
                    'script': script
                })
                
            except Exception as e:
                print(f"❌ Failed to start {language_name}: {e}")
        
        # Monitor processes
        print(f"\\n📊 Monitoring {len(processes)} parallel processes...")
        
        completed = 0
        while completed < len(processes):
            for proc_info in processes:
                if proc_info['process'].poll() is not None and 'completed' not in proc_info:
                    proc_info['completed'] = True
                    completed += 1
                    
                    stdout, stderr = proc_info['process'].communicate()
                    
                    if proc_info['process'].returncode == 0:
                        print(f"✅ {proc_info['language']} completed successfully")
                    else:
                        print(f"❌ {proc_info['language']} failed: {stderr}")
            
            time.sleep(2)
        
        print(f"\\n🎉 Parallel creation completed!")

def main():
    """Main function for scalable multi-language creation"""
    
    print("🌍 SCALABLE MULTI-LANGUAGE CREATOR")
    print("Creating learning paths for all Tier 1 languages")
    print("=" * 60)
    
    creator = ScalableMultiLanguageCreator()
    
    print("\\n📋 Tier 1 Languages:")
    for i, (key, info) in enumerate(TIER1_LANGUAGES.items(), 1):
        status = "✅ Complete" if key == 'tamil' else "🔄 Pending"
        print(f"  {i:2d}. {info['name']} ({info['code']}) - {status}")
    
    print(f"\\n🎯 Total: {len(TIER1_LANGUAGES)} languages")
    print("📊 Per language: 130 lessons (A1:30, A2:30, B1:20, B2:20, C1:15, C2:15)")
    print(f"🎉 Grand total: {len(TIER1_LANGUAGES) * 130} lessons across all languages")
    
    # Ask user for confirmation
    response = input("\\n🚀 Start parallel creation for all languages? (y/N): ").strip().lower()
    
    if response == 'y':
        creator.run_parallel_creation(max_parallel=10)
    else:
        print("\\n📋 Available options:")
        print("1. Run individual language scripts manually")
        print("2. Modify max_parallel setting")
        print("3. Create scripts only (no execution)")
        
        choice = input("\\nEnter choice (1-3): ").strip()
        
        if choice == '3':
            creator.create_all_language_scripts()
            print("\\n✅ Scripts created. Run them manually when ready.")

if __name__ == "__main__":
    main()
