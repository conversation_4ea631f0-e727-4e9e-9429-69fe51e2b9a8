#!/usr/bin/env python3
"""
🧹 CLEAR WRONG TAMIL AUDIO
Removes all Tamil audio files that use the wrong English voice
Keeps only the Basic Greetings lesson (which has correct Nila voice)
"""

import requests
import json

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

# Lessons to clear (all except Basic Greetings)
LESSONS_TO_CLEAR = [
    {
        'id': 'd5ef89df-5cd2-453e-8809-502440812f5d',
        'title': 'Colors and Descriptions',
        'files': 180
    },
    {
        'id': '32f42389-39a6-4b63-89e9-2e0e1b8f1c5d',
        'title': 'Family Members and Relationships', 
        'files': 192
    },
    {
        'id': 'bb85ec92-1ed1-4f3a-8a2f-3f42c6a8e9d7',
        'title': 'Directions and Locations',
        'files': 192
    }
    # Add others as needed
]

def clear_lesson_audio(lesson_id: str, lesson_title: str) -> bool:
    """Clear audio URLs from a specific lesson"""
    
    try:
        print(f"🧹 Clearing audio from: {lesson_title}")
        
        # Update lesson to remove audio_urls
        response = requests.patch(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=HEADERS,
            params={'id': f'eq.{lesson_id}'},
            json={
                'content_metadata': {}  # This will clear audio_urls
            }
        )
        
        if response.status_code in [200, 204]:
            print(f"✅ Cleared audio URLs from: {lesson_title}")
            return True
        else:
            print(f"❌ Failed to clear {lesson_title}: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error clearing {lesson_title}: {e}")
        return False

def clear_all_wrong_audio():
    """Clear all Tamil audio except Basic Greetings"""
    
    print("🧹 CLEARING WRONG TAMIL AUDIO")
    print("=" * 50)
    print("🎯 Goal: Remove all English-accented Tamil audio")
    print("✅ Keep: Basic Greetings (correct Nila voice)")
    print("❌ Clear: All other lessons with wrong voice")
    print("=" * 50)
    
    cleared_count = 0
    
    # Get all Tamil A1 lessons with audio
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=HEADERS,
            params={
                'path_id': 'eq.6b427613-420f-4586-bce8-2773d722f0b4',
                'select': 'id,title,content_metadata',
                'order': 'sequence_order.asc'
            }
        )
        
        if response.status_code == 200:
            lessons = response.json()
            
            for lesson in lessons:
                lesson_id = lesson['id']
                lesson_title = lesson['title']
                content_metadata = lesson.get('content_metadata', {})
                audio_urls = content_metadata.get('audio_urls', {})
                
                # Skip Basic Greetings (keep the correct audio)
                if lesson_title == "Basic Greetings and Introductions":
                    print(f"✅ Keeping: {lesson_title} (correct Nila voice)")
                    continue
                
                # Clear lessons with audio
                if audio_urls:
                    print(f"❌ Found wrong audio in: {lesson_title} ({len(audio_urls)} files)")
                    if clear_lesson_audio(lesson_id, lesson_title):
                        cleared_count += 1
                else:
                    print(f"⚪ No audio in: {lesson_title}")
        
        else:
            print(f"❌ Failed to fetch lessons: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Error fetching lessons: {e}")
    
    print(f"\n✅ CLEARED {cleared_count} lessons with wrong audio")
    print(f"✅ Basic Greetings lesson preserved (correct Nila voice)")
    print(f"\n🎯 RESULT:")
    print(f"- No more English accent in Tamil audio!")
    print(f"- Only Basic Greetings has audio (proper Tamil)")
    print(f"- Other lessons need new audio generation (when approved)")

if __name__ == "__main__":
    
    print("\n⚠️  CONFIRMATION REQUIRED")
    print("This will remove audio from all Tamil lessons except Basic Greetings")
    print("Type 'YES' to proceed: ", end="")
    
    confirmation = input().strip()
    if confirmation == "YES":
        clear_all_wrong_audio()
    else:
        print("❌ Operation cancelled") 