#!/usr/bin/env python3
"""
Emergency Audio Fix for Tamil A1
Fixes broken audio URLs and regenerates audio files
"""

import requests
import json
import time
import os

# Configuration
ELEVENLABS_API_KEY = "sk_b3f4c8c9c8a4b8f4e8d9c8b4a8f4e8d9c8b4a8f4e8d9c8b4a8f4e8d9c8b4a8f4"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Approved voices for Tamil
VOICES = {
    'female': 'pNInz6obpgDQGcFmaJgB',  # Adam (multilingual)
    'male': 'EXAVITQu4vr4xnSDxMaL'     # Bella (multilingual)
}

def find_tamil_a1_lessons():
    """Find Tamil A1 lessons"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    # Try to find Tamil A1 path
    paths_response = requests.get(
        f"{SUPABASE_URL}/rest/v1/learning_paths",
        headers=headers,
        params={
            'name': 'like.*Tamil*A1*',
            'select': 'id,name,level'
        }
    )
    
    if paths_response.status_code == 200:
        paths = paths_response.json()
        print(f"Found {len(paths)} Tamil A1 paths")
        
        for path in paths:
            print(f"  - {path['name']} (ID: {path['id']})")
            
            # Get lessons for this path
            lessons_response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=headers,
                params={
                    'path_id': f'eq.{path["id"]}',
                    'select': 'id,title,sequence_order,content_metadata',
                    'order': 'sequence_order'
                }
            )
            
            if lessons_response.status_code == 200:
                lessons = lessons_response.json()
                print(f"    Found {len(lessons)} lessons")
                
                # Look for Animals and Nature
                for lesson in lessons:
                    if 'animals' in lesson['title'].lower() and 'nature' in lesson['title'].lower():
                        print(f"    🎯 Found Animals and Nature: {lesson['title']}")
                        return lesson, headers
    
    return None, None

def generate_audio_elevenlabs(text, voice_id):
    """Generate audio using ElevenLabs"""
    
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
    
    headers = {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': ELEVENLABS_API_KEY
    }
    
    data = {
        'text': text,
        'model_id': 'eleven_multilingual_v2',
        'voice_settings': {
            'stability': 0.5,
            'similarity_boost': 0.5
        }
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 200:
            return response.content
        else:
            print(f"ElevenLabs API error: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Audio generation error: {e}")
        return None

def upload_to_supabase(audio_data, file_path):
    """Upload audio to Supabase storage"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'audio/mpeg'
    }
    
    try:
        # Try different upload approaches
        upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{file_path}"
        
        response = requests.post(upload_url, data=audio_data, headers=headers)
        
        if response.status_code in [200, 201]:
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{file_path}"
            return public_url
        else:
            print(f"Upload failed: {response.status_code} - {response.text}")
            
            # Try PUT method
            response = requests.put(upload_url, data=audio_data, headers=headers)
            if response.status_code in [200, 201]:
                public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{file_path}"
                return public_url
            else:
                print(f"PUT upload also failed: {response.status_code}")
                return None
                
    except Exception as e:
        print(f"Upload error: {e}")
        return None

def fix_animals_nature_audio():
    """Fix audio for Animals and Nature lesson"""
    
    print("🚨 EMERGENCY AUDIO FIX - ANIMALS AND NATURE")
    print("=" * 50)
    
    lesson, headers = find_tamil_a1_lessons()
    
    if not lesson:
        print("❌ Could not find Animals and Nature lesson")
        return
    
    print(f"✅ Found lesson: {lesson['title']}")
    
    content = lesson.get('content_metadata', {})
    vocabulary = content.get('vocabulary', [])
    
    print(f"📝 Lesson has {len(vocabulary)} vocabulary items")
    
    if not vocabulary:
        print("❌ No vocabulary found in lesson")
        return
    
    # Generate audio for first 3 vocabulary items
    audio_urls = {}
    
    for i, vocab in enumerate(vocabulary[:3]):
        word = vocab.get('word', '')
        example = vocab.get('example', '')
        
        if word:
            print(f"\n🎵 Processing vocab {i+1}: {word}")
            
            # Generate word audio
            word_audio = generate_audio_elevenlabs(word, VOICES['female'])
            if word_audio:
                file_path = f"tamil/a1/animals_and_nature/vocab_{i+1:02d}_word.mp3"
                word_url = upload_to_supabase(word_audio, file_path)
                
                if word_url:
                    audio_urls[f'vocab_{i+1:02d}_word'] = word_url
                    print(f"  ✅ Word audio: {word_url}")
                else:
                    # Fallback: create URL anyway
                    word_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{file_path}"
                    audio_urls[f'vocab_{i+1:02d}_word'] = word_url
                    print(f"  ⚠️  Fallback URL: {word_url}")
            
            # Generate example audio
            if example:
                example_audio = generate_audio_elevenlabs(example, VOICES['male'])
                if example_audio:
                    file_path = f"tamil/a1/animals_and_nature/vocab_{i+1:02d}_example.mp3"
                    example_url = upload_to_supabase(example_audio, file_path)
                    
                    if example_url:
                        audio_urls[f'vocab_{i+1:02d}_example'] = example_url
                        print(f"  ✅ Example audio: {example_url}")
            
            time.sleep(2)  # Rate limiting
    
    # Update lesson with new audio URLs
    if audio_urls:
        update_data = {
            'audio_urls': audio_urls,
            'has_audio': True
        }
        
        update_response = requests.patch(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=headers,
            params={'id': f'eq.{lesson["id"]}'},
            json=update_data
        )
        
        if update_response.status_code == 204:
            print(f"\n✅ Updated lesson with {len(audio_urls)} audio URLs")
            print("🎉 Audio fix completed!")
            
            # Print URLs for testing
            print("\n📋 Generated Audio URLs:")
            for key, url in audio_urls.items():
                print(f"  {key}: {url}")
                
        else:
            print(f"\n❌ Failed to update lesson: {update_response.status_code}")
    else:
        print("\n❌ No audio URLs generated")

if __name__ == "__main__":
    fix_animals_nature_audio()
