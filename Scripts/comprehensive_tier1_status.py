#!/usr/bin/env python3
"""
📊 COMPREHENSIVE TIER 1 STATUS CHECKER
Provides detailed status for all 21 Tier 1 languages with accurate path mapping
"""

import requests
import json
from datetime import datetime
from collections import defaultdict

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

# Tier 1 Languages
TIER1_LANGUAGES = {
    'english': 'English', 'spanish': 'Spanish', 'french': 'French', 'german': 'German',
    'italian': 'Italian', 'portuguese': 'Portuguese', 'dutch': 'Dutch', 'russian': 'Russian',
    'chinese': 'Chinese', 'japanese': 'Japanese', 'korean': 'Korean', 'arabic': 'Arabic',
    'hindi': 'Hindi', 'tamil': 'Tamil', 'telugu': 'Telugu', 'kannada': 'Kannada',
    'malayalam': 'Malayalam', 'bengali': 'Bengali', 'marathi': 'Marathi', 
    'punjabi': 'Punjabi', 'gujarati': 'Gujarati'
}

LEVELS = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']
TARGET_LESSONS = {'A1': 30, 'A2': 30, 'B1': 20, 'B2': 20, 'C1': 15, 'C2': 15}

def get_all_paths():
    """Get all learning paths"""
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/learning_paths",
        headers=headers,
        params={'select': 'id,name,level', 'limit': '1000'}
    )
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Failed to fetch paths: {response.status_code}")
        return []

def map_paths_to_languages(paths):
    """Map paths to languages and levels"""
    language_paths = defaultdict(lambda: defaultdict(list))
    
    for path in paths:
        name = path.get('name', '').lower()
        level = path.get('level', '').upper()
        path_id = path['id']
        
        # Find language
        detected_language = None
        for lang_key, lang_name in TIER1_LANGUAGES.items():
            if lang_name.lower() in name:
                detected_language = lang_key
                break
        
        if detected_language:
            # Find level
            detected_level = None
            if level in LEVELS:
                detected_level = level
            else:
                # Try to detect from name
                for lvl in LEVELS:
                    if lvl.lower() in name:
                        detected_level = lvl
                        break
            
            if detected_level:
                language_paths[detected_language][detected_level].append({
                    'id': path_id,
                    'name': path['name']
                })
    
    return language_paths

def count_lessons_for_path(path_id):
    """Count lessons for a specific path"""
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'path_id': f'eq.{path_id}',
            'select': 'id,title,content_metadata'
        }
    )
    
    if response.status_code == 200:
        lessons = response.json()
        # Count lessons with complete content
        complete_count = 0
        for lesson in lessons:
            content = lesson.get('content_metadata', {})
            if (content.get('vocabulary') and 
                content.get('conversations') and 
                content.get('grammar_points') and 
                content.get('exercises')):
                complete_count += 1
        
        return len(lessons), complete_count
    return 0, 0

def main():
    print("📊 COMPREHENSIVE TIER 1 STATUS CHECKER")
    print("="*80)
    print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Get all paths
    print("🔍 Fetching all learning paths...")
    paths = get_all_paths()
    print(f"✅ Found {len(paths)} total paths")
    
    # Map to languages
    language_paths = map_paths_to_languages(paths)
    
    print(f"\n📊 TIER 1 LANGUAGE STATUS")
    print("="*80)
    print(f"{'Language':<12} | {'A1':<8} | {'A2':<8} | {'B1':<8} | {'B2':<8} | {'C1':<8} | {'C2':<8} | {'Total':<8}")
    print("-" * 80)
    
    grand_total_lessons = 0
    grand_total_complete = 0
    grand_expected = 0
    languages_with_content = 0
    
    for lang_key in sorted(TIER1_LANGUAGES.keys()):
        lang_name = TIER1_LANGUAGES[lang_key]
        levels_data = language_paths.get(lang_key, {})
        
        row = f"{lang_name:<12} |"
        lang_total_lessons = 0
        lang_total_complete = 0
        lang_expected = 0
        
        for level in LEVELS:
            paths_for_level = levels_data.get(level, [])
            level_lessons = 0
            level_complete = 0
            
            # Count lessons across all paths for this level
            for path_info in paths_for_level:
                lessons, complete = count_lessons_for_path(path_info['id'])
                level_lessons += lessons
                level_complete += complete
            
            if level_lessons > 0:
                display = f"{level_complete}/{TARGET_LESSONS[level]}"
            else:
                display = "0/0" if not paths_for_level else f"0/{TARGET_LESSONS[level]}"
            
            row += f" {display:<8} |"
            lang_total_lessons += level_lessons
            lang_total_complete += level_complete
            lang_expected += TARGET_LESSONS[level]
        
        completion_rate = f"{(lang_total_complete/lang_expected*100):.0f}%" if lang_expected > 0 else "0%"
        row += f" {completion_rate:<8}"
        
        if lang_total_lessons > 0:
            languages_with_content += 1
        
        print(row)
        
        grand_total_lessons += lang_total_lessons
        grand_total_complete += lang_total_complete
        grand_expected += lang_expected
    
    print("-" * 80)
    print(f"📈 GRAND TOTALS:")
    print(f"✅ Languages with Content: {languages_with_content}/21")
    print(f"📚 Total Lessons: {grand_total_lessons}")
    print(f"📝 Complete Lessons: {grand_total_complete}")
    print(f"🎯 Expected Total: {grand_expected}")
    print(f"📊 Overall Completion: {(grand_total_complete/grand_expected*100):.1f}%")
    
    # Status assessment
    completion_rate = (grand_total_complete/grand_expected*100) if grand_expected > 0 else 0
    
    print(f"\n🎯 OVERALL STATUS")
    print("="*50)
    
    if completion_rate >= 80:
        print("🎉 EXCELLENT PROGRESS!")
        print("✅ Most languages have substantial content")
        print("✅ Ready for audio generation phase")
    elif completion_rate >= 50:
        print("🔄 GOOD PROGRESS!")
        print("✅ Multiple languages have content")
        print("🔄 Continue lesson creation for remaining languages")
    elif completion_rate >= 20:
        print("⚠️  MODERATE PROGRESS")
        print("✅ Some languages have lessons")
        print("🔄 Need more comprehensive content creation")
    else:
        print("🚀 EARLY STAGE")
        print("🔄 Most content creation still needed")
    
    # Recent activity check
    print(f"\n🕐 RECENT ACTIVITY (Last 24 hours)")
    print("="*50)
    
    from datetime import timedelta
    yesterday = (datetime.now() - timedelta(days=1)).isoformat()
    
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'created_at': f'gte.{yesterday}',
            'select': 'id,title,created_at',
            'order': 'created_at.desc',
            'limit': '10'
        }
    )
    
    if response.status_code == 200:
        recent_lessons = response.json()
        print(f"📈 {len(recent_lessons)} lessons created in last 24 hours")
        
        if recent_lessons:
            print("Recent lessons:")
            for lesson in recent_lessons[:5]:
                title = lesson.get('title', 'Unknown')[:50]
                created = lesson.get('created_at', '')[:19]
                print(f"  📚 {created}: {title}...")
        else:
            print("⚠️  No recent lesson creation detected")
    
    print(f"\n💡 NEXT STEPS:")
    print("="*50)
    
    if completion_rate < 80:
        print("1. 🔄 Continue running tier1_smart_lesson_creator.py")
        print("2. 📚 Focus on languages with 0 lessons")
        print("3. 🎯 Target completing A1-A2 levels first (60 lessons each)")
    
    if completion_rate >= 50:
        print("4. 🎵 Start audio generation for completed lessons")
        print("5. ✅ Run quality validation on existing content")
    
    print("6. 📊 Check this status regularly to track progress")

if __name__ == "__main__":
    main() 