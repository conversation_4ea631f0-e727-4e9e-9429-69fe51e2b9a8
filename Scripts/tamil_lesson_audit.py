#!/usr/bin/env python3
"""
📊 TAMIL LESSON AUDIT
Comprehensive analysis of Tamil lesson content structure and audio coverage
"""

import requests
import json
from typing import Dict, List, Any

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def get_tamil_lessons() -> List[Dict]:
    """Get all Tamil A1 lessons"""
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=HEADERS,
            params={
                'path_id': 'eq.6b427613-420f-4586-bce8-2773d722f0b4',
                'select': 'id,title,content_metadata,sequence_order',
                'order': 'sequence_order.asc'
            }
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to fetch lessons: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error fetching lessons: {e}")
        return []

def analyze_lesson_content(lesson: Dict) -> Dict:
    """Analyze a single lesson's content structure"""
    content_metadata = lesson.get('content_metadata', {})
    
    analysis = {
        'id': lesson['id'],
        'title': lesson['title'],
        'sequence_order': lesson.get('sequence_order', 0),
        'content_keys': list(content_metadata.keys()),
        'vocabulary_count': len(content_metadata.get('vocabulary', [])),
        'exercises_count': len(content_metadata.get('exercises', [])),
        'grammar_count': len(content_metadata.get('grammar_points', [])),
        'conversations_count': len(content_metadata.get('conversations', [])),
        'audio_urls_count': len(content_metadata.get('audio_urls', {})),
        'has_complete_structure': False,
        'missing_content': [],
        'audio_coverage': {},
        'issues': []
    }
    
    # Check for complete lesson structure
    required_keys = ['vocabulary', 'exercises', 'grammar_points', 'conversations']
    analysis['has_complete_structure'] = all(key in content_metadata for key in required_keys)
    analysis['missing_content'] = [key for key in required_keys if key not in content_metadata]
    
    # Analyze audio coverage
    if 'audio_urls' in content_metadata:
        audio_urls = content_metadata['audio_urls']
        analysis['audio_coverage'] = {
            'vocab_audio': len([k for k in audio_urls.keys() if 'vocab' in k and 'word' in k]),
            'example_audio': len([k for k in audio_urls.keys() if 'vocab' in k and 'example' in k]),
            'exercise_audio': len([k for k in audio_urls.keys() if 'exercise' in k]),
            'grammar_audio': len([k for k in audio_urls.keys() if 'grammar' in k]),
            'conversation_audio': len([k for k in audio_urls.keys() if 'conversation' in k])
        }
    
    # Identify issues
    if analysis['vocabulary_count'] == 0 and analysis['audio_urls_count'] > 0:
        analysis['issues'].append("Has audio but no vocabulary content")
    
    if analysis['vocabulary_count'] > 0 and analysis['audio_coverage'].get('vocab_audio', 0) == 0:
        analysis['issues'].append("Has vocabulary but no vocabulary audio")
    
    if analysis['exercises_count'] > 0 and analysis['audio_coverage'].get('exercise_audio', 0) == 0:
        analysis['issues'].append("Has exercises but no exercise audio")
    
    if len(analysis['missing_content']) > 0:
        analysis['issues'].append(f"Missing content: {', '.join(analysis['missing_content'])}")
    
    if analysis['audio_urls_count'] > 0:
        # Check if audio uses wrong voice (file paths with old structure)
        sample_audio = list(content_metadata.get('audio_urls', {}).values())[0] if content_metadata.get('audio_urls') else ""
        if 'colors_and_descriptions' in sample_audio or 'family_members' in sample_audio:
            analysis['issues'].append("Audio likely uses wrong English voice")
    
    return analysis

def generate_audit_report():
    """Generate comprehensive audit report"""
    
    print("📊 TAMIL LESSON CONTENT & AUDIO AUDIT")
    print("=" * 60)
    
    lessons = get_tamil_lessons()
    if not lessons:
        print("❌ No lessons found")
        return
    
    analyses = []
    total_lessons = len(lessons)
    complete_lessons = 0
    audio_lessons = 0
    problematic_lessons = 0
    
    print(f"📚 Analyzing {total_lessons} Tamil A1 lessons...\n")
    
    for lesson in lessons:
        analysis = analyze_lesson_content(lesson)
        analyses.append(analysis)
        
        if analysis['has_complete_structure']:
            complete_lessons += 1
        
        if analysis['audio_urls_count'] > 0:
            audio_lessons += 1
        
        if len(analysis['issues']) > 0:
            problematic_lessons += 1
    
    # Sort by sequence order
    analyses.sort(key=lambda x: x['sequence_order'])
    
    # Print detailed analysis
    print("📋 LESSON-BY-LESSON ANALYSIS:")
    print("-" * 60)
    
    for i, analysis in enumerate(analyses, 1):
        status_icon = "✅" if len(analysis['issues']) == 0 else "❌"
        audio_icon = "🔊" if analysis['audio_urls_count'] > 0 else "🔇"
        
        print(f"{i:2d}. {status_icon} {audio_icon} {analysis['title']}")
        print(f"    Content: {analysis['vocabulary_count']} vocab, {analysis['exercises_count']} exercises, {analysis['grammar_count']} grammar")
        print(f"    Audio: {analysis['audio_urls_count']} files ({analysis['audio_coverage']})")
        
        if analysis['issues']:
            for issue in analysis['issues']:
                print(f"    ⚠️  {issue}")
        print()
    
    # Summary statistics
    print("📊 SUMMARY STATISTICS:")
    print("-" * 60)
    print(f"Total Lessons: {total_lessons}")
    print(f"Complete Content Structure: {complete_lessons}/{total_lessons} ({complete_lessons/total_lessons*100:.1f}%)")
    print(f"Lessons with Audio: {audio_lessons}/{total_lessons} ({audio_lessons/total_lessons*100:.1f}%)")
    print(f"Problematic Lessons: {problematic_lessons}/{total_lessons} ({problematic_lessons/total_lessons*100:.1f}%)")
    
    # Critical issues
    print(f"\n🚨 CRITICAL ISSUES FOUND:")
    print("-" * 60)
    
    orphaned_audio = [a for a in analyses if a['vocabulary_count'] == 0 and a['audio_urls_count'] > 0]
    missing_audio = [a for a in analyses if a['vocabulary_count'] > 0 and a['audio_coverage'].get('vocab_audio', 0) == 0]
    wrong_voice = [a for a in analyses if any('wrong voice' in issue for issue in a['issues'])]
    
    if orphaned_audio:
        print(f"🔊 Orphaned Audio (audio without content): {len(orphaned_audio)} lessons")
        for lesson in orphaned_audio:
            print(f"   - {lesson['title']}: {lesson['audio_urls_count']} audio files")
    
    if missing_audio:
        print(f"🔇 Missing Audio (content without audio): {len(missing_audio)} lessons")
        for lesson in missing_audio:
            print(f"   - {lesson['title']}: {lesson['vocabulary_count']} vocab items")
    
    if wrong_voice:
        print(f"🎤 Wrong Voice Audio: {len(wrong_voice)} lessons")
        for lesson in wrong_voice:
            print(f"   - {lesson['title']}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    print("-" * 60)
    print("1. 🔧 Fix content structure for lessons with orphaned audio")
    print("2. 🎵 Generate audio for lessons with complete content but no audio")
    print("3. 🎤 Replace wrong voice audio with correct Tamil voice (Nila)")
    print("4. 📚 Complete missing content for incomplete lessons")
    
    # Export detailed results
    with open('tamil_lesson_audit_results.json', 'w') as f:
        json.dump({
            'summary': {
                'total_lessons': total_lessons,
                'complete_lessons': complete_lessons,
                'audio_lessons': audio_lessons,
                'problematic_lessons': problematic_lessons
            },
            'detailed_analysis': analyses
        }, f, indent=2)
    
    print(f"\n📁 Detailed results saved to: tamil_lesson_audit_results.json")

if __name__ == "__main__":
    generate_audit_report() 