#!/usr/bin/env python3
"""
Simple Quality Check for Tamil A2-C2 Lessons
"""

import requests
import json

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

TAMIL_PATHS = {
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

def check_lesson_content(lesson, level):
    """Check content quality for a single lesson"""
    
    issues = []
    content_metadata = lesson.get('content_metadata', {})
    
    # Check vocabulary
    vocabulary = content_metadata.get('vocabulary', [])
    if len(vocabulary) != 20:
        issues.append(f"Vocabulary: {len(vocabulary)}/20 items")
    
    # Check grammar
    grammar = content_metadata.get('grammar_points', [])
    if len(grammar) != 10:
        issues.append(f"Grammar: {len(grammar)}/10 points")
    
    # Check conversations
    conversations = content_metadata.get('conversations', [])
    if len(conversations) != 15:
        issues.append(f"Conversations: {len(conversations)}/15 items")
    
    # Check exercises
    exercises = content_metadata.get('exercises', [])
    if len(exercises) != 15:
        issues.append(f"Exercises: {len(exercises)}/15 items")
    
    return issues

def validate_level(level, path_id):
    """Validate all lessons for a level"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    print(f"\n🔍 VALIDATING {level} LEVEL")
    print("=" * 40)
    
    # Get all lessons
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'path_id': f'eq.{path_id}',
            'select': 'id,title,sequence_order,content_metadata',
            'order': 'sequence_order'
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Failed to fetch {level} lessons")
        return {'total': 0, 'passed': 0, 'failed': 0}
    
    lessons = response.json()
    passed = 0
    failed = 0
    
    for lesson in lessons:
        issues = check_lesson_content(lesson, level)
        
        if not issues:
            passed += 1
            print(f"✅ Lesson {lesson['sequence_order']}: {lesson['title'][:40]}...")
        else:
            failed += 1
            print(f"❌ Lesson {lesson['sequence_order']}: {', '.join(issues)}")
    
    print(f"\n📊 {level} RESULTS: {passed}/{len(lessons)} lessons passed")
    return {'total': len(lessons), 'passed': passed, 'failed': failed}

def main():
    """Main validation function"""
    
    print("🔍 SIMPLE QUALITY CHECK")
    print("Validating A2-C2 Tamil lesson content")
    print("=" * 50)
    
    overall_results = {}
    
    for level, path_id in TAMIL_PATHS.items():
        result = validate_level(level, path_id)
        overall_results[level] = result
    
    # Overall summary
    print(f"\n📊 OVERALL VALIDATION RESULTS")
    print("=" * 50)
    
    total_lessons = sum(r['total'] for r in overall_results.values())
    total_passed = sum(r['passed'] for r in overall_results.values())
    
    for level, result in overall_results.items():
        print(f"  {level}: {result['passed']}/{result['total']} lessons passed")
    
    print(f"\n🎯 TOTAL: {total_passed}/{total_lessons} lessons passed validation")
    
    if total_passed == total_lessons:
        print("🎉 ALL LESSONS PASSED QUALITY VALIDATION!")
    else:
        print("⚠️  Some lessons need attention")

if __name__ == "__main__":
    main()
