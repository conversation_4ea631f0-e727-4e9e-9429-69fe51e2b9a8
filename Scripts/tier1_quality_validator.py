#!/usr/bin/env python3
"""
🎯 TIER 1 QUALITY VALIDATOR
Validates all 21 Tier 1 languages against COMPREHENSIVE_QUALITY_CHECKLIST.md

Features:
- Real-time progress monitoring
- Comprehensive content validation
- Cultural authenticity checks
- Romanization validation
- Database structure compliance
"""

import os
import json
import requests
import time
from typing import Dict, List, Any, Tuple
from datetime import datetime
import concurrent.futures
from collections import defaultdict

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tier 1 Languages
TIER1_LANGUAGES = {
    'english': 'English', 'spanish': 'Spanish', 'french': 'French', 'german': 'German',
    'italian': 'Italian', 'portuguese': 'Portuguese', 'dutch': 'Dutch', 'russian': 'Russian',
    'chinese': 'Chinese', 'japanese': 'Japanese', 'korean': 'Korean', 'arabic': 'Arabic',
    'hindi': 'Hindi', 'tamil': 'Tamil', 'telugu': 'Telugu', 'kannada': 'Kannada',
    'malayalam': 'Malayalam', 'bengali': 'Bengali', 'marathi': 'Marathi', 
    'punjabi': 'Punjabi', 'gujarati': 'Gujarati'
}

# Expected lesson counts
EXPECTED_COUNTS = {'A1': 30, 'A2': 30, 'B1': 20, 'B2': 20, 'C1': 15, 'C2': 15}
TOTAL_EXPECTED = sum(EXPECTED_COUNTS.values())  # 130 lessons per language

class Tier1QualityValidator:
    """Comprehensive quality validator for all Tier 1 languages"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        self.validation_results = {}
        
    def get_learning_paths(self) -> Dict[str, str]:
        """Get all learning paths for Tier 1 languages"""
        print("🔍 Discovering learning paths...")
        
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/learning_paths",
            headers=self.headers,
            params={'select': 'id,name,level'}
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch learning paths: {response.status_code}")
            return {}
        
        paths = response.json()
        language_paths = {}
        
        for path in paths:
            name = path.get('name', '').lower()
            for lang_key, lang_name in TIER1_LANGUAGES.items():
                if lang_name.lower() in name and ('complete' in name or 'a1-c2' in name.lower()):
                    language_paths[lang_key] = path['id']
                    print(f"  📁 Found {lang_name}: {path['id']}")
                    break
        
        print(f"✅ Discovered {len(language_paths)}/21 language paths")
        return language_paths
    
    def get_lessons_for_path(self, path_id: str) -> List[Dict[str, Any]]:
        """Get all lessons for a learning path"""
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={
                'path_id': f'eq.{path_id}',
                'select': 'id,title,description,difficulty_level,content_metadata,sequence_order'
            }
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to fetch lessons for path {path_id}")
            return []
    
    def validate_lesson_content(self, lesson: Dict[str, Any], language: str) -> Dict[str, Any]:
        """Validate a single lesson against quality checklist"""
        validation = {
            'lesson_id': lesson['id'],
            'title': lesson.get('title', ''),
            'level': f"Level {lesson.get('difficulty_level', 0)}",
            'issues': [],
            'passed_checks': [],
            'content_counts': {},
            'quality_score': 0
        }
        
        content = lesson.get('content_metadata', {})
        
        # 🔤 CONTENT QUALITY VERIFICATION
        vocabulary = content.get('vocabulary', [])
        conversations = content.get('conversations', [])
        grammar_points = content.get('grammar_points', [])
        exercises = content.get('exercises', [])
        
        validation['content_counts'] = {
            'vocabulary': len(vocabulary),
            'conversations': len(conversations),
            'grammar': len(grammar_points),
            'exercises': len(exercises)
        }
        
        # Check content counts
        if len(vocabulary) == 0:
            validation['issues'].append("❌ No vocabulary items")
        elif len(vocabulary) < 15:
            validation['issues'].append(f"⚠️  Only {len(vocabulary)} vocabulary items (expected 15+)")
        else:
            validation['passed_checks'].append(f"✅ Vocabulary count: {len(vocabulary)}")
        
        if len(conversations) == 0:
            validation['issues'].append("❌ No conversations")
        elif len(conversations) < 10:
            validation['issues'].append(f"⚠️  Only {len(conversations)} conversations (expected 10+)")
        else:
            validation['passed_checks'].append(f"✅ Conversations count: {len(conversations)}")
        
        if len(grammar_points) == 0:
            validation['issues'].append("❌ No grammar points")
        elif len(grammar_points) < 8:
            validation['issues'].append(f"⚠️  Only {len(grammar_points)} grammar points (expected 8+)")
        else:
            validation['passed_checks'].append(f"✅ Grammar count: {len(grammar_points)}")
        
        if len(exercises) == 0:
            validation['issues'].append("❌ No exercises")
        elif len(exercises) < 10:
            validation['issues'].append(f"⚠️  Only {len(exercises)} exercises (expected 10+)")
        else:
            validation['passed_checks'].append(f"✅ Exercises count: {len(exercises)}")
        
        # 🎵 ROMANIZED PRONUNCIATION TESTING
        pronunciation_issues = []
        
        # Check conversation pronunciations
        for i, conv in enumerate(conversations):
            exchanges = conv.get('exchanges', [])
            for j, exchange in enumerate(exchanges):
                if not exchange.get('pronunciation'):
                    pronunciation_issues.append(f"Missing pronunciation in conversation {i+1}, exchange {j+1}")
        
        # Check exercise option pronunciations
        for i, exercise in enumerate(exercises):
            options = exercise.get('options', [])
            pronunciations = exercise.get('options_pronunciations', [])
            
            if len(options) != len(pronunciations):
                pronunciation_issues.append(f"Exercise {i+1}: {len(options)} options vs {len(pronunciations)} pronunciations")
            
            for j, pronunciation in enumerate(pronunciations):
                if not pronunciation or len(pronunciation.strip()) == 0:
                    pronunciation_issues.append(f"Exercise {i+1}, option {j+1}: empty pronunciation")
        
        if pronunciation_issues:
            validation['issues'].extend([f"🎵 {issue}" for issue in pronunciation_issues[:3]])  # Show first 3
            if len(pronunciation_issues) > 3:
                validation['issues'].append(f"🎵 ...and {len(pronunciation_issues)-3} more pronunciation issues")
        else:
            validation['passed_checks'].append("✅ All pronunciations present")
        
        # 📊 DATABASE STRUCTURE VALIDATION
        structure_issues = []
        
        # Check vocabulary structure
        for i, vocab in enumerate(vocabulary):
            if not vocab.get('word'):
                structure_issues.append(f"Vocabulary {i+1}: missing word")
            if not vocab.get('translation'):
                structure_issues.append(f"Vocabulary {i+1}: missing translation")
            if not vocab.get('example'):
                structure_issues.append(f"Vocabulary {i+1}: missing example")
        
        if structure_issues:
            validation['issues'].extend([f"📊 {issue}" for issue in structure_issues[:2]])
            if len(structure_issues) > 2:
                validation['issues'].append(f"📊 ...and {len(structure_issues)-2} more structure issues")
        else:
            validation['passed_checks'].append("✅ Database structure valid")
        
        # 🌍 CULTURAL AUTHENTICITY (basic check)
        title = lesson.get('title', '').lower()
        description = lesson.get('description', '').lower()
        
        # Check if title/description are in English
        if any(char in title for char in ['தமிழ்', 'हिन्दी', '中文', 'العربية', 'русский']):
            validation['issues'].append("🌍 Title not in English")
        else:
            validation['passed_checks'].append("✅ Title in English")
        
        # Calculate quality score
        total_checks = len(validation['passed_checks']) + len(validation['issues'])
        if total_checks > 0:
            validation['quality_score'] = (len(validation['passed_checks']) / total_checks) * 100
        
        return validation
    
    def validate_language(self, language: str, path_id: str) -> Dict[str, Any]:
        """Validate all lessons for a language"""
        print(f"\n🔍 VALIDATING {TIER1_LANGUAGES[language].upper()}")
        print("="*60)
        
        start_time = datetime.now()
        
        # Get all lessons
        lessons = self.get_lessons_for_path(path_id)
        print(f"📚 Found {len(lessons)} lessons")
        
        if len(lessons) == 0:
            return {
                'language': language,
                'error': 'No lessons found',
                'lesson_count': 0,
                'expected_count': TOTAL_EXPECTED
            }
        
        # Organize lessons by difficulty level
        lessons_by_level = defaultdict(list)
        for lesson in lessons:
            level = lesson.get('difficulty_level', 0)
            lessons_by_level[level].append(lesson)
        
        # Validate each lesson
        all_validations = []
        total_quality_score = 0
        passed_lessons = 0
        
        for lesson in lessons:
            validation = self.validate_lesson_content(lesson, language)
            all_validations.append(validation)
            
            # Calculate pass/fail
            if validation['quality_score'] >= 70:  # 70% quality threshold
                passed_lessons += 1
            
            total_quality_score += validation['quality_score']
            
            # Show progress every 10 lessons
            if len(all_validations) % 10 == 0:
                avg_score = total_quality_score / len(all_validations)
                print(f"  📊 Progress: {len(all_validations)}/{len(lessons)} lessons | Avg Quality: {avg_score:.1f}%")
        
        # Calculate summary statistics
        avg_quality_score = total_quality_score / len(lessons) if lessons else 0
        pass_rate = (passed_lessons / len(lessons)) * 100 if lessons else 0
        
        # Level breakdown
        level_breakdown = {}
        for level, lesson_list in lessons_by_level.items():
            level_name = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'][level-1] if 1 <= level <= 6 else f"Level {level}"
            level_breakdown[level_name] = {
                'count': len(lesson_list),
                'expected': EXPECTED_COUNTS.get(level_name, 0)
            }
        
        end_time = datetime.now()
        
        result = {
            'language': language,
            'language_name': TIER1_LANGUAGES[language],
            'path_id': path_id,
            'lesson_count': len(lessons),
            'expected_count': TOTAL_EXPECTED,
            'completion_rate': f"{(len(lessons)/TOTAL_EXPECTED*100):.1f}%",
            'passed_lessons': passed_lessons,
            'pass_rate': f"{pass_rate:.1f}%",
            'avg_quality_score': f"{avg_quality_score:.1f}%",
            'level_breakdown': level_breakdown,
            'validation_duration': str(end_time - start_time),
            'detailed_validations': all_validations
        }
        
        # Summary
        print(f"✅ {TIER1_LANGUAGES[language]} Validation Complete:")
        print(f"   📚 Lessons: {len(lessons)}/{TOTAL_EXPECTED} ({(len(lessons)/TOTAL_EXPECTED*100):.1f}%)")
        print(f"   🎯 Pass Rate: {passed_lessons}/{len(lessons)} ({pass_rate:.1f}%)")
        print(f"   📊 Avg Quality: {avg_quality_score:.1f}%")
        
        return result
    
    def run_tier1_validation(self, max_workers: int = 5):
        """Run validation for all Tier 1 languages in parallel"""
        print("🎯 TIER 1 LANGUAGES QUALITY VALIDATOR")
        print("="*70)
        print("Validating all 21 Tier 1 languages against comprehensive quality checklist")
        print()
        
        start_time = datetime.now()
        
        # Get learning paths
        language_paths = self.get_learning_paths()
        
        if not language_paths:
            print("❌ No learning paths found. Run tier1_languages_complete_creator.py first.")
            return
        
        # Validate languages in parallel
        print(f"\n🔄 Running validation with {max_workers} parallel workers...")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_language = {
                executor.submit(self.validate_language, language, path_id): language
                for language, path_id in language_paths.items()
            }
            
            results = {}
            for future in concurrent.futures.as_completed(future_to_language):
                language = future_to_language[future]
                try:
                    result = future.result()
                    results[language] = result
                    print(f"✅ {TIER1_LANGUAGES[language]} validation complete")
                except Exception as e:
                    results[language] = {'error': str(e)}
                    print(f"❌ {TIER1_LANGUAGES[language]} validation failed: {e}")
        
        # Generate comprehensive report
        end_time = datetime.now()
        self.generate_quality_report(results, end_time - start_time)
    
    def generate_quality_report(self, results: Dict[str, Any], duration):
        """Generate comprehensive quality report"""
        print("\n" + "="*70)
        print("🎯 TIER 1 QUALITY VALIDATION REPORT")
        print("="*70)
        print(f"⏰ Total Validation Time: {duration}")
        print()
        
        # Calculate totals
        total_languages = len(TIER1_LANGUAGES)
        successful_validations = 0
        total_lessons = 0
        total_expected_lessons = total_languages * TOTAL_EXPECTED
        total_passed_lessons = 0
        quality_scores = []
        
        print("📊 RESULTS BY LANGUAGE:")
        print("-" * 70)
        print(f"{'Language':<15} | {'Lessons':<12} | {'Pass Rate':<10} | {'Quality':<8} | {'Status'}")
        print("-" * 70)
        
        for language, result in results.items():
            language_name = TIER1_LANGUAGES[language]
            
            if 'error' not in result:
                lesson_count = result['lesson_count']
                completion_rate = result['completion_rate']
                pass_rate = result['pass_rate']
                quality_score = result['avg_quality_score']
                
                total_lessons += lesson_count
                total_passed_lessons += result['passed_lessons']
                
                try:
                    quality_scores.append(float(quality_score.replace('%', '')))
                except:
                    pass
                
                status = "✅" if lesson_count >= TOTAL_EXPECTED * 0.8 else "⚠️"
                
                print(f"{language_name:<15} | {lesson_count:3}/{TOTAL_EXPECTED} ({completion_rate:>4}) | {pass_rate:>8} | {quality_score:>6} | {status}")
                
                if lesson_count >= TOTAL_EXPECTED * 0.5:  # At least 50% complete
                    successful_validations += 1
            else:
                error_msg = result.get('error', 'Unknown error')[:30]
                print(f"{language_name:<15} | ERROR: {error_msg}... | ❌")
        
        print("-" * 70)
        
        # Grand totals
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        overall_completion = (total_lessons / total_expected_lessons) * 100
        overall_pass_rate = (total_passed_lessons / total_lessons) * 100 if total_lessons else 0
        
        print(f"📈 GRAND TOTALS:")
        print(f"✅ Languages Validated: {successful_validations}/{total_languages}")
        print(f"📚 Total Lessons: {total_lessons}/{total_expected_lessons} ({overall_completion:.1f}%)")
        print(f"🎯 Overall Pass Rate: {overall_pass_rate:.1f}%")
        print(f"📊 Average Quality Score: {avg_quality:.1f}%")
        
        # Quality assessment
        if avg_quality >= 80 and overall_completion >= 80:
            print("\n🎉 EXCELLENT QUALITY!")
            print("✅ Tier 1 languages meet high quality standards")
            print("✅ Ready for audio generation phase")
        elif avg_quality >= 60 and overall_completion >= 60:
            print("\n⚠️  GOOD QUALITY - Some Improvements Needed")
            print("🔄 Most content meets standards but some refinement needed")
        else:
            print("\n❌ QUALITY CONCERNS")
            print("🔄 Significant improvements needed before proceeding")
        
        # Save detailed report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"quality_report_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📄 Detailed report saved: {report_file}")

def main():
    """Main execution function"""
    validator = Tier1QualityValidator()
    
    # Run validation with moderate parallelism
    validator.run_tier1_validation(max_workers=4)

if __name__ == "__main__":
    main() 