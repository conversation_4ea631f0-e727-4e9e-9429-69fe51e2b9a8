#!/usr/bin/env python3
"""
Fixed Tamil Title Updater
Updates Tamil lesson titles from Tamil to English

This script fixes the issue where lesson titles were generated in Tamil instead of English.
It updates all A2-C2 Tamil lessons to have proper English titles like A1.
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil Learning Path IDs
TAMIL_PATHS = {
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

class TamilTitleUpdater:
    """Updates Tamil lesson titles to English"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_lessons_by_level(self, level: str) -> List[Dict[str, Any]]:
        """Get all lessons for a specific level"""
        
        path_id = TAMIL_PATHS[level]
        
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=self.headers,
                params={
                    'path_id': f'eq.{path_id}',
                    'select': 'id,title,sequence_order',
                    'order': 'sequence_order'
                }
            )
            
            if response.status_code == 200:
                lessons = response.json()
                print(f"📚 Found {len(lessons)} {level} lessons")
                return lessons
            else:
                print(f"❌ Failed to fetch {level} lessons: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Error fetching lessons: {e}")
            return []
    
    def generate_english_titles(self, level: str, current_titles: List[str]) -> List[str]:
        """Generate English titles based on current Tamil titles"""
        
        titles_text = "\n".join([f"{i+1}. {title}" for i, title in enumerate(current_titles)])
        
        prompt = f"""
        Convert these Tamil lesson titles to proper English titles for Tamil {level} level:
        
        {titles_text}
        
        Requirements:
        - Convert to clear, concise English titles
        - Maintain the same topic and meaning
        - Follow A1 Tamil lesson title format (simple, descriptive)
        - {level} difficulty level appropriate
        - Keep cultural context but use English
        
        Examples of good English titles:
        - "Daily Routine: From Morning to Night"
        - "Job Search: Understanding Advertisements"
        - "Restaurant: Ordering Food"
        - "Health: Visiting a Doctor"
        
        Return exactly {len(current_titles)} English titles as a numbered list:
        1. English Title
        2. English Title
        ...
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            english_titles = []
            
            for line in response.text.strip().split('\n'):
                if line.strip() and '. ' in line:
                    title = line.split('. ', 1)[1].strip()
                    if title:
                        english_titles.append(title)
            
            print(f"✅ Generated {len(english_titles)} English titles for {level}")
            return english_titles[:len(current_titles)]  # Ensure exact count
            
        except Exception as e:
            print(f"❌ Failed to generate English titles for {level}: {e}")
            return []
    
    def update_lesson_title(self, lesson_id: str, new_title: str) -> bool:
        """Update a lesson's title"""
        
        try:
            response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=self.headers,
                params={'id': f'eq.{lesson_id}'},
                json={'title': new_title}
            )
            
            if response.status_code == 204:
                return True
            else:
                print(f"❌ Failed to update lesson: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Update error: {e}")
            return False
    
    def update_level_titles(self, level: str) -> Dict[str, Any]:
        """Update all titles for a specific level"""
        
        print(f"\n🔄 UPDATING {level} TITLES")
        print("=" * 50)
        
        # Get current lessons
        lessons = self.get_lessons_by_level(level)
        
        if not lessons:
            return {'updated': 0, 'failed': len(lessons)}
        
        # Extract current titles
        current_titles = [lesson['title'] for lesson in lessons]
        
        print(f"\n📝 Current {level} titles (first 3):")
        for i, title in enumerate(current_titles[:3]):
            print(f"  {i+1}. {title}")
        print("  ...")
        
        # Generate English titles
        english_titles = self.generate_english_titles(level, current_titles)
        
        if len(english_titles) != len(current_titles):
            print(f"❌ Title count mismatch: {len(english_titles)} vs {len(current_titles)}")
            return {'updated': 0, 'failed': len(lessons)}
        
        print(f"\n✨ New {level} English titles (first 3):")
        for i, title in enumerate(english_titles[:3]):
            print(f"  {i+1}. {title}")
        print("  ...")
        
        # Update lessons
        results = {'updated': 0, 'failed': []}
        
        for lesson, new_title in zip(lessons, english_titles):
            lesson_id = lesson['id']
            old_title = lesson['title']
            
            print(f"\n🔄 Updating lesson {lesson['sequence_order']}")
            print(f"   Old: {old_title[:50]}...")
            print(f"   New: {new_title}")
            
            if self.update_lesson_title(lesson_id, new_title):
                results['updated'] += 1
                print("   ✅ Updated")
            else:
                results['failed'].append(old_title)
                print("   ❌ Failed")
            
            time.sleep(1)  # Prevent rate limiting
        
        return results
    
    def update_all_levels(self) -> Dict[str, Any]:
        """Update titles for all levels"""
        
        print("🔧 TAMIL TITLE UPDATER")
        print("Converting Tamil titles to English")
        print("=" * 60)
        
        overall_results = {}
        
        for level in ['A2', 'B1', 'B2', 'C1', 'C2']:
            results = self.update_level_titles(level)
            overall_results[level] = results
            
            print(f"\n📊 {level} RESULTS:")
            print(f"✅ Updated: {results['updated']}")
            print(f"❌ Failed: {len(results['failed'])}")
            
            time.sleep(2)  # Wait between levels
        
        return overall_results

def main():
    """Main function - Update all Tamil lesson titles to English"""
    
    print("🔧 FIXING TAMIL LESSON TITLES")
    print("Converting Tamil titles to English like A1")
    print("=" * 60)
    
    updater = TamilTitleUpdater()
    
    # Show current issue
    print("🚨 CURRENT ISSUE:")
    print("   Tamil A2-C2 lessons have Tamil titles")
    print("   Should have English titles like A1")
    print("   Example: 'தினசரி வேலைகள்' → 'Daily Routine'")
    
    # Confirm update
    response = input("\n🔄 Proceed with title updates? (y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ Update cancelled")
        return
    
    # Run updates
    overall_results = updater.update_all_levels()
    
    # Final summary
    print(f"\n📊 OVERALL RESULTS:")
    print("=" * 50)
    
    total_updated = sum(r['updated'] for r in overall_results.values())
    total_failed = sum(len(r['failed']) for r in overall_results.values())
    
    for level, results in overall_results.items():
        print(f"  • {level}: {results['updated']} updated, {len(results['failed'])} failed")
    
    print(f"\n🎉 TOTAL: {total_updated} titles updated, {total_failed} failed")
    
    if total_updated > 0:
        print("\n✅ Tamil lesson titles successfully converted to English!")
        print("📋 Next: Continue with content generation")
    else:
        print("\n⚠️  No titles were updated. Check for errors above.")

if __name__ == "__main__":
    main()
