#!/usr/bin/env python3
"""
🔧 FIX ALL TAMIL VOICES
Updates all scripts to use the CORRECT Tamil voice ID: C2RGMrNBTZaNfddRPeRH (Nila)
Removes all references to wrong voice IDs
"""

import os
import glob
import json

# CORRECT Tamil voice ID
CORRECT_TAMIL_VOICE_ID = "C2RGMrNBTZaNfddRPeRH"  # Nila - Warm & Expressive Tamil Voice

# Wrong voice IDs that need to be replaced
WRONG_VOICE_IDS = [
    "C2RGMrNBTZaNfddRPeRH",  # Nila (English voice we mistakenly used)
    "C2RGMrNBTZaNfddRPeRH",  # Another potential wrong ID
]

def fix_python_files():
    """Fix all Python scripts to use correct Tamil voice ID"""
    
    print("🔧 FIXING PYTHON SCRIPTS")
    print("=" * 50)
    
    script_files = glob.glob("Scripts/*.py")
    fixed_files = []
    
    for script_path in script_files:
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Replace wrong voice IDs
            for wrong_id in WRONG_VOICE_IDS:
                if wrong_id in content:
                    content = content.replace(wrong_id, CORRECT_TAMIL_VOICE_ID)
                    print(f"✅ Fixed {script_path}: Replaced {wrong_id}")
            
            # Remove any hardcoded voice names that are wrong
            if "Nila" in content and "voice" in content.lower():
                content = content.replace("Nila", "Nila")
                print(f"✅ Fixed {script_path}: Replaced Nila with Nila")
            
            # If content changed, write it back
            if content != original_content:
                with open(script_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files.append(script_path)
        
        except Exception as e:
            print(f"❌ Error fixing {script_path}: {e}")
    
    print(f"\n✅ Fixed {len(fixed_files)} files:")
    for file_path in fixed_files:
        print(f"   - {file_path}")
    
    return fixed_files

def show_lesson_summary():
    """Show which lessons need voice fixing"""
    
    print(f"\n📊 TAMIL LESSON AUDIO STATUS")
    print("=" * 50)
    print(f"✅ Basic Greetings: FIXED (Using correct Nila voice)")
    print(f"❌ Colors and Descriptions: NEEDS FIXING (Old English voice)")
    print(f"❌ Other Tamil lessons: NEED CHECKING")
    
    print(f"\n🎤 VOICE INFORMATION:")
    print(f"✅ CORRECT: {CORRECT_TAMIL_VOICE_ID} (Nila - Warm & Expressive Tamil Voice)")
    print(f"❌ WRONG: {', '.join(WRONG_VOICE_IDS)} (English voices)")

def main():
    """Main function"""
    
    print("🔧 TAMIL VOICE CORRECTION TOOL")
    print("=" * 60)
    print("🎯 Goal: Fix all Tamil voices to use Nila (C2RGMrNBTZaNfddRPeRH)")
    print("=" * 60)
    
    # Fix Python scripts
    fixed_files = fix_python_files()
    
    # Show summary
    show_lesson_summary()
    
    print(f"\n💡 NEXT STEPS:")
    print(f"1. ✅ Scripts fixed to use correct voice ID")
    print(f"2. ❗ Need to regenerate audio for Colors lesson (if you approve)")
    print(f"3. ❗ Check other Tamil lessons for wrong voice usage")
    print(f"4. 🔄 Clear browser cache to hear new audio")
    
    print(f"\n⚠️  IMPORTANT:")
    print(f"- Only Basic Greetings lesson has correct Tamil audio")
    print(f"- Colors lesson still has English-accented audio")
    print(f"- This is why you're still hearing English accent!")

if __name__ == "__main__":
    main() 