#!/usr/bin/env python3
"""
🎵 TIER 1 AUDIO GENERATOR
Generates audio for all 21 Tier 1 languages following AUDIO_STRUCTURE_AND_NAMING_STANDARDS.md

Features:
- ElevenLabs integration with approved voices
- Parallel audio generation across languages
- Proper file naming and storage structure  
- Supabase storage upload and URL linking
- Existing file detection to avoid regeneration
- Cost optimization and quota management
"""

import os
import json
import requests
import time
import uuid
from typing import Dict, List, Any, Tuple
from datetime import datetime
import random
import concurrent.futures
from threading import Lock
import re
from pathlib import Path

# ElevenLabs configuration
ELEVENLABS_API_KEY = "***************************************************"

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Thread-safe counters
audio_generation_lock = Lock()
audio_files_generated = 0
total_cost_estimate = 0.0

# Tier 1 Languages - TAMIL ONLY FOR NOW
TIER1_LANGUAGES = {
    'tamil': {'name': 'Tamil', 'code': 'ta', 'voice_id': 'jsCqWAovK2LkecY7zXl4'}  # Freya (confirmed working)
}

# Voice settings for optimal quality
VOICE_SETTINGS = {
    'jsCqWAovK2LkecY7zXl4': {'stability': 0.7, 'similarity_boost': 0.85, 'style': 0.25},  # Freya
    'MF3mGyEYCl7XYWbV9V6O': {'stability': 0.4, 'similarity_boost': 0.7, 'style': 0.6},   # Elli
    'VR6AewLTigWG4xSOukaG': {'stability': 0.6, 'similarity_boost': 0.8, 'style': 0.3},   # Arnold
    'pNInz6obpgDQGcFmaJgB': {'stability': 0.5, 'similarity_boost': 0.75, 'style': 0.4},  # Elli (Alternative)
    'yoZ06aMxZJJ28mfd3POQ': {'stability': 0.6, 'similarity_boost': 0.8, 'style': 0.35}   # Sam
}

class Tier1AudioGenerator:
    """Comprehensive audio generator for all Tier 1 languages"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        self.elevenlabs_headers = {
            'Accept': 'audio/mpeg',
            'Content-Type': 'application/json',
            'xi-api-key': ELEVENLABS_API_KEY
        }
        self.audio_cache = {}  # Cache existing audio URLs
        
    def create_lesson_slug(self, title: str) -> str:
        """Convert lesson title to URL-safe slug"""
        # Convert to lowercase and replace spaces with underscores
        slug = title.lower()
        # Remove special characters, keep only alphanumeric and underscores
        slug = re.sub(r'[^a-z0-9\s]', '', slug)
        # Replace spaces with underscores
        slug = re.sub(r'\s+', '_', slug)
        # Remove multiple underscores
        slug = re.sub(r'_+', '_', slug)
        # Remove leading/trailing underscores
        slug = slug.strip('_')
        return slug
    
    def check_existing_audio(self, storage_path: str) -> bool:
        """Check if audio file already exists in Supabase storage"""
        url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
        
        try:
            response = requests.head(url, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def generate_audio_file(self, text: str, voice_id: str, language: str) -> bytes:
        """Generate audio using ElevenLabs API"""
        global audio_files_generated, total_cost_estimate
        
        # Get voice settings
        settings = VOICE_SETTINGS.get(voice_id, {
            'stability': 0.6, 'similarity_boost': 0.8, 'style': 0.3
        })
        
        data = {
            'text': text,
            'model_id': 'eleven_multilingual_v2',
            'voice_settings': settings
        }
        
        try:
            # Add delay to respect rate limits
            time.sleep(random.uniform(2, 5))
            
            response = requests.post(
                f'https://api.elevenlabs.io/v1/text-to-speech/{voice_id}',
                json=data,
                headers=self.elevenlabs_headers,
                timeout=30
            )
            
            if response.status_code == 200:
                with audio_generation_lock:
                    audio_files_generated += 1
                    # Estimate cost (roughly $0.30 per 1000 characters)
                    total_cost_estimate += len(text) * 0.0003
                
                print(f"      ✅ Generated audio ({len(text)} chars)")
                return response.content
            else:
                print(f"      ❌ ElevenLabs error {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            print(f"      ❌ Audio generation failed: {e}")
            return None
    
    def upload_to_supabase(self, audio_data: bytes, storage_path: str) -> str:
        """Upload audio file to Supabase storage"""
        try:
            # Upload to Supabase storage
            upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{storage_path}"
            
            upload_headers = {
                'apikey': SUPABASE_KEY,
                'Authorization': f'Bearer {SUPABASE_KEY}',
                'Content-Type': 'audio/mpeg'
            }
            
            response = requests.post(
                upload_url,
                data=audio_data,
                headers=upload_headers,
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                # Return public URL
                public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
                print(f"      ✅ Uploaded: {storage_path}")
                return public_url
            else:
                print(f"      ❌ Upload failed {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            print(f"      ❌ Upload error: {e}")
            return None
    
    def get_lessons_for_path(self, path_id: str) -> List[Dict[str, Any]]:
        """Get all lessons for a learning path"""
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={
                'path_id': f'eq.{path_id}',
                'select': 'id,title,description,difficulty_level,content_metadata,sequence_order'
            }
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to fetch lessons for path {path_id}")
            return []
    
    def generate_lesson_audio(self, lesson: Dict[str, Any], language: str, language_info: Dict[str, str]) -> Dict[str, str]:
        """Generate all audio files for a single lesson"""
        lesson_id = lesson['id']
        title = lesson.get('title', 'Unknown Lesson')
        content = lesson.get('content_metadata', {})
        difficulty = lesson.get('difficulty_level', 1)
        
        # Create lesson slug
        lesson_slug = self.create_lesson_slug(title)
        
        # Determine level folder
        level_map = {1: 'a1', 2: 'a2', 3: 'b1', 4: 'b2', 5: 'c1', 6: 'c2'}
        level_folder = level_map.get(difficulty, 'a1')
        
        print(f"    🎵 Generating audio for: {title}")
        print(f"       📁 Storage path: {language}/{level_folder}/{lesson_slug}/")
        
        audio_urls = {}
        voice_id = language_info['voice_id']
        
        # Generate vocabulary audio
        vocabulary = content.get('vocabulary', [])
        for i, vocab in enumerate(vocabulary, 1):
            word = vocab.get('word', '')
            example = vocab.get('example', '')
            
            if word:
                # Word audio
                word_path = f"{language}/{level_folder}/{lesson_slug}/vocab_{i:02d}_word.mp3"
                if not self.check_existing_audio(word_path):
                    audio_data = self.generate_audio_file(word, voice_id, language)
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, word_path)
                        if url:
                            audio_urls[f'vocab_{i:02d}_word'] = url
                else:
                    audio_urls[f'vocab_{i:02d}_word'] = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{word_path}"
                    print(f"      ✅ Using existing: vocab_{i:02d}_word.mp3")
            
            if example:
                # Example sentence audio
                sentence_path = f"{language}/{level_folder}/{lesson_slug}/vocab_{i:02d}_sentence.mp3"
                if not self.check_existing_audio(sentence_path):
                    # Extract just the target language part before translation
                    sentence_text = example.split(' - ')[0] if ' - ' in example else example
                    # Remove pronunciation part in parentheses
                    sentence_text = re.sub(r'\([^)]*\)', '', sentence_text).strip()
                    
                    audio_data = self.generate_audio_file(sentence_text, voice_id, language)
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, sentence_path)
                        if url:
                            audio_urls[f'vocab_{i:02d}_sentence'] = url
                else:
                    audio_urls[f'vocab_{i:02d}_sentence'] = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{sentence_path}"
                    print(f"      ✅ Using existing: vocab_{i:02d}_sentence.mp3")
        
        # Generate conversation audio
        conversations = content.get('conversations', [])
        conv_count = 0
        for conv in conversations:
            exchanges = conv.get('exchanges', [])
            for j, exchange in enumerate(exchanges, 1):
                conv_count += 1
                text = exchange.get('text', '')
                speaker = exchange.get('speaker', 'speaker_a').lower().replace(' ', '_')
                
                if text:
                    conv_path = f"{language}/{level_folder}/{lesson_slug}/conv_{conv_count:02d}_{speaker}.mp3"
                    if not self.check_existing_audio(conv_path):
                        audio_data = self.generate_audio_file(text, voice_id, language)
                        if audio_data:
                            url = self.upload_to_supabase(audio_data, conv_path)
                            if url:
                                audio_urls[f'conv_{conv_count:02d}_{speaker}'] = url
                    else:
                        audio_urls[f'conv_{conv_count:02d}_{speaker}'] = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{conv_path}"
                        print(f"      ✅ Using existing: conv_{conv_count:02d}_{speaker}.mp3")
        
        # Generate grammar audio
        grammar_points = content.get('grammar_points', [])
        for i, grammar in enumerate(grammar_points, 1):
            examples = grammar.get('examples', [])
            
            # Grammar explanation (in English)
            explanation = grammar.get('explanation', '')
            if explanation:
                explanation_path = f"{language}/{level_folder}/{lesson_slug}/grammar_{i:02d}_explanation.mp3"
                if not self.check_existing_audio(explanation_path):
                    # Use English voice for explanations
                    english_voice = 'pNInz6obpgDQGcFmaJgB'  # Elli
                    audio_data = self.generate_audio_file(explanation, english_voice, 'english')
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, explanation_path)
                        if url:
                            audio_urls[f'grammar_{i:02d}_explanation'] = url
                else:
                    audio_urls[f'grammar_{i:02d}_explanation'] = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{explanation_path}"
                    print(f"      ✅ Using existing: grammar_{i:02d}_explanation.mp3")
            
            # Grammar examples (in target language)
            for j, example in enumerate(examples, 1):
                if example:
                    example_path = f"{language}/{level_folder}/{lesson_slug}/grammar_{i:02d}_example_{j}.mp3"
                    if not self.check_existing_audio(example_path):
                        audio_data = self.generate_audio_file(example, voice_id, language)
                        if audio_data:
                            url = self.upload_to_supabase(audio_data, example_path)
                            if url:
                                audio_urls[f'grammar_{i:02d}_example_{j}'] = url
                    else:
                        audio_urls[f'grammar_{i:02d}_example_{j}'] = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{example_path}"
                        print(f"      ✅ Using existing: grammar_{i:02d}_example_{j}.mp3")
        
        # Generate exercise audio
        exercises = content.get('exercises', [])
        for i, exercise in enumerate(exercises, 1):
            # Exercise question (in English)
            question = exercise.get('question', '')
            if question:
                question_path = f"{language}/{level_folder}/{lesson_slug}/exercise_{i:02d}_question.mp3"
                if not self.check_existing_audio(question_path):
                    # Use English voice for questions
                    english_voice = 'pNInz6obpgDQGcFmaJgB'  # Elli
                    audio_data = self.generate_audio_file(question, english_voice, 'english')
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, question_path)
                        if url:
                            audio_urls[f'exercise_{i:02d}_question'] = url
                else:
                    audio_urls[f'exercise_{i:02d}_question'] = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{question_path}"
                    print(f"      ✅ Using existing: exercise_{i:02d}_question.mp3")
            
            # Exercise options (in target language)
            options = exercise.get('options', [])
            option_labels = ['a', 'b', 'c', 'd']
            for j, option in enumerate(options):
                if option and j < len(option_labels):
                    label = option_labels[j]
                    option_path = f"{language}/{level_folder}/{lesson_slug}/exercise_{i:02d}_option_{label}.mp3"
                    if not self.check_existing_audio(option_path):
                        audio_data = self.generate_audio_file(option, voice_id, language)
                        if audio_data:
                            url = self.upload_to_supabase(audio_data, option_path)
                            if url:
                                audio_urls[f'exercise_{i:02d}_option_{label}'] = url
                    else:
                        audio_urls[f'exercise_{i:02d}_option_{label}'] = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{option_path}"
                        print(f"      ✅ Using existing: exercise_{i:02d}_option_{label}.mp3")
        
        # Update lesson with audio URLs
        if audio_urls:
            update_data = {
                'content_metadata': {
                    **content,
                    'audio_urls': audio_urls
                },
                'has_audio': True,
                'updated_at': datetime.now().isoformat()
            }
            
            update_response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=self.headers,
                params={'id': f'eq.{lesson_id}'},
                json=update_data
            )
            
            if update_response.status_code in [200, 204]:
                print(f"    ✅ Updated lesson with {len(audio_urls)} audio URLs")
            else:
                print(f"    ❌ Failed to update lesson with audio URLs")
        
        return audio_urls
    
    def process_language_audio(self, language: str, language_info: Dict[str, str], path_id: str) -> Dict[str, Any]:
        """Generate audio for all lessons in a language"""
        print(f"\n🎵 GENERATING AUDIO FOR {language_info['name'].upper()}")
        print("="*60)
        
        start_time = datetime.now()
        
        # Get all lessons
        lessons = self.get_lessons_for_path(path_id)
        print(f"📚 Found {len(lessons)} lessons")
        
        if not lessons:
            return {'error': 'No lessons found', 'language': language}
        
        # Process lessons
        total_audio_urls = 0
        processed_lessons = 0
        
        for lesson in lessons:
            try:
                audio_urls = self.generate_lesson_audio(lesson, language, language_info)
                total_audio_urls += len(audio_urls)
                processed_lessons += 1
                
                # Progress update
                if processed_lessons % 5 == 0:
                    print(f"  📊 Progress: {processed_lessons}/{len(lessons)} lessons | {total_audio_urls} audio files")
                
                # Brief pause between lessons
                time.sleep(5)
                
            except Exception as e:
                print(f"    ❌ Error processing lesson {lesson.get('title', 'Unknown')}: {e}")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        result = {
            'language': language,
            'language_name': language_info['name'],
            'lessons_processed': processed_lessons,
            'total_lessons': len(lessons),
            'audio_files_generated': total_audio_urls,
            'duration': str(duration),
            'voice_id': language_info['voice_id']
        }
        
        print(f"🎉 {language_info['name']} Audio Complete:")
        print(f"   📚 Lessons: {processed_lessons}/{len(lessons)}")
        print(f"   🎵 Audio Files: {total_audio_urls}")
        print(f"   ⏰ Duration: {duration}")
        
        return result
    
    def get_tamil_paths(self) -> Dict[str, List[str]]:
        """Get all Tamil learning paths by level"""
        print("🔍 Discovering Tamil learning paths...")
        
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/learning_paths",
            headers=self.headers,
            params={'select': 'id,name,level', 'limit': '1000'}
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch learning paths: {response.status_code}")
            return {}
        
        paths = response.json()
        tamil_paths = {}
        
        # Look for Tamil paths
        for path in paths:
            name = path.get('name', '').lower()
            level = path.get('level', '').upper()
            
            if 'tamil' in name:
                if level not in tamil_paths:
                    tamil_paths[level] = []
                tamil_paths[level].append({
                    'id': path['id'],
                    'name': path['name']
                })
                print(f"  📁 Found Tamil {level}: {path['id']} - {path['name']}")
        
        print(f"✅ Discovered Tamil paths for {len(tamil_paths)} levels")
        return tamil_paths
    
    def run_tier1_audio_generation(self, max_workers: int = 3):
        """Generate audio for all Tier 1 languages in parallel"""
        print("🎵 TIER 1 AUDIO GENERATOR")
        print("="*70)
        print("Generating audio for all 21 Tier 1 languages following audio standards")
        print(f"Using {max_workers} parallel workers")
        print()
        
        start_time = datetime.now()
        
        # Get language paths
        language_paths = self.get_language_paths()
        
        if not language_paths:
            print("❌ No language paths found. Run tier1_languages_complete_creator.py first.")
            return
        
        # Process languages in parallel batches
        language_items = [(lang, TIER1_LANGUAGES[lang], path_id) 
                         for lang, path_id in language_paths.items()]
        batch_size = max_workers
        
        all_results = {}
        
        for i in range(0, len(language_items), batch_size):
            batch = language_items[i:i+batch_size]
            
            print(f"\n🔄 PROCESSING AUDIO BATCH {i//batch_size + 1}: {', '.join([info['name'] for _, info, _ in batch])}")
            print("="*70)
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(batch)) as executor:
                future_to_language = {
                    executor.submit(self.process_language_audio, language, language_info, path_id): language
                    for language, language_info, path_id in batch
                }
                
                for future in concurrent.futures.as_completed(future_to_language):
                    language = future_to_language[future]
                    try:
                        result = future.result()
                        all_results[language] = result
                        print(f"✅ {TIER1_LANGUAGES[language]['name']} audio complete")
                    except Exception as e:
                        all_results[language] = {'error': str(e)}
                        print(f"❌ {TIER1_LANGUAGES[language]['name']} audio failed: {e}")
            
            # Wait between batches to manage costs and API limits
            if i + batch_size < len(language_items):
                print(f"\n⏰ Batch {i//batch_size + 1} complete. Waiting 300s before next batch...")
                time.sleep(300)  # 5 minutes between batches
        
        # Generate final report
        end_time = datetime.now()
        duration = end_time - start_time
        
        self.generate_audio_report(all_results, duration)
    
    def generate_audio_report(self, results: Dict[str, Any], duration):
        """Generate comprehensive audio generation report"""
        global audio_files_generated, total_cost_estimate
        
        print("\n" + "="*70)
        print("🎵 TIER 1 AUDIO GENERATION COMPLETE!")
        print("="*70)
        print(f"⏰ Total Duration: {duration}")
        print(f"🎵 Total Audio Files: {audio_files_generated}")
        print(f"💰 Estimated Cost: ${total_cost_estimate:.2f}")
        print()
        
        # Summary statistics
        total_languages = len(TIER1_LANGUAGES)
        successful_languages = 0
        total_audio_files = 0
        
        print("📊 RESULTS BY LANGUAGE:")
        print("-" * 70)
        print(f"{'Language':<15} | {'Lessons':<10} | {'Audio Files':<12} | {'Status'}")
        print("-" * 70)
        
        for language, result in results.items():
            language_info = TIER1_LANGUAGES[language]
            
            if 'error' not in result:
                lessons_processed = result['lessons_processed']
                audio_count = result['audio_files_generated']
                
                total_audio_files += audio_count
                
                status = "✅" if lessons_processed > 0 else "⚠️"
                
                print(f"{language_info['name']:<15} | {lessons_processed:^10} | {audio_count:^12} | {status}")
                
                if lessons_processed > 0:
                    successful_languages += 1
            else:
                error_msg = result.get('error', 'Unknown error')[:20]
                print(f"{language_info['name']:<15} | ERROR: {error_msg}... | ❌")
        
        print("-" * 70)
        print(f"📈 GRAND TOTALS:")
        print(f"✅ Successful Languages: {successful_languages}/{total_languages}")
        print(f"🎵 Total Audio Files: {total_audio_files}")
        print(f"💰 Total Estimated Cost: ${total_cost_estimate:.2f}")
        
        if successful_languages >= total_languages * 0.8:
            print("\n🎉 AUDIO GENERATION SUCCESS!")
            print("✅ Most Tier 1 languages have audio generated")
            print("✅ Following AUDIO_STRUCTURE_AND_NAMING_STANDARDS.md")
        else:
            print("\n⚠️  PARTIAL AUDIO SUCCESS")
            print("🔄 Some languages need retry or manual intervention")

def main():
    print("🎵 TAMIL AUDIO GENERATOR")
    print("="*60)
    print("Generating audio for Tamil lessons following audio standards")
    print("Using 2 parallel workers")
    print()
    
    generator = Tier1AudioGenerator()
    
    # Get Tamil paths
    tamil_paths = generator.get_tamil_paths()
    
    if not tamil_paths:
        print("❌ No Tamil paths found!")
        return
    
    print(f"✅ Found Tamil paths for {len(tamil_paths)} levels")
    
    # Process each level
    total_audio_files = 0
    start_time = datetime.now()
    
    for level, paths in tamil_paths.items():
        print(f"\n🎵 PROCESSING TAMIL {level}")
        print("="*50)
        
        for path_info in paths:
            path_id = path_info['id']
            path_name = path_info['name']
            
            print(f"📚 Processing: {path_name}")
            
            # Get lessons for this path
            lessons = generator.get_lessons_for_path(path_id)
            
            if not lessons:
                print(f"  ⚠️  No lessons found for {path_name}")
                continue
            
            print(f"  📖 Found {len(lessons)} lessons")
            
            # Process each lesson
            for lesson in lessons:
                try:
                    audio_urls = generator.generate_lesson_audio(
                        lesson, 'tamil', TIER1_LANGUAGES['tamil']
                    )
                    total_audio_files += len(audio_urls)
                    
                    # Update lesson with audio URLs if any generated
                    if audio_urls:
                        # Update content_metadata with audio URLs
                        content = lesson.get('content_metadata', {})
                        content['audio_urls'] = audio_urls
                        
                        # Update lesson in database
                        update_response = requests.patch(
                            f"{SUPABASE_URL}/rest/v1/lessons",
                            headers=generator.headers,
                            params={'id': f'eq.{lesson["id"]}'},
                            json={'content_metadata': content}
                        )
                        
                        if update_response.status_code == 204:
                            print(f"    ✅ Updated lesson with {len(audio_urls)} audio URLs")
                        else:
                            print(f"    ⚠️  Failed to update lesson audio URLs")
                    
                    time.sleep(2)  # Brief pause between lessons
                    
                except Exception as e:
                    print(f"    ❌ Error processing lesson {lesson.get('title', 'Unknown')}: {e}")
        
        print(f"✅ Tamil {level} complete")
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n🎵 TAMIL AUDIO GENERATION COMPLETE!")
    print("="*60)
    print(f"⏰ Duration: {duration}")
    print(f"🎵 Total Audio Files: {total_audio_files}")
    print(f"💰 Estimated Cost: ${total_cost_estimate:.2f}")

if __name__ == "__main__":
    main() 