#!/usr/bin/env python3
"""
🔍 DETAILED CONTENT EXAMINER
Examines specific lessons to understand the exact nature of issues found in the audit.
"""

import json
import requests
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil Learning Path IDs
TAMIL_PATHS = {
    'A1': '6b427613-420f-4586-bce8-2773d722f0b4',
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

def examine_lesson_sample(level: str, lesson_number: int = 1):
    """Examine a specific lesson to understand its content structure"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    path_id = TAMIL_PATHS[level]
    
    # Get the specific lesson
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        params={
            'path_id': f'eq.{path_id}',
            'sequence_order': f'eq.{lesson_number}',
            'select': '*'
        }
    )
    
    if response.status_code != 200 or not response.json():
        print(f"❌ Could not fetch {level} lesson {lesson_number}")
        return
    
    lesson = response.json()[0]
    
    print(f"\n🔍 EXAMINING {level} LESSON {lesson_number}")
    print("=" * 60)
    print(f"Title: {lesson.get('title', 'N/A')}")
    print(f"Description: {lesson.get('description', 'N/A')}")
    print()
    
    content_metadata = lesson.get('content_metadata', {})
    
    if not content_metadata:
        print("❌ NO CONTENT METADATA FOUND")
        return
    
    # Examine vocabulary
    vocabulary = content_metadata.get('vocabulary', [])
    print(f"📚 VOCABULARY ({len(vocabulary)} items):")
    if vocabulary:
        for i, vocab in enumerate(vocabulary[:3], 1):  # Show first 3
            print(f"  {i}. Word: {vocab.get('word', 'N/A')}")
            print(f"     Translation: {vocab.get('translation', 'N/A')}")
            print(f"     Example: {vocab.get('example', 'N/A')}")
            print(f"     Pronunciation: {vocab.get('pronunciation', 'N/A')}")
            print()
    else:
        print("  ❌ No vocabulary found")
    
    # Examine conversations
    conversations = content_metadata.get('conversations', [])
    print(f"💬 CONVERSATIONS ({len(conversations)} items):")
    if conversations:
        for i, conv in enumerate(conversations[:2], 1):  # Show first 2
            print(f"  {i}. Title: {conv.get('title', 'N/A')}")
            print(f"     Scenario: {conv.get('scenario', 'N/A')}")
            exchanges = conv.get('exchanges', [])
            if exchanges:
                print(f"     First exchange: {exchanges[0].get('text', 'N/A')}")
                print(f"     Translation: {exchanges[0].get('translation', 'N/A')}")
                print(f"     Pronunciation: {exchanges[0].get('pronunciation', 'N/A')}")
            print()
    else:
        print("  ❌ No conversations found")
    
    # Examine grammar
    grammar = content_metadata.get('grammar_points', [])
    print(f"📖 GRAMMAR ({len(grammar)} items):")
    if grammar:
        for i, gram in enumerate(grammar[:2], 1):  # Show first 2
            print(f"  {i}. Rule: {gram.get('rule', 'N/A')}")
            print(f"     Explanation: {gram.get('explanation', 'N/A')[:100]}...")
            examples = gram.get('examples', [])
            if examples:
                print(f"     Example: {examples[0] if examples else 'N/A'}")
            print()
    else:
        print("  ❌ No grammar found")
    
    # Examine exercises
    exercises = content_metadata.get('exercises', [])
    print(f"🧩 EXERCISES ({len(exercises)} items):")
    if exercises:
        for i, exercise in enumerate(exercises[:2], 1):  # Show first 2
            print(f"  {i}. Type: {exercise.get('type', 'N/A')}")
            print(f"     Question: {exercise.get('question', 'N/A')}")
            options = exercise.get('options', [])
            if options:
                print(f"     Options: {', '.join(options[:2])}...")
            print(f"     Correct Answer: {exercise.get('correctAnswer', 'N/A')}")
            print()
    else:
        print("  ❌ No exercises found")

def main():
    """Main function to examine sample lessons"""
    
    print("🔍 DETAILED CONTENT EXAMINATION")
    print("=" * 60)
    print("Examining sample lessons from each level to understand content issues...")
    
    # Examine one lesson from each level
    for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
        examine_lesson_sample(level, 1)
    
    print("\n" + "=" * 60)
    print("EXAMINATION COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main() 