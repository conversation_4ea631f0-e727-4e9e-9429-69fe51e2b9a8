#!/usr/bin/env python3
"""
🎵 CORRECTED TAMIL AUDIO GENERATION
Uses the CORRECT Tamil voice: C2RGMrNBTZaNfddRPeRH (Nila - Warm & Expressive Tamil Voice)
Generates comprehensive audio for Tamil A1 Basic Greetings lesson
"""

import os
import json
import requests
import time
from typing import Dict, Any, Optional

# Configuration
ELEVENLABS_API_KEY = "***************************************************"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# CORRECT TAMIL VOICE - Nila
TAMIL_VOICE_ID = "C2RGMrNBTZaNfddRPeRH"  # Nila - Warm & Expressive Tamil Voice

# Voice settings optimized for Tamil
VOICE_SETTINGS = {
    'stability': 0.8,
    'similarity_boost': 0.9,
    'style': 0.3,
    'use_speaker_boost': True
}

# Headers
SUPABASE_HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

ELEVENLABS_HEADERS = {
    'Accept': 'audio/mpeg',
    'Content-Type': 'application/json',
    'xi-api-key': ELEVENLABS_API_KEY
}

class CorrectTamilAudioGenerator:
    """Correct Tamil audio generator using proper Nila voice"""
    
    def __init__(self):
        self.audio_count = 0
        self.success_count = 0
        self.error_count = 0
        
    def generate_audio(self, text: str) -> Optional[bytes]:
        """Generate audio using CORRECT Tamil voice (Nila)"""
        
        data = {
            'text': text,
            'model_id': 'eleven_turbo_v2_5',
            'voice_settings': VOICE_SETTINGS
        }
        
        try:
            response = requests.post(
                f'https://api.elevenlabs.io/v1/text-to-speech/{TAMIL_VOICE_ID}',
                json=data,
                headers=ELEVENLABS_HEADERS,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✅ Generated Tamil audio: {text[:30]}... ({len(text)} chars)")
                self.success_count += 1
                return response.content
            else:
                print(f"❌ ElevenLabs error {response.status_code}: {response.text}")
                self.error_count += 1
                return None
                
        except Exception as e:
            print(f"❌ Audio generation failed: {e}")
            self.error_count += 1
            return None
    
    def create_filename(self, lesson_slug: str, content_type: str, index: int, audio_type: str) -> str:
        """Create proper filename"""
        filename = f"{lesson_slug}_{content_type}_{index:02d}_{audio_type}.mp3"
        return filename
    
    def upload_to_supabase(self, audio_data: bytes, file_path: str) -> Optional[str]:
        """Upload audio to Supabase storage"""
        try:
            upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{file_path}"
            
            response = requests.post(
                upload_url,
                data=audio_data,
                headers={
                    'apikey': SUPABASE_KEY,
                    'Authorization': f'Bearer {SUPABASE_KEY}',
                    'Content-Type': 'audio/mpeg',
                    'Cache-Control': 'max-age=3600'
                }
            )
            
            if response.status_code in [200, 201]:
                public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{file_path}"
                print(f"✅ Uploaded: {file_path}")
                return public_url
            else:
                print(f"❌ Upload failed {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Upload error: {e}")
            return None
    
    def get_lesson_content(self, lesson_id: str) -> Optional[Dict]:
        """Get lesson content from database"""
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=SUPABASE_HEADERS,
                params={
                    'id': f'eq.{lesson_id}',
                    'select': 'id,title,content_metadata'
                }
            )
            
            if response.status_code == 200:
                lessons = response.json()
                return lessons[0] if lessons else None
            else:
                print(f"❌ Failed to fetch lesson: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error fetching lesson: {e}")
            return None
    
    def generate_comprehensive_audio(self, lesson_id: str) -> Dict[str, str]:
        """Generate ALL audio types for a lesson"""
        print(f"🎵 Generating comprehensive Tamil audio using CORRECT Nila voice")
        print(f"🎤 Voice: C2RGMrNBTZaNfddRPeRH (Nila - Warm & Expressive Tamil Voice)")
        print(f"📚 Lesson ID: {lesson_id}")
        
        audio_urls = {}
        
        # Get lesson content
        lesson_data = self.get_lesson_content(lesson_id)
        if not lesson_data:
            print(f"❌ Could not fetch lesson content")
            return {}
        
        content_metadata = lesson_data.get('content_metadata', {})
        lesson_title = lesson_data.get('title', 'unknown')
        lesson_slug = lesson_title.lower().replace(' ', '_').replace(',', '').replace(':', '').replace('and', '').replace('&', '')
        
        print(f"📚 Processing: {lesson_title}")
        print(f"🔗 Slug: {lesson_slug}")
        
        # 1. VOCABULARY AUDIO
        vocabulary = content_metadata.get('vocabulary', [])
        print(f"📝 Generating {len(vocabulary)} vocabulary items...")
        
        for i, vocab in enumerate(vocabulary, 1):
            word = vocab.get('word', '')
            meaning = vocab.get('meaning', '')
            example = vocab.get('example', '')
            
            if word:
                # Word audio
                filename = self.create_filename(lesson_slug, 'vocab', i, 'word')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(word)
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'vocab_{i:02d}_word'] = url
                
                time.sleep(1)
            
            if example:
                # Example sentence audio
                filename = self.create_filename(lesson_slug, 'vocab', i, 'example')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(example)
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'vocab_{i:02d}_example'] = url
                
                time.sleep(1)
            
            if meaning:
                # Meaning audio
                filename = self.create_filename(lesson_slug, 'vocab', i, 'meaning')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(meaning)
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'vocab_{i:02d}_meaning'] = url
                
                time.sleep(1)
        
        # 2. EXERCISE AUDIO
        exercises = content_metadata.get('exercises', [])
        print(f"🎯 Generating {len(exercises)} exercise items...")
        
        for i, exercise in enumerate(exercises, 1):
            question = exercise.get('question', '')
            options = exercise.get('options', [])
            
            if question:
                # Question audio
                filename = self.create_filename(lesson_slug, 'exercise', i, 'question')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(question)
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'exercise_{i:02d}_question'] = url
                
                time.sleep(1)
            
            # Option audio
            for j, option in enumerate(options[:4], 1):
                if option:
                    option_letter = chr(ord('a') + j - 1)
                    filename = self.create_filename(lesson_slug, 'exercise', i, f'option_{option_letter}')
                    file_path = f"tamil/a1/{lesson_slug}/{filename}"
                    
                    audio_data = self.generate_audio(option)
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, file_path)
                        if url:
                            audio_urls[f'exercise_{i:02d}_option_{option_letter}'] = url
                    
                    time.sleep(1)
        
        # 3. GRAMMAR EXPLANATIONS
        grammar_points = content_metadata.get('grammar_points', [])
        print(f"📖 Generating {len(grammar_points)} grammar explanations...")
        
        for i, grammar in enumerate(grammar_points, 1):
            explanation = grammar.get('explanation', '')
            examples = grammar.get('examples', [])
            
            if explanation:
                filename = self.create_filename(lesson_slug, 'grammar', i, 'explanation')
                file_path = f"tamil/a1/{lesson_slug}/{filename}"
                
                audio_data = self.generate_audio(explanation)
                if audio_data:
                    url = self.upload_to_supabase(audio_data, file_path)
                    if url:
                        audio_urls[f'grammar_{i:02d}_explanation'] = url
                
                time.sleep(1)
            
            for j, example in enumerate(examples, 1):
                if example:
                    filename = self.create_filename(lesson_slug, 'grammar', i, f'example_{j}')
                    file_path = f"tamil/a1/{lesson_slug}/{filename}"
                    
                    audio_data = self.generate_audio(example)
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, file_path)
                        if url:
                            audio_urls[f'grammar_{i:02d}_example_{j}'] = url
                    
                    time.sleep(1)
        
        # 4. CONVERSATIONS/DIALOGUES
        conversations = content_metadata.get('conversations', [])
        if conversations:
            print(f"💬 Generating {len(conversations)} conversation dialogues...")
            
            for i, conv in enumerate(conversations, 1):
                dialogue = conv.get('dialogue', '')
                if dialogue:
                    filename = self.create_filename(lesson_slug, 'conversation', i, 'dialogue')
                    file_path = f"tamil/a1/{lesson_slug}/{filename}"
                    
                    audio_data = self.generate_audio(dialogue)
                    if audio_data:
                        url = self.upload_to_supabase(audio_data, file_path)
                        if url:
                            audio_urls[f'conversation_{i:02d}_dialogue'] = url
                    
                    time.sleep(1)
        
        print(f"✅ Generated {len(audio_urls)} audio files using CORRECT Tamil voice")
        print(f"📊 Success: {self.success_count}, Errors: {self.error_count}")
        
        return audio_urls
    
    def update_lesson_audio_urls(self, lesson_id: str, audio_urls: Dict[str, str]) -> bool:
        """Update lesson with generated audio URLs"""
        try:
            response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=SUPABASE_HEADERS,
                params={'id': f'eq.{lesson_id}'},
                json={
                    'content_metadata': {
                        'audio_urls': audio_urls
                    }
                }
            )
            
            if response.status_code in [200, 204]:
                print(f"✅ Updated lesson with {len(audio_urls)} audio URLs")
                return True
            else:
                print(f"❌ Failed to update lesson: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating lesson: {e}")
            return False

def generate_basic_greetings_audio():
    """Generate audio for Tamil A1 Basic Greetings lesson"""
    print("🎵 CORRECTED TAMIL AUDIO GENERATION")
    print("=" * 60)
    print("🎤 Using CORRECT Tamil voice: C2RGMrNBTZaNfddRPeRH")
    print("🗣️  Voice: Nila - Warm & Expressive Tamil Voice")
    print("📚 Lesson: Tamil A1 Basic Greetings and Introductions")
    print("=" * 60)
    
    # Basic Greetings lesson ID
    lesson_id = "342230c2-8ea9-495d-bbef-ab0bec4df7be"
    
    generator = CorrectTamilAudioGenerator()
    
    # Generate comprehensive audio
    audio_urls = generator.generate_comprehensive_audio(lesson_id)
    
    if audio_urls:
        # Update lesson with audio URLs
        generator.update_lesson_audio_urls(lesson_id, audio_urls)
        
        # Save results
        results = {
            'lesson_id': lesson_id,
            'lesson_title': 'Basic Greetings and Introductions',
            'voice_used': 'C2RGMrNBTZaNfddRPeRH (Nila - Warm & Expressive Tamil Voice)',
            'model_used': 'eleven_turbo_v2_5',
            'audio_count': len(audio_urls),
            'audio_urls': audio_urls
        }
        
        with open('correct_tamil_audio_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n🎉 SUCCESS!")
        print(f"📊 Generated {len(audio_urls)} audio files")
        print(f"🎤 Using CORRECT Tamil voice: Nila")
        print(f"📁 Results saved to: correct_tamil_audio_results.json")
        
        # Show sample URLs
        print(f"\n🎯 SAMPLE AUDIO URLS:")
        for key, url in list(audio_urls.items())[:3]:
            print(f"   {key}: {url}")
    else:
        print(f"\n❌ FAILED to generate audio")

if __name__ == "__main__":
    generate_basic_greetings_audio() 