#!/usr/bin/env python3
"""
Systematic Multi-Level Creator for NIRA Tamil Lessons
Creates A2, B1, B2, C1, C2 lessons following the proven A1 structure

This script implements the systematic approach:
1. Create lesson titles first for all levels
2. Create basic lesson structures 
3. Generate content systematically (vocab, grammar, conversations, exercises)
4. Avoid script failures through chunked processing

Content Requirements (A2-C2):
- 20 vocabulary items per lesson
- 10 grammar points per lesson
- 15 conversations per lesson  
- 15 practice exercises per lesson

Lesson Counts:
- A2: 30 lessons
- B1: 20 lessons
- B2: 20 lessons
- C1: 15 lessons
- C2: 15 lessons
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil Learning Path IDs (verified from database)
TAMIL_PATHS = {
    'A1': '6b427613-420f-4586-bce8-2773d722f0b4',
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

# Level Configuration
LEVEL_CONFIG = {
    'A2': {
        'difficulty_level': 2,
        'lesson_count': 30,
        'duration': 45,
        'description': 'Elementary level with complex conversations and advanced grammar'
    },
    'B1': {
        'difficulty_level': 3,
        'lesson_count': 20,
        'duration': 60,
        'description': 'Intermediate level with complex vocabulary and sophisticated grammar'
    },
    'B2': {
        'difficulty_level': 4,
        'lesson_count': 20,
        'duration': 75,
        'description': 'Upper-intermediate with specialized vocabulary and advanced concepts'
    },
    'C1': {
        'difficulty_level': 5,
        'lesson_count': 15,
        'duration': 90,
        'description': 'Advanced with sophisticated vocabulary and complex theoretical concepts'
    },
    'C2': {
        'difficulty_level': 6,
        'lesson_count': 15,
        'duration': 120,
        'description': 'Mastery level with native-level vocabulary and classical literary concepts'
    }
}

class SystematicMultiLevelCreator:
    """Systematic creator for all Tamil levels A2-C2"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def generate_lesson_titles(self, level: str) -> List[str]:
        """Generate appropriate lesson titles for each level"""
        
        config = LEVEL_CONFIG[level]
        lesson_count = config['lesson_count']
        
        prompt = f"""
        Generate exactly {lesson_count} lesson titles for Tamil {level} level.
        
        Requirements:
        - {level} difficulty level appropriate topics
        - Progressive complexity from basic to advanced within the level
        - Practical, real-world topics
        - Cultural relevance to Tamil speakers
        - No duplicates from A1 level topics
        
        A1 topics already covered: Basic Greetings, Family Members, Numbers, Colors, Food, 
        Body Parts, Weather, Home, Clothing, Transportation, Animals, Nature, etc.
        
        For {level} level, focus on:
        {self._get_level_focus(level)}
        
        Return as a simple numbered list:
        1. Topic Name
        2. Topic Name
        ...
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            titles = []
            
            for line in response.text.strip().split('\n'):
                if line.strip() and '. ' in line:
                    title = line.split('. ', 1)[1].strip()
                    if title:
                        titles.append(title)
            
            print(f"✅ Generated {len(titles)} lesson titles for {level}")
            return titles[:lesson_count]  # Ensure exact count
            
        except Exception as e:
            print(f"❌ Failed to generate titles for {level}: {e}")
            return []
    
    def _get_level_focus(self, level: str) -> str:
        """Get focus areas for each level"""
        focus_areas = {
            'A2': 'Complex daily activities, workplace basics, travel, shopping, health, education, hobbies',
            'B1': 'Professional contexts, abstract concepts, opinions, future plans, cultural topics, media',
            'B2': 'Specialized fields, academic topics, business, technology, social issues, literature',
            'C1': 'Professional expertise, academic research, complex analysis, cultural criticism, philosophy',
            'C2': 'Literary mastery, classical texts, linguistic theory, cultural heritage, expert discourse'
        }
        return focus_areas.get(level, 'General topics')
    
    def create_lesson_structure(self, title: str, level: str, sequence: int) -> Dict[str, Any]:
        """Create basic lesson structure"""
        
        config = LEVEL_CONFIG[level]
        path_id = TAMIL_PATHS[level]
        
        return {
            'path_id': path_id,
            'title': title,
            'description': f'{level} level lesson covering {title.lower()} with {config["description"]}',
            'lesson_type': 'comprehensive',
            'difficulty_level': config['difficulty_level'],
            'estimated_duration': config['duration'],
            'sequence_order': sequence,
            'learning_objectives': [
                f'Master {level} level vocabulary for {title.lower()}',
                f'Understand complex grammar structures',
                f'Engage in sophisticated conversations',
                f'Complete advanced practice exercises'
            ],
            'vocabulary_focus': [],
            'grammar_concepts': [f'{level} level grammar', 'Advanced sentence structures'],
            'cultural_notes': f'Tamil {title.lower()} in {level} level cultural context',
            'prerequisite_lessons': [],
            'content_metadata': {
                'vocabulary': [],  # Will be filled later
                'conversations': [],  # Will be filled later
                'grammar_points': [],  # Will be filled later
                'exercises': []  # Will be filled later
            },
            'is_active': True,
            'has_audio': False,  # Will be set after audio generation
            'audio_metadata': {
                'generated_audio_count': 0,
                'total_expected_audio': 150  # 20+20+30+20+60 files
            }
        }
    
    def create_lesson_in_database(self, lesson_data: Dict[str, Any]) -> bool:
        """Create lesson in Supabase database"""
        
        try:
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=self.headers,
                json=lesson_data
            )
            
            if response.status_code == 201:
                print(f"✅ Created lesson: {lesson_data['title']}")
                return True
            else:
                print(f"❌ Failed to create lesson: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Database error: {e}")
            return False
    
    def create_all_lesson_structures(self, level: str) -> Dict[str, Any]:
        """Create all lesson structures for a level"""
        
        print(f"\n🏗️  CREATING {level} LESSON STRUCTURES")
        print("=" * 50)
        
        # Generate lesson titles
        titles = self.generate_lesson_titles(level)
        
        if not titles:
            return {'created': 0, 'failed': len(titles)}
        
        results = {'created': 0, 'failed': []}
        
        for i, title in enumerate(titles, 1):
            print(f"\n📚 Creating {level} {i}/{len(titles)}: {title}")
            
            # Create lesson structure
            lesson_data = self.create_lesson_structure(title, level, i)
            
            # Add to database
            if self.create_lesson_in_database(lesson_data):
                results['created'] += 1
            else:
                results['failed'].append(title)
            
            # Prevent rate limiting
            time.sleep(1)
        
        return results

def main():
    """Main function - Create lesson structures for all levels"""
    
    print("🚀 SYSTEMATIC MULTI-LEVEL CREATOR")
    print("Creating lesson structures for A2, B1, B2, C1, C2")
    print("=" * 60)
    
    creator = SystematicMultiLevelCreator()
    
    # Process each level
    levels_to_process = ['A2', 'B1', 'B2', 'C1', 'C2']
    overall_results = {}
    
    for level in levels_to_process:
        print(f"\n🎯 PROCESSING {level} LEVEL")
        print(f"Target: {LEVEL_CONFIG[level]['lesson_count']} lessons")
        print("-" * 40)
        
        results = creator.create_all_lesson_structures(level)
        overall_results[level] = results
        
        print(f"\n📊 {level} RESULTS:")
        print(f"✅ Created: {results['created']}")
        print(f"❌ Failed: {len(results['failed'])}")
        
        # Wait between levels
        time.sleep(3)
    
    # Final summary
    print(f"\n📊 OVERALL RESULTS:")
    print("=" * 50)
    
    total_created = sum(r['created'] for r in overall_results.values())
    total_failed = sum(len(r['failed']) for r in overall_results.values())
    
    for level, results in overall_results.items():
        print(f"  • {level}: {results['created']} created, {len(results['failed'])} failed")
    
    print(f"\n🎉 TOTAL: {total_created} lessons created, {total_failed} failed")
    print("\n📋 NEXT STEPS:")
    print("1. Run content generation for each level")
    print("2. Generate audio for all lessons")
    print("3. Validate and test all content")

if __name__ == "__main__":
    main()
