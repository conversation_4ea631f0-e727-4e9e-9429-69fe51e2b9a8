#!/usr/bin/env python3
"""
Comprehensive Quality Validator for NIRA Tamil Lessons
Validates all content against the comprehensive quality checklist

This script ensures:
- No duplicate content
- Proper romanized Tamil pronunciations
- Correct content counts (20 vocab, 10 grammar, 15 conversations, 15 exercises)
- Cultural authenticity and appropriateness
- Database structure compliance
"""

import json
import requests
from typing import Dict, List, Any

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

TAMIL_PATHS = {
    'A1': '6b427613-420f-4586-bce8-2773d722f0b4',
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

class ComprehensiveQualityValidator:
    """Comprehensive quality validator for Tamil lessons"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        self.validation_results = {}
    
    def get_lessons_by_level(self, level: str) -> List[Dict[str, Any]]:
        """Get all lessons for a specific level"""
        
        path_id = TAMIL_PATHS[level]
        
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=self.headers,
                params={
                    'path_id': f'eq.{path_id}',
                    'select': 'id,title,sequence_order,content_metadata',
                    'order': 'sequence_order'
                }
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to fetch {level} lessons: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Error fetching lessons: {e}")
            return []
    
    def validate_vocabulary(self, vocabulary: List[Dict], lesson_title: str, level: str) -> Dict[str, Any]:
        """Validate vocabulary content"""
        
        issues = []
        
        # Count validation
        if len(vocabulary) != 20:
            issues.append(f"Expected 20 vocabulary items, found {len(vocabulary)}")
        
        # Uniqueness validation
        words = [v.get('word', '') for v in vocabulary]
        if len(set(words)) != len(words):
            issues.append("Duplicate vocabulary words found")
        
        # Content validation
        for i, vocab in enumerate(vocabulary):
            if not vocab.get('word'):
                issues.append(f"Vocabulary {i+1}: Missing word")
            if not vocab.get('translation'):
                issues.append(f"Vocabulary {i+1}: Missing translation")
            if not vocab.get('example'):
                issues.append(f"Vocabulary {i+1}: Missing example")
            
            # Check example format
            example = vocab.get('example', '')
            if example and '(' not in example or ')' not in example or '-' not in example:
                issues.append(f"Vocabulary {i+1}: Example missing proper format (romanization) - translation")
        
        return {
            'section': 'vocabulary',
            'passed': len(issues) == 0,
            'issues': issues,
            'count': len(vocabulary)
        }
    
    def validate_conversations(self, conversations: List[Dict], lesson_title: str, level: str) -> Dict[str, Any]:
        """Validate conversation content"""
        
        issues = []
        
        # Count validation
        if len(conversations) != 15:
            issues.append(f"Expected 15 conversations, found {len(conversations)}")
        
        # Content validation
        for i, conv in enumerate(conversations):
            dialogue = conv.get('dialogue', [])
            if not dialogue:
                issues.append(f"Conversation {i+1}: Empty dialogue")
                continue
            
            for j, exchange in enumerate(dialogue):
                if not exchange.get('tamil') and not exchange.get('text'):
                    issues.append(f"Conversation {i+1}, Exchange {j+1}: Missing Tamil text")
                if not exchange.get('pronunciation'):
                    issues.append(f"Conversation {i+1}, Exchange {j+1}: Missing pronunciation")
                if not exchange.get('translation') and not exchange.get('english'):
                    issues.append(f"Conversation {i+1}, Exchange {j+1}: Missing translation")
        
        return {
            'section': 'conversations',
            'passed': len(issues) == 0,
            'issues': issues,
            'count': len(conversations)
        }
    
    def validate_grammar(self, grammar: List[Dict], lesson_title: str, level: str) -> Dict[str, Any]:
        """Validate grammar content"""
        
        issues = []
        
        # Count validation
        if len(grammar) != 10:
            issues.append(f"Expected 10 grammar points, found {len(grammar)}")
        
        # Content validation
        for i, gram in enumerate(grammar):
            if not gram.get('concept') and not gram.get('rule'):
                issues.append(f"Grammar {i+1}: Missing concept/rule")
            if not gram.get('explanation'):
                issues.append(f"Grammar {i+1}: Missing explanation")
            if not gram.get('examples') and not gram.get('tamil_example'):
                issues.append(f"Grammar {i+1}: Missing examples")
        
        return {
            'section': 'grammar',
            'passed': len(issues) == 0,
            'issues': issues,
            'count': len(grammar)
        }
    
    def validate_exercises(self, exercises: List[Dict], lesson_title: str, level: str) -> Dict[str, Any]:
        """Validate exercise content"""
        
        issues = []
        
        # Count validation
        if len(exercises) != 15:
            issues.append(f"Expected 15 exercises, found {len(exercises)}")
        
        # Content validation
        for i, exercise in enumerate(exercises):
            if not exercise.get('question'):
                issues.append(f"Exercise {i+1}: Missing question")
            
            options = exercise.get('options', [])
            pronunciations = exercise.get('options_pronunciations', [])
            
            if not options:
                issues.append(f"Exercise {i+1}: Missing options")
            elif len(options) != len(pronunciations):
                issues.append(f"Exercise {i+1}: Options/pronunciations count mismatch ({len(options)} vs {len(pronunciations)})")
            
            # Check pronunciations
            for j, pronunciation in enumerate(pronunciations):
                if not pronunciation or len(pronunciation.strip()) == 0:
                    issues.append(f"Exercise {i+1}, Option {j+1}: Empty pronunciation")
            
            # Check correct answer
            correct_answer = exercise.get('correct_answer', exercise.get('correctAnswer'))
            if correct_answer is None:
                issues.append(f"Exercise {i+1}: Missing correct answer")
            elif correct_answer >= len(options):
                issues.append(f"Exercise {i+1}: Invalid correct answer index")
        
        return {
            'section': 'exercises',
            'passed': len(issues) == 0,
            'issues': issues,
            'count': len(exercises)
        }
    
    def validate_lesson(self, lesson: Dict[str, Any], level: str) -> Dict[str, Any]:
        """Validate a complete lesson"""
        
        lesson_id = lesson['id']
        lesson_title = lesson['title']
        content_metadata = lesson.get('content_metadata', {})
        
        print(f"\n🔍 Validating: {lesson_title}")
        
        # Extract content sections
        vocabulary = content_metadata.get('vocabulary', [])
        conversations = content_metadata.get('conversations', [])
        grammar = content_metadata.get('grammar_points', [])
        exercises = content_metadata.get('exercises', [])
        
        # Validate each section
        vocab_result = self.validate_vocabulary(vocabulary, lesson_title, level)
        conv_result = self.validate_conversations(conversations, lesson_title, level)
        grammar_result = self.validate_grammar(grammar, lesson_title, level)
        exercise_result = self.validate_exercises(exercises, lesson_title, level)
        
        # Overall validation
        all_passed = all([
            vocab_result['passed'],
            conv_result['passed'],
            grammar_result['passed'],
            exercise_result['passed']
        ])
        
        result = {
            'lesson_id': lesson_id,
            'lesson_title': lesson_title,
            'level': level,
            'passed': all_passed,
            'sections': {
                'vocabulary': vocab_result,
                'conversations': conv_result,
                'grammar': grammar_result,
                'exercises': exercise_result
            }
        }
        
        # Print results
        if all_passed:
            print(f"✅ {lesson_title}: All validations passed")
        else:
            print(f"❌ {lesson_title}: Validation failed")
            for section_name, section_result in result['sections'].items():
                if not section_result['passed']:
                    print(f"   📝 {section_name}: {len(section_result['issues'])} issues")
                    for issue in section_result['issues'][:3]:  # Show first 3 issues
                        print(f"      • {issue}")
        
        return result
    
    def validate_level(self, level: str) -> Dict[str, Any]:
        """Validate all lessons for a level"""
        
        print(f"\n📚 VALIDATING {level} LEVEL")
        print("=" * 50)
        
        lessons = self.get_lessons_by_level(level)
        
        if not lessons:
            return {'level': level, 'lessons': [], 'summary': {'total': 0, 'passed': 0, 'failed': 0}}
        
        results = []
        passed_count = 0
        
        for lesson in lessons:
            result = self.validate_lesson(lesson, level)
            results.append(result)
            if result['passed']:
                passed_count += 1
        
        summary = {
            'total': len(lessons),
            'passed': passed_count,
            'failed': len(lessons) - passed_count
        }
        
        print(f"\n📊 {level} SUMMARY: {passed_count}/{len(lessons)} lessons passed validation")
        
        return {
            'level': level,
            'lessons': results,
            'summary': summary
        }

def main():
    """Main validation function"""
    
    print("🔍 COMPREHENSIVE QUALITY VALIDATOR")
    print("Validating A2, B1, B2, C1, C2 Tamil lessons")
    print("=" * 60)
    
    validator = ComprehensiveQualityValidator()
    
    # Validate each level
    levels = ['A2', 'B1', 'B2', 'C1', 'C2']
    overall_results = {}
    
    for level in levels:
        result = validator.validate_level(level)
        overall_results[level] = result
    
    # Overall summary
    print(f"\n📊 OVERALL VALIDATION RESULTS")
    print("=" * 50)
    
    total_lessons = 0
    total_passed = 0
    
    for level, result in overall_results.items():
        summary = result['summary']
        total_lessons += summary['total']
        total_passed += summary['passed']
        
        print(f"  {level}: {summary['passed']}/{summary['total']} lessons passed")
    
    print(f"\n🎯 TOTAL: {total_passed}/{total_lessons} lessons passed validation")
    
    if total_passed == total_lessons:
        print("🎉 ALL LESSONS PASSED COMPREHENSIVE QUALITY VALIDATION!")
    else:
        print("⚠️  Some lessons need attention before production release")

if __name__ == "__main__":
    main()
