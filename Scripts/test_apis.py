#!/usr/bin/env python3
"""
🧪 API TEST SCRIPT
Tests both Gemini and OpenAI APIs to verify they're working properly
"""

import os
import requests
import json
import time

# API Keys
GEMINI_API_KEY = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'
OPENAI_API_KEY = "***********************************************************************************************************************************************************************"

def test_gemini():
    """Test Gemini API"""
    print("🧪 Testing Gemini API...")
    
    try:
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        model = genai.GenerativeModel("gemini-1.5-flash")
        
        simple_prompt = "Create a simple JSON object with just title and description for a Tamil A1 lesson. Return only valid JSON."
        
        response = model.generate_content(simple_prompt)
        
        if response and response.text:
            print(f"✅ Gemini Response: {response.text[:200]}...")
            try:
                json_data = json.loads(response.text.strip())
                print("✅ Gemini JSON parsing successful")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ Gemini JSON parsing failed: {e}")
                print(f"Raw response: {response.text}")
                return False
        else:
            print("❌ Gemini returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ Gemini error: {e}")
        return False

def test_openai():
    """Test OpenAI API"""
    print("\n🧪 Testing OpenAI API...")
    
    try:
        headers = {
            'Authorization': f'Bearer {OPENAI_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'gpt-4o-mini',
            'messages': [
                {'role': 'user', 'content': 'Create a simple JSON object with just title and description for a Tamil A1 lesson. Return only valid JSON.'}
            ],
            'max_tokens': 200,
            'temperature': 0.7
        }
        
        response = requests.post(
            'https://api.openai.com/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✅ OpenAI Response: {content[:200]}...")
            
            try:
                json_data = json.loads(content.strip())
                print("✅ OpenAI JSON parsing successful")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ OpenAI JSON parsing failed: {e}")
                print(f"Raw response: {content}")
                return False
        else:
            print(f"❌ OpenAI API error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI error: {e}")
        return False

def test_json_creation():
    """Test both APIs with a comprehensive JSON prompt"""
    print("\n🧪 Testing comprehensive JSON creation...")
    
    prompt = """Create a Tamil A1 lesson about family members. Return ONLY this JSON structure:
{
    "title": "Family Members",
    "description": "Basic Tamil vocabulary for family relationships",
    "vocabulary": [
        {"word": "அப்பா", "pronunciation": "appa", "meaning": "father"},
        {"word": "அம்மா", "pronunciation": "amma", "meaning": "mother"}
    ]
}"""
    
    # Test Gemini
    print("\n🤖 Testing Gemini with comprehensive prompt...")
    try:
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        model = genai.GenerativeModel("gemini-1.5-flash")
        
        response = model.generate_content(prompt)
        if response and response.text:
            clean_response = response.text.strip()
            # Remove any markdown formatting
            if clean_response.startswith('```json'):
                clean_response = clean_response.replace('```json', '').replace('```', '').strip()
            
            print(f"Raw Gemini: {clean_response}")
            json_data = json.loads(clean_response)
            print("✅ Gemini comprehensive JSON successful!")
        else:
            print("❌ Gemini returned empty response")
            
    except Exception as e:
        print(f"❌ Gemini comprehensive test failed: {e}")
    
    # Test OpenAI
    print("\n🤖 Testing OpenAI with comprehensive prompt...")
    try:
        headers = {
            'Authorization': f'Bearer {OPENAI_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'gpt-4o-mini',
            'messages': [{'role': 'user', 'content': prompt}],
            'max_tokens': 500,
            'temperature': 0.7
        }
        
        response = requests.post(
            'https://api.openai.com/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content'].strip()
            
            # Remove any markdown formatting
            if content.startswith('```json'):
                content = content.replace('```json', '').replace('```', '').strip()
            
            print(f"Raw OpenAI: {content}")
            json_data = json.loads(content)
            print("✅ OpenAI comprehensive JSON successful!")
        else:
            print(f"❌ OpenAI error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ OpenAI comprehensive test failed: {e}")

def main():
    print("🧪 API CONNECTIVITY TEST")
    print("=" * 50)
    
    gemini_works = test_gemini()
    openai_works = test_openai()
    
    test_json_creation()
    
    print("\n📊 TEST RESULTS:")
    print(f"Gemini API: {'✅ Working' if gemini_works else '❌ Failed'}")
    print(f"OpenAI API: {'✅ Working' if openai_works else '❌ Failed'}")
    
    if gemini_works or openai_works:
        print("\n✅ At least one API is working - proceeding with fixes")
    else:
        print("\n❌ Both APIs failed - need to investigate")

if __name__ == "__main__":
    main() 