#!/usr/bin/env python3
"""
🚀 COMPREHENSIVE TAMIL LESSON FIXER - OPTIMIZED VERSION
Using Gemini Flash 2.0 Lite with conservative rate limiting

Phases:
1. Fix Tamil titles/descriptions to English  
2. Generate missing content for empty lessons
3. Replace placeholder content with authentic Tamil content
4. Add missing pronunciations

Optimized for quota limits with smart batching and retry logic.
"""

import os
import json
import requests
import time
import re
from typing import Dict, List, Any, Tuple
from datetime import datetime
import random

# Set up Gemini API
os.environ['GEMINI_API_KEY'] = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'

try:
    import google.generativeai as genai
    genai.configure(api_key=os.environ['GEMINI_API_KEY'])
    model = genai.GenerativeModel('gemini-2.0-flash-exp')
    print("✅ Gemini Flash 2.0 Lite initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize Gemini: {e}")
    exit(1)

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil Learning Path IDs
TAMIL_PATHS = {
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

class OptimizedTamilLessonFixer:
    """Optimized Tamil lesson fixer with conservative rate limiting"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        self.fixed_count = 0
        self.total_lessons = 0
        self.api_calls_made = 0
        self.start_time = datetime.now()
        
    def call_gemini_with_backoff(self, prompt: str, retries: int = 5) -> str:
        """Call Gemini with exponential backoff and quota handling"""
        base_delay = 10  # Start with 10 seconds between calls
        
        for attempt in range(retries):
            try:
                # Add random jitter to avoid thundering herd
                delay = base_delay + random.uniform(0, 5)
                print(f"    🕐 Waiting {delay:.1f}s before API call (attempt {attempt + 1})")
                time.sleep(delay)
                
                response = model.generate_content(prompt)
                self.api_calls_made += 1
                
                # Successful call - reduce delay for next call slightly
                base_delay = max(5, base_delay * 0.9)
                return response.text
                
            except Exception as e:
                error_msg = str(e).lower()
                
                if "quota" in error_msg or "429" in error_msg:
                    # Quota exceeded - increase delay significantly
                    base_delay = min(120, base_delay * 2)  # Cap at 2 minutes
                    print(f"    ⚠️  Quota limit hit. Increasing delay to {base_delay}s")
                    
                    if attempt < retries - 1:
                        # Wait longer for quota issues
                        quota_delay = base_delay * (attempt + 1)
                        print(f"    ⏰ Waiting {quota_delay}s for quota reset...")
                        time.sleep(quota_delay)
                        continue
                else:
                    print(f"    ⚠️  API call failed (attempt {attempt + 1}): {e}")
                    
                if attempt < retries - 1:
                    time.sleep(base_delay * (attempt + 1))
                else:
                    raise e
        
        raise Exception(f"Failed after {retries} attempts")
    
    def is_tamil_text(self, text: str) -> bool:
        """Check if text contains Tamil characters"""
        if not text:
            return False
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))
    
    def get_lessons_for_level(self, level: str) -> List[Dict[str, Any]]:
        """Get all lessons for a specific level"""
        path_id = TAMIL_PATHS[level]
        
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={
                'path_id': f'eq.{path_id}',
                'select': '*',
                'order': 'sequence_order'
            }
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to fetch {level} lessons: {response.status_code}")
            return []
    
    def update_lesson(self, lesson_id: str, updates: Dict[str, Any]) -> bool:
        """Update a lesson in Supabase"""
        response = requests.patch(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={'id': f'eq.{lesson_id}'},
            json=updates
        )
        
        return response.status_code in [200, 204]
    
    def phase1_fix_titles_descriptions(self, level: str, lessons: List[Dict[str, Any]]) -> int:
        """Phase 1: Fix Tamil titles and descriptions to English"""
        print(f"\n🔥 PHASE 1: Fixing {level} Titles/Descriptions")
        print("-" * 50)
        
        fixed_count = 0
        lessons_to_fix = []
        
        # First, identify lessons that need fixing
        for lesson in lessons:
            title = lesson.get('title', '')
            description = lesson.get('description', '')
            
            if self.is_tamil_text(title) or (description and self.is_tamil_text(description)):
                lessons_to_fix.append(lesson)
        
        print(f"📝 Found {len(lessons_to_fix)} lessons needing title/description fixes")
        
        # Process in small batches to manage quota
        batch_size = 3
        for i in range(0, len(lessons_to_fix), batch_size):
            batch = lessons_to_fix[i:i+batch_size]
            print(f"\n  📦 Processing batch {i//batch_size + 1}/{(len(lessons_to_fix) + batch_size - 1)//batch_size}")
            
            for lesson in batch:
                lesson_id = lesson['id']
                title = lesson.get('title', '')
                description = lesson.get('description', '')
                
                updates = {}
                
                # Create combined prompt for efficiency
                translation_needed = []
                if self.is_tamil_text(title):
                    translation_needed.append(f"TITLE: {title}")
                if description and self.is_tamil_text(description):
                    translation_needed.append(f"DESCRIPTION: {description}")
                
                if translation_needed:
                    print(f"    🔄 Translating lesson {lesson.get('sequence_order', '?')}")
                    
                    prompt = f"""
                    Translate these Tamil texts to proper English:
                    
                    {chr(10).join(translation_needed)}
                    
                    Requirements:
                    - For TITLE: Create a proper lesson title (e.g., "Daily Routine: From Morning to Night")
                    - For DESCRIPTION: Create a lesson description (max 200 chars)
                    - Provide ONLY the English translations
                    - Remove any Tamil text
                    - Format as:
                      TITLE: [English title]
                      DESCRIPTION: [English description]
                    """
                    
                    try:
                        response = self.call_gemini_with_backoff(prompt)
                        
                        # Parse response
                        lines = response.strip().split('\n')
                        for line in lines:
                            if line.startswith('TITLE:'):
                                new_title = line.replace('TITLE:', '').strip().strip('"\'')
                                if new_title:
                                    updates['title'] = new_title
                                    print(f"      ✅ New title: {new_title[:50]}...")
                            elif line.startswith('DESCRIPTION:'):
                                new_desc = line.replace('DESCRIPTION:', '').strip().strip('"\'')
                                if new_desc:
                                    updates['description'] = new_desc
                                    print(f"      ✅ New description: {new_desc[:50]}...")
                        
                        # Update lesson if we have changes
                        if updates:
                            if self.update_lesson(lesson_id, updates):
                                fixed_count += 1
                                print(f"      ✅ Updated lesson {lesson.get('sequence_order', '?')}")
                            else:
                                print(f"      ❌ Failed to update lesson")
                        
                    except Exception as e:
                        print(f"      ❌ Failed to translate: {e}")
            
            # Batch delay
            if i + batch_size < len(lessons_to_fix):
                print(f"    ⏰ Batch complete. Waiting before next batch...")
                time.sleep(5)
        
        print(f"📊 Phase 1 Complete: Fixed {fixed_count}/{len(lessons_to_fix)} lessons")
        return fixed_count
    
    def process_level_basic(self, level: str) -> Dict[str, int]:
        """Process basic fixes for a specific level (Phase 1 only for now)"""
        print(f"\n🚀 PROCESSING {level} LEVEL (BASIC MODE)")
        print("=" * 60)
        
        # Get all lessons for this level
        lessons = self.get_lessons_for_level(level)
        if not lessons:
            print(f"❌ No lessons found for {level}")
            return {'phase1': 0}
        
        print(f"📚 Found {len(lessons)} lessons for {level}")
        self.total_lessons += len(lessons)
        
        # Execute Phase 1 only for now
        results = {}
        results['phase1'] = self.phase1_fix_titles_descriptions(level, lessons)
        
        total_fixed = sum(results.values())
        self.fixed_count += total_fixed
        
        print(f"🎉 {level} BASIC PROCESSING COMPLETE: Fixed {total_fixed} lesson aspects")
        return results
    
    def run_basic_fix(self):
        """Run basic fix (Phase 1 only) for all levels A2-C2"""
        print("🚀 COMPREHENSIVE TAMIL LESSON FIXER - BASIC MODE")
        print("=" * 60)
        print("Using Gemini Flash 2.0 Lite with conservative rate limiting")
        print("Starting with Phase 1: Titles/Descriptions only")
        print()
        
        start_time = datetime.now()
        all_results = {}
        
        # Process each level
        for level in ['A2', 'B1', 'B2', 'C1', 'C2']:
            try:
                all_results[level] = self.process_level_basic(level)
                
                # Level completion delay
                if level != 'C2':  # Don't wait after the last level
                    print(f"\n⏰ {level} complete. Waiting 30s before next level...")
                    time.sleep(30)
                    
            except Exception as e:
                print(f"❌ Error processing {level}: {e}")
                all_results[level] = {'error': str(e)}
        
        # Generate final report
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("🎉 BASIC FIX COMPLETE!")
        print("=" * 60)
        print(f"Duration: {duration}")
        print(f"Total API Calls: {self.api_calls_made}")
        print(f"Total Lessons Processed: {self.total_lessons}")
        print(f"Total Fixes Applied: {self.fixed_count}")
        print()
        
        print("📊 RESULTS BY LEVEL:")
        for level, results in all_results.items():
            if 'error' not in results:
                total = sum(results.values())
                print(f"{level}: P1={results['phase1']} (Total: {total})")
            else:
                print(f"{level}: ERROR - {results['error']}")
        
        print("\n✅ Phase 1 (Titles/Descriptions) complete for all levels A2-C2!")
        print("🔄 Next: Run phases 2-4 with renewed quota")

def main():
    """Main execution function"""
    fixer = OptimizedTamilLessonFixer()
    fixer.run_basic_fix()

if __name__ == "__main__":
    main() 