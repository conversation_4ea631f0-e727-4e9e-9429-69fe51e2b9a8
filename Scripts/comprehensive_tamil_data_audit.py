#!/usr/bin/env python3
"""
🔍 COMPREHENSIVE TAMIL DATA AUDIT
Audits all Tamil lessons A1-C2 to identify issues and provide a complete report.

This audit checks:
1. Lesson titles and descriptions (should be in English)
2. Content completeness and quality
3. Placeholder vs. real content
4. Pronunciation data
5. Audio URL structure
6. Cultural authenticity
7. Data consistency across levels

Based on: COMPREHENSIVE_QUALITY_CHECKLIST.md
"""

import json
import requests
import re
from datetime import datetime
from typing import Dict, List, Any, Tuple

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil Learning Path IDs from documents
TAMIL_PATHS = {
    'A1': '6b427613-420f-4586-bce8-2773d722f0b4',
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

# Expected content counts based on documentation
EXPECTED_CONTENT = {
    'A1': {'vocabulary': 25, 'conversations': 15, 'grammar': 10, 'exercises': 24},
    'A2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'B1': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'B2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'C1': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'C2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15}
}

class TamilDataAuditor:
    """Comprehensive auditor for Tamil lesson data"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        self.audit_results = {}
        self.summary_report = {}
        
    def is_tamil_text(self, text: str) -> bool:
        """Check if text contains Tamil characters"""
        if not text:
            return False
        # Tamil Unicode range: U+0B80–U+0BFF
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))
    
    def detect_placeholder_content(self, content: Any) -> List[str]:
        """Detect placeholder or generic content"""
        placeholder_indicators = [
            'placeholder', 'example', 'sample', 'generic', 'template',
            'lorem ipsum', 'test content', 'dummy', 'placeholder text',
            '[word]', '[translation]', '[example]', 'basic word',
            'simple conversation', 'general topic', 'standard exercise'
        ]
        
        issues = []
        content_str = str(content).lower()
        
        for indicator in placeholder_indicators:
            if indicator in content_str:
                issues.append(f"Contains '{indicator}'")
        
        return issues
    
    def audit_lesson_title_description(self, lesson: Dict[str, Any], level: str) -> Dict[str, Any]:
        """Audit lesson title and description"""
        issues = []
        
        title = lesson.get('title', '')
        description = lesson.get('description', '')
        
        # Check if title is in Tamil (should be English)
        if self.is_tamil_text(title):
            issues.append("Title is in Tamil (should be English)")
        
        # Check if description is in Tamil (should be English)
        if self.is_tamil_text(description):
            issues.append("Description is in Tamil (should be English)")
        
        # Check for empty fields
        if not title.strip():
            issues.append("Empty title")
        if not description or not description.strip():
            issues.append("Empty description")
        
        return {
            'title': title,
            'description': description,
            'issues': issues,
            'title_language': 'Tamil' if self.is_tamil_text(title) else 'English',
            'description_language': 'Tamil' if self.is_tamil_text(description) else 'English'
        }
    
    def audit_content_completeness(self, content_metadata: Dict[str, Any], level: str) -> Dict[str, Any]:
        """Audit content completeness against expected counts"""
        expected = EXPECTED_CONTENT[level]
        actual = {}
        issues = []
        
        # Check vocabulary
        vocabulary = content_metadata.get('vocabulary', [])
        actual['vocabulary'] = len(vocabulary)
        if actual['vocabulary'] != expected['vocabulary']:
            issues.append(f"Vocabulary: {actual['vocabulary']}/{expected['vocabulary']}")
        
        # Check conversations
        conversations = content_metadata.get('conversations', [])
        actual['conversations'] = len(conversations)
        if actual['conversations'] != expected['conversations']:
            issues.append(f"Conversations: {actual['conversations']}/{expected['conversations']}")
        
        # Check grammar
        grammar = content_metadata.get('grammar_points', [])
        actual['grammar'] = len(grammar)
        if actual['grammar'] != expected['grammar']:
            issues.append(f"Grammar: {actual['grammar']}/{expected['grammar']}")
        
        # Check exercises
        exercises = content_metadata.get('exercises', [])
        actual['exercises'] = len(exercises)
        if actual['exercises'] != expected['exercises']:
            issues.append(f"Exercises: {actual['exercises']}/{expected['exercises']}")
        
        return {
            'expected': expected,
            'actual': actual,
            'issues': issues,
            'completeness_score': len([k for k in expected.keys() if actual.get(k) == expected[k]]) / len(expected)
        }
    
    def audit_pronunciation_data(self, content_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Audit pronunciation data quality"""
        issues = []
        
        # Check conversations for pronunciations
        conversations = content_metadata.get('conversations', [])
        conv_pronunciation_missing = 0
        for i, conv in enumerate(conversations):
            if 'exchanges' in conv:
                for j, exchange in enumerate(conv['exchanges']):
                    if not exchange.get('pronunciation'):
                        conv_pronunciation_missing += 1
        
        if conv_pronunciation_missing > 0:
            issues.append(f"{conv_pronunciation_missing} conversation exchanges missing pronunciations")
        
        # Check exercises for option pronunciations
        exercises = content_metadata.get('exercises', [])
        exercise_pronunciation_missing = 0
        for exercise in exercises:
            options = exercise.get('options', [])
            pronunciations = exercise.get('options_pronunciations', [])
            if options and len(pronunciations) != len(options):
                exercise_pronunciation_missing += 1
        
        if exercise_pronunciation_missing > 0:
            issues.append(f"{exercise_pronunciation_missing} exercises missing option pronunciations")
        
        # Check vocabulary for pronunciations
        vocabulary = content_metadata.get('vocabulary', [])
        vocab_pronunciation_missing = 0
        for vocab in vocabulary:
            if not vocab.get('pronunciation'):
                vocab_pronunciation_missing += 1
        
        if vocab_pronunciation_missing > 0:
            issues.append(f"{vocab_pronunciation_missing} vocabulary items missing pronunciations")
        
        return {
            'conversation_issues': conv_pronunciation_missing,
            'exercise_issues': exercise_pronunciation_missing,
            'vocabulary_issues': vocab_pronunciation_missing,
            'total_issues': conv_pronunciation_missing + exercise_pronunciation_missing + vocab_pronunciation_missing,
            'issues': issues
        }
    
    def audit_content_quality(self, content_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Audit content quality for placeholders and authenticity"""
        issues = []
        placeholder_count = 0
        
        # Check vocabulary for placeholder content
        vocabulary = content_metadata.get('vocabulary', [])
        for vocab in vocabulary:
            word_issues = self.detect_placeholder_content(vocab.get('word', ''))
            translation_issues = self.detect_placeholder_content(vocab.get('translation', ''))
            example_issues = self.detect_placeholder_content(vocab.get('example', ''))
            
            if word_issues or translation_issues or example_issues:
                placeholder_count += 1
        
        # Check conversations for placeholder content
        conversations = content_metadata.get('conversations', [])
        for conv in conversations:
            title_issues = self.detect_placeholder_content(conv.get('title', ''))
            if title_issues:
                placeholder_count += 1
            
            if 'exchanges' in conv:
                for exchange in conv['exchanges']:
                    text_issues = self.detect_placeholder_content(exchange.get('text', ''))
                    if text_issues:
                        placeholder_count += 1
        
        # Check grammar for placeholder content
        grammar = content_metadata.get('grammar_points', [])
        for gram in grammar:
            rule_issues = self.detect_placeholder_content(gram.get('rule', ''))
            explanation_issues = self.detect_placeholder_content(gram.get('explanation', ''))
            if rule_issues or explanation_issues:
                placeholder_count += 1
        
        # Check exercises for placeholder content
        exercises = content_metadata.get('exercises', [])
        for exercise in exercises:
            question_issues = self.detect_placeholder_content(exercise.get('question', ''))
            if question_issues:
                placeholder_count += 1
        
        if placeholder_count > 0:
            issues.append(f"{placeholder_count} items contain placeholder content")
        
        return {
            'placeholder_count': placeholder_count,
            'issues': issues,
            'quality_score': max(0, 1 - (placeholder_count / 50))  # Rough quality score
        }
    
    def audit_audio_structure(self, content_metadata: Dict[str, Any], lesson: Dict[str, Any]) -> Dict[str, Any]:
        """Audit audio URL structure and completeness"""
        issues = []
        
        # Check has_audio flag
        has_audio = lesson.get('has_audio', False)
        if not has_audio:
            issues.append("has_audio flag is False")
        
        # Check audio_url field
        audio_url = lesson.get('audio_url')
        if not audio_url:
            issues.append("Missing main audio_url")
        
        # Check audio_metadata
        audio_metadata = lesson.get('audio_metadata', {})
        if not audio_metadata:
            issues.append("Missing audio_metadata")
        else:
            generated_count = audio_metadata.get('generated_audio_count', 0)
            if generated_count == 0:
                issues.append("No generated audio files")
        
        # Check for audio URLs in content
        audio_urls = content_metadata.get('audio_urls', {})
        if not audio_urls:
            issues.append("No audio URLs in content_metadata")
        
        return {
            'has_audio': has_audio,
            'audio_url_present': bool(audio_url),
            'audio_metadata_present': bool(audio_metadata),
            'content_audio_urls': len(audio_urls),
            'issues': issues
        }
    
    def audit_single_lesson(self, lesson: Dict[str, Any], level: str) -> Dict[str, Any]:
        """Audit a single lesson comprehensively"""
        
        lesson_audit = {
            'id': lesson.get('id'),
            'sequence_order': lesson.get('sequence_order'),
            'level': level
        }
        
        # Audit title and description
        lesson_audit['title_description'] = self.audit_lesson_title_description(lesson, level)
        
        # Get content metadata
        content_metadata = lesson.get('content_metadata', {})
        
        if not content_metadata:
            lesson_audit['fatal_error'] = "No content_metadata found"
            return lesson_audit
        
        # Audit content completeness
        lesson_audit['content_completeness'] = self.audit_content_completeness(content_metadata, level)
        
        # Audit pronunciation data
        lesson_audit['pronunciation_data'] = self.audit_pronunciation_data(content_metadata)
        
        # Audit content quality
        lesson_audit['content_quality'] = self.audit_content_quality(content_metadata)
        
        # Audit audio structure
        lesson_audit['audio_structure'] = self.audit_audio_structure(content_metadata, lesson)
        
        # Calculate overall lesson score
        scores = []
        if 'completeness_score' in lesson_audit['content_completeness']:
            scores.append(lesson_audit['content_completeness']['completeness_score'])
        if 'quality_score' in lesson_audit['content_quality']:
            scores.append(lesson_audit['content_quality']['quality_score'])
        
        lesson_audit['overall_score'] = sum(scores) / len(scores) if scores else 0
        
        # Determine if lesson passes quality check
        title_issues = len(lesson_audit['title_description']['issues'])
        content_issues = len(lesson_audit['content_completeness']['issues'])
        pronunciation_issues = lesson_audit['pronunciation_data']['total_issues']
        quality_issues = lesson_audit['content_quality']['placeholder_count']
        
        lesson_audit['passes_quality_check'] = (
            title_issues == 0 and 
            content_issues == 0 and 
            pronunciation_issues == 0 and 
            quality_issues == 0
        )
        
        return lesson_audit
    
    def audit_level(self, level: str) -> Dict[str, Any]:
        """Audit all lessons for a specific level"""
        
        print(f"\n🔍 AUDITING {level} LEVEL")
        print("=" * 50)
        
        path_id = TAMIL_PATHS[level]
        
        # Fetch all lessons for this level
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={
                'path_id': f'eq.{path_id}',
                'select': '*',
                'order': 'sequence_order'
            }
        )
        
        if response.status_code != 200:
            return {'error': f"Failed to fetch {level} lessons: {response.status_code}"}
        
        lessons = response.json()
        
        level_audit = {
            'level': level,
            'path_id': path_id,
            'total_lessons': len(lessons),
            'lessons': [],
            'summary': {
                'lessons_with_tamil_titles': 0,
                'lessons_with_tamil_descriptions': 0,
                'lessons_with_incomplete_content': 0,
                'lessons_with_placeholder_content': 0,
                'lessons_with_missing_pronunciations': 0,
                'lessons_with_audio_issues': 0,
                'lessons_passing_quality_check': 0
            }
        }
        
        for lesson in lessons:
            print(f"  Auditing lesson {lesson.get('sequence_order', '?')}: {lesson.get('title', 'Untitled')[:50]}...")
            
            lesson_audit = self.audit_single_lesson(lesson, level)
            level_audit['lessons'].append(lesson_audit)
            
            # Update summary counters
            if lesson_audit['title_description']['title_language'] == 'Tamil':
                level_audit['summary']['lessons_with_tamil_titles'] += 1
            
            if lesson_audit['title_description']['description_language'] == 'Tamil':
                level_audit['summary']['lessons_with_tamil_descriptions'] += 1
            
            if lesson_audit['content_completeness']['issues']:
                level_audit['summary']['lessons_with_incomplete_content'] += 1
            
            if lesson_audit['content_quality']['placeholder_count'] > 0:
                level_audit['summary']['lessons_with_placeholder_content'] += 1
            
            if lesson_audit['pronunciation_data']['total_issues'] > 0:
                level_audit['summary']['lessons_with_missing_pronunciations'] += 1
            
            if lesson_audit['audio_structure']['issues']:
                level_audit['summary']['lessons_with_audio_issues'] += 1
            
            if lesson_audit['passes_quality_check']:
                level_audit['summary']['lessons_passing_quality_check'] += 1
        
        # Calculate level quality score
        total_lessons = level_audit['total_lessons']
        if total_lessons > 0:
            level_audit['quality_percentage'] = (
                level_audit['summary']['lessons_passing_quality_check'] / total_lessons * 100
            )
        else:
            level_audit['quality_percentage'] = 0
        
        print(f"📊 {level} AUDIT COMPLETE: {level_audit['summary']['lessons_passing_quality_check']}/{total_lessons} lessons pass quality check ({level_audit['quality_percentage']:.1f}%)")
        
        return level_audit
    
    def generate_comprehensive_report(self) -> str:
        """Generate comprehensive audit report"""
        
        report = []
        report.append("🔍 COMPREHENSIVE TAMIL DATA AUDIT REPORT")
        report.append("=" * 60)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Based on: COMPREHENSIVE_QUALITY_CHECKLIST.md")
        report.append("")
        
        # Executive Summary
        report.append("📊 EXECUTIVE SUMMARY")
        report.append("-" * 30)
        
        total_lessons = 0
        total_passing = 0
        
        for level, audit in self.audit_results.items():
            if 'error' not in audit:
                total_lessons += audit['total_lessons']
                total_passing += audit['summary']['lessons_passing_quality_check']
        
        overall_percentage = (total_passing / total_lessons * 100) if total_lessons > 0 else 0
        
        report.append(f"Total Lessons: {total_lessons}")
        report.append(f"Lessons Passing Quality Check: {total_passing}")
        report.append(f"Overall Quality Score: {overall_percentage:.1f}%")
        report.append("")
        
        # Level-by-level breakdown
        report.append("📚 LEVEL-BY-LEVEL BREAKDOWN")
        report.append("-" * 35)
        
        for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
            if level in self.audit_results:
                audit = self.audit_results[level]
                if 'error' in audit:
                    report.append(f"{level}: ERROR - {audit['error']}")
                else:
                    summary = audit['summary']
                    report.append(f"{level}: {summary['lessons_passing_quality_check']}/{audit['total_lessons']} lessons pass ({audit['quality_percentage']:.1f}%)")
                    
                    # Key issues
                    issues = []
                    if summary['lessons_with_tamil_titles'] > 0:
                        issues.append(f"{summary['lessons_with_tamil_titles']} Tamil titles")
                    if summary['lessons_with_tamil_descriptions'] > 0:
                        issues.append(f"{summary['lessons_with_tamil_descriptions']} Tamil descriptions")
                    if summary['lessons_with_incomplete_content'] > 0:
                        issues.append(f"{summary['lessons_with_incomplete_content']} incomplete content")
                    if summary['lessons_with_placeholder_content'] > 0:
                        issues.append(f"{summary['lessons_with_placeholder_content']} placeholder content")
                    
                    if issues:
                        report.append(f"     ⚠️  Issues: {', '.join(issues)}")
                    else:
                        report.append(f"     ✅ No major issues")
        
        report.append("")
        
        # Critical Issues
        report.append("🚨 CRITICAL ISSUES REQUIRING IMMEDIATE ATTENTION")
        report.append("-" * 55)
        
        critical_issues = []
        
        for level, audit in self.audit_results.items():
            if 'error' not in audit:
                summary = audit['summary']
                
                # Tamil titles/descriptions (critical for A2-C2)
                if level != 'A1' and summary['lessons_with_tamil_titles'] > 0:
                    critical_issues.append(f"{level}: {summary['lessons_with_tamil_titles']} lessons have Tamil titles (should be English)")
                
                if level != 'A1' and summary['lessons_with_tamil_descriptions'] > 0:
                    critical_issues.append(f"{level}: {summary['lessons_with_tamil_descriptions']} lessons have Tamil descriptions (should be English)")
                
                # Placeholder content
                if summary['lessons_with_placeholder_content'] > 0:
                    critical_issues.append(f"{level}: {summary['lessons_with_placeholder_content']} lessons contain placeholder content")
                
                # Missing pronunciations
                if summary['lessons_with_missing_pronunciations'] > 0:
                    critical_issues.append(f"{level}: {summary['lessons_with_missing_pronunciations']} lessons missing pronunciations")
        
        if critical_issues:
            for issue in critical_issues:
                report.append(f"❌ {issue}")
        else:
            report.append("✅ No critical issues found")
        
        report.append("")
        
        # Recommendations
        report.append("💡 RECOMMENDATIONS FOR FIXES")
        report.append("-" * 35)
        
        # Priority 1: Fix Tamil titles/descriptions
        tamil_title_levels = []
        for level, audit in self.audit_results.items():
            if 'error' not in audit and audit['summary']['lessons_with_tamil_titles'] > 0:
                tamil_title_levels.append(level)
        
        if tamil_title_levels:
            report.append("🔥 PRIORITY 1: Fix Tamil Titles/Descriptions")
            report.append(f"   Levels affected: {', '.join(tamil_title_levels)}")
            report.append("   Solution: Use Gemini Flash 2.0 Lite to translate Tamil titles/descriptions to English")
            report.append("   Cost: Low (translation only)")
            report.append("")
        
        # Priority 2: Replace placeholder content
        placeholder_levels = []
        for level, audit in self.audit_results.items():
            if 'error' not in audit and audit['summary']['lessons_with_placeholder_content'] > 0:
                placeholder_levels.append(level)
        
        if placeholder_levels:
            report.append("🔥 PRIORITY 2: Replace Placeholder Content")
            report.append(f"   Levels affected: {', '.join(placeholder_levels)}")
            report.append("   Solution: Use GPT-4 Turbo + Gemini Flash 2.0 Lite to generate authentic Tamil content")
            report.append("   Cost: Medium (content generation)")
            report.append("")
        
        # Priority 3: Add missing pronunciations
        pronunciation_levels = []
        for level, audit in self.audit_results.items():
            if 'error' not in audit and audit['summary']['lessons_with_missing_pronunciations'] > 0:
                pronunciation_levels.append(level)
        
        if pronunciation_levels:
            report.append("🔥 PRIORITY 3: Add Missing Pronunciations")
            report.append(f"   Levels affected: {', '.join(pronunciation_levels)}")
            report.append("   Solution: Use Tamil romanization algorithms + manual review")
            report.append("   Cost: Low (romanization)")
            report.append("")
        
        # Cost-effective implementation plan
        report.append("💰 COST-EFFECTIVE IMPLEMENTATION PLAN")
        report.append("-" * 45)
        report.append("1. Batch translate Tamil titles/descriptions (Gemini Flash 2.0 Lite - $0.075/1M tokens)")
        report.append("2. Generate authentic content in batches (GPT-4 Turbo + Gemini Flash - $10-30/1M tokens)")
        report.append("3. Add pronunciations using existing romanization scripts")
        report.append("4. Validate and test in batches of 10 lessons")
        report.append("")
        
        report.append("🎯 SUCCESS CRITERIA")
        report.append("-" * 20)
        report.append("✅ All lesson titles and descriptions in English")
        report.append("✅ No placeholder content remaining")
        report.append("✅ All content has proper Tamil pronunciations")
        report.append("✅ All lessons pass comprehensive quality checklist")
        report.append("✅ 95%+ lessons achieve quality score of 0.8+")
        
        return "\n".join(report)
    
    def run_full_audit(self) -> str:
        """Run complete audit of all Tamil levels"""
        
        print("🚀 STARTING COMPREHENSIVE TAMIL DATA AUDIT")
        print("=" * 60)
        
        # Audit each level
        for level in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
            try:
                self.audit_results[level] = self.audit_level(level)
            except Exception as e:
                print(f"❌ Error auditing {level}: {e}")
                self.audit_results[level] = {'error': str(e)}
        
        # Generate comprehensive report
        report = self.generate_comprehensive_report()
        
        # Save report to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"tamil_data_audit_report_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 AUDIT COMPLETE!")
        print(f"Report saved to: {filename}")
        print("\n" + "="*60)
        
        return report

def main():
    """Main execution function"""
    auditor = TamilDataAuditor()
    report = auditor.run_full_audit()
    print(report)

if __name__ == "__main__":
    main() 