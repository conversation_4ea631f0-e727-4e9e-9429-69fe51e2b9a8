#!/usr/bin/env python3
"""
Database Consolidation Fix - Merge Duplicate Paths Without Data Loss

This script:
1. Identifies duplicate learning paths for same language-level combinations
2. Consolidates lessons from duplicate paths into a single canonical path
3. Updates path references and maintains data integrity
4. Provides comprehensive reporting on consolidation actions
5. Does NOT delete any existing content
"""

import requests
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import time

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    "apikey": SUPABASE_ANON_KEY,
    "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
    "Content-Type": "application/json"
}

# Tier 1 Languages
TIER1_LANGUAGES = {
    'arabic': 'Arabic', 'bengali': 'Bengali', 'chinese': 'Chinese', 'dutch': 'Dutch',
    'english': 'English', 'french': 'French', 'german': 'German', 'gujarati': 'Gujarati',
    'hindi': 'Hindi', 'italian': 'Italian', 'japanese': 'Japanese', 'kannada': 'Kannada',
    'korean': 'Korean', 'malayalam': 'Malayalam', 'marathi': 'Marathi', 'portuguese': 'Portuguese',
    'punjabi': 'Punjabi', 'russian': 'Russian', 'spanish': 'Spanish', 'tamil': 'Tamil',
    'telugu': 'Telugu'
}

LEVELS = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']

class DatabaseConsolidator:
    """Database consolidation system without data loss"""
    
    def __init__(self):
        self.headers = headers
        self.consolidation_report = {
            'total_paths_before': 0,
            'total_paths_after': 0,
            'duplicate_groups_found': 0,
            'lessons_moved': 0,
            'paths_deactivated': 0,
            'languages_affected': set(),
            'detailed_actions': []
        }
    
    def log(self, message: str):
        """Log with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def get_all_learning_paths(self) -> List[Dict[str, Any]]:
        """Get all learning paths from database"""
        self.log("🔍 Fetching all learning paths...")
        
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/learning_paths",
            headers=self.headers,
            params={'select': 'id,name,level,language_id,is_active', 'limit': '1000'}
        )
        
        if response.status_code == 200:
            paths = response.json()
            self.log(f"✅ Found {len(paths)} total learning paths")
            self.consolidation_report['total_paths_before'] = len(paths)
            return paths
        else:
            self.log(f"❌ Failed to fetch learning paths: {response.status_code}")
            return []
    
    def get_lessons_for_path(self, path_id: str) -> List[Dict[str, Any]]:
        """Get all lessons for a specific path"""
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={
                'path_id': f'eq.{path_id}',
                'select': '*',
                'order': 'sequence_order'
            }
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            self.log(f"❌ Failed to fetch lessons for path {path_id}: {response.status_code}")
            return []
    
    def identify_duplicate_groups(self, paths: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Identify groups of duplicate paths for same language-level combinations"""
        self.log("🔍 Identifying duplicate path groups...")
        
        # Group paths by language and level signature
        path_groups = defaultdict(list)
        
        for path in paths:
            if not path.get('is_active', True):
                continue  # Skip inactive paths
                
            name = path.get('name', '').lower()
            level = path.get('level', '').upper()
            
            # Extract language and level from path name
            language_detected = None
            level_detected = None
            
            # Detect language
            for lang_key, lang_name in TIER1_LANGUAGES.items():
                if lang_name.lower() in name or lang_key in name:
                    language_detected = lang_key
                    break
            
            # Detect level - prioritize explicit level field, then name parsing
            if level and level in LEVELS:
                level_detected = level
            else:
                # Parse from name
                for lvl in LEVELS:
                    if lvl.lower() in name:
                        level_detected = lvl
                        break
                
                # Check for complete course indicators
                if 'complete' in name or 'a1-c2' in name:
                    level_detected = 'COMPLETE'
            
            if language_detected:
                # Create signature for grouping
                signature = f"{language_detected}_{level_detected}" if level_detected else f"{language_detected}_UNKNOWN"
                path_groups[signature].append(path)
        
        # Filter to only groups with duplicates
        duplicate_groups = {sig: paths for sig, paths in path_groups.items() if len(paths) > 1}
        
        self.log(f"✅ Found {len(duplicate_groups)} duplicate groups:")
        for signature, paths in duplicate_groups.items():
            lang, level = signature.split('_', 1)
            self.log(f"   📁 {TIER1_LANGUAGES.get(lang, lang)} {level}: {len(paths)} paths")
            for path in paths:
                lesson_count = len(self.get_lessons_for_path(path['id']))
                self.log(f"      - {path['name']} ({lesson_count} lessons)")
        
        self.consolidation_report['duplicate_groups_found'] = len(duplicate_groups)
        return duplicate_groups
    
    def choose_canonical_path(self, duplicate_paths: List[Dict[str, Any]]) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Choose which path to keep as canonical and which to merge"""
        self.log(f"🎯 Choosing canonical path from {len(duplicate_paths)} candidates...")
        
        # Score each path based on various factors
        path_scores = []
        
        for path in duplicate_paths:
            score = 0
            lessons = self.get_lessons_for_path(path['id'])
            
            # Factor 1: Number of lessons (higher is better)
            score += len(lessons) * 10
            
            # Factor 2: Content quality (lessons with complete content)
            complete_lessons = 0
            for lesson in lessons:
                content = lesson.get('content_metadata', {})
                if (content.get('vocabulary') and 
                    content.get('conversations') and 
                    content.get('grammar_points') and 
                    content.get('exercises')):
                    complete_lessons += 1
            score += complete_lessons * 5
            
            # Factor 3: Path name quality (prefer "Complete Course" or clear naming)
            name = path.get('name', '').lower()
            if 'complete' in name:
                score += 20
            if 'course' in name:
                score += 10
            
            # Factor 4: Recent activity (newer paths might be better organized)
            # This is a simplified check - could be enhanced with actual timestamps
            if path.get('id'):  # All paths have IDs, but this is placeholder for timestamp logic
                score += 1
            
            path_scores.append((score, path, lessons))
            
            self.log(f"   📊 {path['name']}: Score {score} ({len(lessons)} lessons, {complete_lessons} complete)")
        
        # Sort by score (highest first)
        path_scores.sort(key=lambda x: x[0], reverse=True)
        
        canonical_path = path_scores[0][1]
        other_paths = [item[1] for item in path_scores[1:]]
        
        self.log(f"✅ Canonical path chosen: {canonical_path['name']} (Score: {path_scores[0][0]})")
        
        return canonical_path, other_paths
    
    def merge_lessons_to_canonical(self, canonical_path: Dict[str, Any], other_paths: List[Dict[str, Any]]) -> int:
        """Merge lessons from other paths to canonical path"""
        lessons_moved = 0
        canonical_id = canonical_path['id']
        
        self.log(f"🔄 Merging lessons to canonical path: {canonical_path['name']}")
        
        # Get existing lessons in canonical path to avoid duplicates
        canonical_lessons = self.get_lessons_for_path(canonical_id)
        existing_titles = {lesson['title'].lower().strip() for lesson in canonical_lessons}
        
        self.log(f"   📚 Canonical path has {len(canonical_lessons)} existing lessons")
        
        for other_path in other_paths:
            other_id = other_path['id']
            other_lessons = self.get_lessons_for_path(other_id)
            
            self.log(f"   🔍 Processing {other_path['name']} ({len(other_lessons)} lessons)")
            
            for lesson in other_lessons:
                lesson_title = lesson['title'].lower().strip()
                
                # Check for duplicates by title
                if lesson_title in existing_titles:
                    self.log(f"      ⚠️ Skipping duplicate: {lesson['title']}")
                    continue
                
                # Reassign lesson to canonical path
                try:
                    # Find next available sequence order
                    max_sequence = max([l.get('sequence_order', 0) for l in canonical_lessons] + [0])
                    new_sequence = max_sequence + 1
                    
                    update_data = {
                        'path_id': canonical_id,
                        'sequence_order': new_sequence,
                        'updated_at': datetime.now().isoformat()
                    }
                    
                    response = requests.patch(
                        f"{SUPABASE_URL}/rest/v1/lessons",
                        headers=self.headers,
                        params={'id': f'eq.{lesson["id"]}'},
                        json=update_data
                    )
                    
                    if response.status_code in [200, 204]:
                        self.log(f"      ✅ Moved: {lesson['title']} (seq: {new_sequence})")
                        lessons_moved += 1
                        existing_titles.add(lesson_title)
                        canonical_lessons.append({**lesson, **update_data})
                    else:
                        self.log(f"      ❌ Failed to move: {lesson['title']} (HTTP {response.status_code})")
                
                except Exception as e:
                    self.log(f"      ❌ Error moving lesson {lesson['title']}: {e}")
                
                # Brief pause to avoid overwhelming database
                time.sleep(0.1)
        
        self.log(f"✅ Moved {lessons_moved} lessons to canonical path")
        return lessons_moved
    
    def deactivate_empty_paths(self, other_paths: List[Dict[str, Any]]) -> int:
        """Deactivate paths that have been emptied of lessons"""
        deactivated = 0
        
        self.log("🔒 Deactivating empty duplicate paths...")
        
        for path in other_paths:
            # Check if path is now empty
            remaining_lessons = self.get_lessons_for_path(path['id'])
            
            if len(remaining_lessons) == 0:
                try:
                    update_data = {
                        'is_active': False,
                        'updated_at': datetime.now().isoformat()
                    }
                    
                    response = requests.patch(
                        f"{SUPABASE_URL}/rest/v1/learning_paths",
                        headers=self.headers,
                        params={'id': f'eq.{path["id"]}'},
                        json=update_data
                    )
                    
                    if response.status_code in [200, 204]:
                        self.log(f"   ✅ Deactivated: {path['name']}")
                        deactivated += 1
                    else:
                        self.log(f"   ❌ Failed to deactivate: {path['name']} (HTTP {response.status_code})")
                
                except Exception as e:
                    self.log(f"   ❌ Error deactivating {path['name']}: {e}")
            else:
                self.log(f"   ⚠️ Keeping active (has {len(remaining_lessons)} lessons): {path['name']}")
        
        return deactivated
    
    def consolidate_duplicate_group(self, signature: str, duplicate_paths: List[Dict[str, Any]]):
        """Consolidate a single group of duplicate paths"""
        lang, level = signature.split('_', 1)
        language_name = TIER1_LANGUAGES.get(lang, lang)
        
        self.log(f"\n🔧 CONSOLIDATING: {language_name} {level}")
        self.log("=" * 60)
        
        # Choose canonical path
        canonical_path, other_paths = self.choose_canonical_path(duplicate_paths)
        
        # Merge lessons
        lessons_moved = self.merge_lessons_to_canonical(canonical_path, other_paths)
        
        # Deactivate empty paths
        paths_deactivated = self.deactivate_empty_paths(other_paths)
        
        # Update report
        self.consolidation_report['lessons_moved'] += lessons_moved
        self.consolidation_report['paths_deactivated'] += paths_deactivated
        self.consolidation_report['languages_affected'].add(language_name)
        
        self.consolidation_report['detailed_actions'].append({
            'language': language_name,
            'level': level,
            'canonical_path': canonical_path['name'],
            'canonical_id': canonical_path['id'],
            'merged_paths': [p['name'] for p in other_paths],
            'lessons_moved': lessons_moved,
            'paths_deactivated': paths_deactivated
        })
        
        self.log(f"✅ Consolidation complete: {lessons_moved} lessons moved, {paths_deactivated} paths deactivated")
    
    def run_consolidation(self):
        """Run complete database consolidation"""
        self.log("🚀 DATABASE CONSOLIDATION STARTED")
        self.log("=" * 80)
        self.log("GOAL: Merge duplicate paths without deleting any content")
        print()
        
        start_time = datetime.now()
        
        # Step 1: Get all learning paths
        all_paths = self.get_all_learning_paths()
        if not all_paths:
            self.log("❌ No paths found - aborting")
            return
        
        # Step 2: Identify duplicate groups
        duplicate_groups = self.identify_duplicate_groups(all_paths)
        
        if not duplicate_groups:
            self.log("✅ No duplicate groups found - database is already consolidated!")
            return
        
        # Step 3: Process each duplicate group
        self.log(f"\n🔧 PROCESSING {len(duplicate_groups)} DUPLICATE GROUPS")
        self.log("=" * 80)
        
        for signature, duplicate_paths in duplicate_groups.items():
            try:
                self.consolidate_duplicate_group(signature, duplicate_paths)
                time.sleep(2)  # Pause between groups
            except Exception as e:
                self.log(f"❌ Error consolidating {signature}: {e}")
        
        # Step 4: Final report
        end_time = datetime.now()
        duration = end_time - start_time
        
        self.generate_final_report(duration)
    
    def generate_final_report(self, duration):
        """Generate comprehensive consolidation report"""
        self.log("\n" + "=" * 80)
        self.log("🎉 DATABASE CONSOLIDATION COMPLETE!")
        self.log("=" * 80)
        
        report = self.consolidation_report
        
        self.log(f"⏰ Duration: {duration}")
        self.log(f"📁 Duplicate Groups Found: {report['duplicate_groups_found']}")
        self.log(f"📚 Total Lessons Moved: {report['lessons_moved']}")
        self.log(f"🔒 Paths Deactivated: {report['paths_deactivated']}")
        self.log(f"🌍 Languages Affected: {len(report['languages_affected'])}")
        
        if report['languages_affected']:
            self.log(f"   Languages: {', '.join(sorted(report['languages_affected']))}")
        
        self.log(f"\n📊 DETAILED ACTIONS:")
        self.log("-" * 80)
        
        for action in report['detailed_actions']:
            self.log(f"🌍 {action['language']} {action['level']}:")
            self.log(f"   ✅ Canonical: {action['canonical_path']}")
            self.log(f"   📚 Lessons Moved: {action['lessons_moved']}")
            self.log(f"   🔒 Paths Deactivated: {action['paths_deactivated']}")
            if action['merged_paths']:
                self.log(f"   📁 Merged Paths: {', '.join(action['merged_paths'])}")
        
        self.log(f"\n✅ RESULT: Database consolidated without data loss!")
        self.log(f"✅ All content preserved and properly organized")
        
        # Save detailed report to file
        report_filename = f"consolidation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            # Convert set to list for JSON serialization
            report_copy = report.copy()
            report_copy['languages_affected'] = list(report_copy['languages_affected'])
            report_copy['duration'] = str(duration)
            report_copy['timestamp'] = datetime.now().isoformat()
            
            json.dump(report_copy, f, indent=2)
        
        self.log(f"💾 Detailed report saved: {report_filename}")

if __name__ == "__main__":
    consolidator = DatabaseConsolidator()
    consolidator.run_consolidation() 