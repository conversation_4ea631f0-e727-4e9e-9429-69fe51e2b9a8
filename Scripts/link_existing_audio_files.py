#!/usr/bin/env python3
"""
Link Existing Audio Files
Finds existing audio files in Supabase storage and links them to lessons
Follows the documented structure: language/level/lesson_slug/filename
"""

import requests
import json

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

def test_audio_file_exists(url):
    """Test if an audio file exists at the given URL"""
    try:
        response = requests.head(url, timeout=10)
        return response.status_code == 200
    except:
        return False

def find_existing_audio_files(language, level, lesson_slug):
    """Find existing audio files for a lesson"""
    
    base_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{language}/{level}/{lesson_slug}"
    
    # Common file patterns to check
    file_patterns = [
        # Vocabulary files
        "vocab_01_word.mp3",
        "vocab_01_example.mp3", 
        "vocab_01_sentence.mp3",
        "vocab_01_pronunciation.mp3",
        "vocab_02_word.mp3",
        "vocab_02_example.mp3",
        "vocab_02_sentence.mp3",
        "vocab_03_word.mp3",
        "vocab_03_example.mp3",
        "vocab_03_sentence.mp3",
        "vocab_04_word.mp3",
        "vocab_04_example.mp3",
        "vocab_05_word.mp3",
        "vocab_05_example.mp3",
        
        # Conversation files
        "conv_01_speaker_a.mp3",
        "conv_01_speaker_b.mp3",
        "conv_01_full.mp3",
        "conv_02_speaker_a.mp3",
        "conv_02_speaker_b.mp3",
        
        # Grammar files
        "grammar_01_explanation.mp3",
        "grammar_01_example.mp3",
        "grammar_02_explanation.mp3",
        "grammar_02_example.mp3",
        
        # Exercise files
        "exercise_01_question.mp3",
        "exercise_01_option_a.mp3",
        "exercise_01_option_b.mp3",
        "exercise_01_option_c.mp3",
        "exercise_01_option_d.mp3"
    ]
    
    existing_files = {}
    
    print(f"🔍 Checking for existing audio files in: {base_url}")
    
    for filename in file_patterns:
        url = f"{base_url}/{filename}"
        if test_audio_file_exists(url):
            # Create proper key name (remove .mp3 extension)
            key = filename.replace('.mp3', '')
            existing_files[key] = url
            print(f"✅ Found: {filename}")
        else:
            print(f"❌ Missing: {filename}")
    
    return existing_files

def update_lesson_audio_urls(lesson_id, audio_urls):
    """Update lesson with found audio URLs"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    if not audio_urls:
        print("⚠️  No audio URLs to update")
        return False
    
    try:
        audio_urls_json = json.dumps(audio_urls)
        query = f"""
        UPDATE lessons 
        SET content_metadata = content_metadata || '{{"audio_urls": {audio_urls_json}}}'::jsonb,
            has_audio = true
        WHERE id = '{lesson_id}'
        """
        
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
            headers=headers,
            json={'query': query}
        )
        
        if response.status_code == 200:
            print(f"✅ Updated lesson with {len(audio_urls)} audio URLs")
            return True
        else:
            print(f"❌ Failed to update lesson: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Update error: {e}")
        return False

def link_animals_nature_audio():
    """Link existing audio files for Animals and Nature lesson"""
    
    print("🔗 LINKING EXISTING AUDIO FILES")
    print("Following structure: tamil/a1/animals_and_nature/")
    print("=" * 60)
    
    # Animals and Nature lesson details
    lesson_id = "b966c742-d36d-4d94-9e35-7c17a5039487"
    language = "tamil"
    level = "a1"
    lesson_slug = "animals_and_nature"
    
    # Find existing audio files
    existing_files = find_existing_audio_files(language, level, lesson_slug)
    
    if existing_files:
        print(f"\n📊 Found {len(existing_files)} existing audio files")
        
        # Update lesson with found URLs
        if update_lesson_audio_urls(lesson_id, existing_files):
            print("\n📋 Linked Audio Files:")
            for key, url in existing_files.items():
                print(f"  {key}: {url}")
            
            print("\n🎉 AUDIO LINKING COMPLETED!")
            print("🎵 Animals and Nature lesson now linked to existing audio files")
        else:
            print("❌ Failed to update lesson")
    else:
        print("\n⚠️  No existing audio files found")
        print("📝 You may need to generate audio files first")

def link_all_tamil_a1_lessons():
    """Link existing audio for all Tamil A1 lessons"""
    
    print("\n🌍 LINKING ALL TAMIL A1 LESSONS")
    print("=" * 60)
    
    # Common Tamil A1 lesson slugs
    lesson_slugs = [
        "animals_and_nature",
        "basic_greetings_and_introductions", 
        "family_members_and_relationships",
        "numbers_and_counting",
        "colors_and_descriptions",
        "food_and_dining",
        "daily_routine",
        "body_parts",
        "clothing_and_accessories",
        "weather_and_seasons"
    ]
    
    for lesson_slug in lesson_slugs:
        print(f"\n🔍 Checking: {lesson_slug}")
        existing_files = find_existing_audio_files("tamil", "a1", lesson_slug)
        
        if existing_files:
            print(f"✅ Found {len(existing_files)} files for {lesson_slug}")
        else:
            print(f"❌ No files found for {lesson_slug}")

def main():
    """Main function"""
    
    print("🔗 AUDIO FILE LINKING SYSTEM")
    print("Links existing audio files to lessons following documented structure")
    print("=" * 70)
    
    # Link Animals and Nature specifically
    link_animals_nature_audio()
    
    # Check other lessons
    link_all_tamil_a1_lessons()
    
    print("\n📋 NEXT STEPS:")
    print("1. Test audio playback in the NIRA app")
    print("2. Generate missing audio files using proper naming")
    print("3. Follow AUDIO_STRUCTURE_AND_NAMING_STANDARDS.md for future audio")

if __name__ == "__main__":
    main()
