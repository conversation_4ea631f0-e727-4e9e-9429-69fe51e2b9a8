#!/usr/bin/env python3
"""
Multi-Level Creation Runner
Executes the systematic approach for creating A2-C2 Tamil lessons

This script provides a menu-driven interface to:
1. Create lesson structures for all levels
2. Generate content for existing lessons
3. Check progress and status
4. Run specific levels only

Usage: python Scripts/run_multilevel_creation.py
"""

import subprocess
import sys
import os
import requests
import json

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

TAMIL_PATHS = {
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

def check_lesson_status():
    """Check current status of lessons for each level"""
    
    print("\n📊 CURRENT LESSON STATUS")
    print("=" * 50)
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    for level, path_id in TAMIL_PATHS.items():
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=headers,
                params={
                    'path_id': f'eq.{path_id}',
                    'select': 'id,title,content_metadata'
                }
            )
            
            if response.status_code == 200:
                lessons = response.json()
                
                # Count lessons with content
                with_content = 0
                for lesson in lessons:
                    metadata = lesson.get('content_metadata', {})
                    if (metadata.get('vocabulary') and 
                        metadata.get('grammar_points') and 
                        metadata.get('conversations') and 
                        metadata.get('exercises')):
                        with_content += 1
                
                print(f"  {level}: {len(lessons)} lessons total, {with_content} with complete content")
            else:
                print(f"  {level}: Error checking status")
                
        except Exception as e:
            print(f"  {level}: Error - {e}")

def run_structure_creation():
    """Run the lesson structure creation script"""
    
    print("\n🏗️  CREATING LESSON STRUCTURES")
    print("=" * 50)
    
    try:
        result = subprocess.run([
            sys.executable, 
            "Scripts/systematic_multilevel_creator.py"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running structure creation: {e}")
        return False

def run_content_generation():
    """Run the content generation script"""
    
    print("\n📝 GENERATING LESSON CONTENT")
    print("=" * 50)
    
    try:
        result = subprocess.run([
            sys.executable, 
            "Scripts/systematic_content_generator.py"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running content generation: {e}")
        return False

def run_quality_validation():
    """Run the comprehensive quality validation script"""

    print("\n🔍 RUNNING QUALITY VALIDATION")
    print("=" * 50)

    try:
        result = subprocess.run([
            sys.executable,
            "Scripts/comprehensive_quality_validator.py"
        ], capture_output=True, text=True)

        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)

        return result.returncode == 0

    except Exception as e:
        print(f"❌ Error running quality validation: {e}")
        return False

def show_menu():
    """Display the main menu"""

    print("\n🚀 NIRA MULTI-LEVEL CREATION SYSTEM")
    print("=" * 50)
    print("1. Check current lesson status")
    print("2. Create lesson structures (A2-C2)")
    print("3. Generate lesson content (A2-C2)")
    print("4. Validate content quality (A2-C2)")
    print("5. Run complete process (structures + content + validation)")
    print("6. Show documentation")
    print("7. Exit")
    print("-" * 50)

def show_documentation():
    """Show key documentation"""
    
    print("\n📚 DOCUMENTATION")
    print("=" * 50)
    print("📁 Key Files:")
    print("  • docs/Final Rules/MULTI_LEVEL_CONTENT_CREATION_GUIDE.md")
    print("  • Scripts/systematic_multilevel_creator.py")
    print("  • Scripts/systematic_content_generator.py")
    print()
    print("📊 Content Requirements (A2-C2):")
    print("  • 20 vocabulary items per lesson")
    print("  • 10 grammar points per lesson")
    print("  • 15 conversations per lesson")
    print("  • 15 practice exercises per lesson")
    print()
    print("📈 Lesson Counts:")
    print("  • A2: 30 lessons")
    print("  • B1: 20 lessons")
    print("  • B2: 20 lessons")
    print("  • C1: 15 lessons")
    print("  • C2: 15 lessons")
    print("  • TOTAL: 100 lessons")

def main():
    """Main menu loop"""
    
    # Check if we're in the right directory
    if not os.path.exists("Scripts/systematic_multilevel_creator.py"):
        print("❌ Please run this script from the NIRA project root directory")
        print("   cd /Users/<USER>/Documents/NIRA")
        print("   python Scripts/run_multilevel_creation.py")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == '1':
                check_lesson_status()
                
            elif choice == '2':
                print("\n⚠️  This will create lesson structures for A2, B1, B2, C1, C2")
                confirm = input("Continue? (y/N): ").strip().lower()
                if confirm == 'y':
                    run_structure_creation()
                
            elif choice == '3':
                print("\n⚠️  This will generate content for existing lesson structures")
                confirm = input("Continue? (y/N): ").strip().lower()
                if confirm == 'y':
                    run_content_generation()
                
            elif choice == '4':
                print("\n⚠️  This will validate content quality for existing lessons")
                confirm = input("Continue? (y/N): ").strip().lower()
                if confirm == 'y':
                    run_quality_validation()

            elif choice == '5':
                print("\n⚠️  This will run the complete process (structures + content + validation)")
                confirm = input("Continue? (y/N): ").strip().lower()
                if confirm == 'y':
                    print("\n🏗️  Step 1: Creating structures...")
                    if run_structure_creation():
                        print("\n📝 Step 2: Generating content...")
                        if run_content_generation():
                            print("\n🔍 Step 3: Validating quality...")
                            run_quality_validation()
                        else:
                            print("❌ Content generation failed, skipping validation")
                    else:
                        print("❌ Structure creation failed, skipping content generation")

            elif choice == '6':
                show_documentation()

            elif choice == '7':
                print("\n👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice. Please enter 1-7.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
