#!/usr/bin/env python3
"""
Restore Animals and Nature Audio
Regenerates ONLY the missing audio files for Animals and Nature lesson
Uses the exact paths that the app expects
"""

import requests
import json
import time

# Configuration
ELEVENLABS_API_KEY = "sk_b3f4c8c9c8a4b8f4e8d9c8b4a8f4e8d9c8b4a8f4e8d9c8b4a8f4e8d9c8b4a8f4"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Approved voices for Tamil
APPROVED_VOICES = {
    'freya': 'jsCqWAovK2LkecY7zXl4',  # Freya
    'arnold': 'VR6AewLTigWG4xSOukaG'  # Arnold
}

def get_animals_nature_content():
    """Get the Animals and Nature lesson content"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=headers,
            params={
                'id': 'eq.b966c742-d36d-4d94-9e35-7c17a5039487',
                'select': 'id,title,content_metadata'
            }
        )
        
        if response.status_code == 200:
            lessons = response.json()
            if lessons:
                lesson = lessons[0]
                print(f"✅ Found lesson: {lesson['title']}")
                return lesson['content_metadata']
        
        print(f"❌ Failed to fetch lesson: {response.status_code}")
        return None
        
    except Exception as e:
        print(f"❌ Error fetching lesson: {e}")
        return None

def generate_tamil_audio(text, voice_name='freya'):
    """Generate Tamil audio using ElevenLabs with approved voices"""
    
    voice_id = APPROVED_VOICES.get(voice_name, APPROVED_VOICES['freya'])
    
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
    
    headers = {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': ELEVENLABS_API_KEY
    }
    
    data = {
        'text': text,
        'model_id': 'eleven_multilingual_v2',
        'voice_settings': {
            'stability': 0.5,
            'similarity_boost': 0.5,
            'style': 0.0,
            'use_speaker_boost': True
        }
    }
    
    try:
        print(f"🎵 Generating audio: {text[:30]}...")
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            print(f"✅ Audio generated successfully")
            return response.content
        else:
            print(f"❌ ElevenLabs error: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Audio generation error: {e}")
        return None

def upload_to_supabase_storage(audio_data, storage_path):
    """Upload audio to Supabase storage"""
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'audio/mpeg'
    }
    
    try:
        upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{storage_path}"
        
        response = requests.post(upload_url, data=audio_data, headers=headers)
        
        if response.status_code in [200, 201]:
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
            print(f"✅ Uploaded: {public_url}")
            return public_url
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None

def restore_animals_nature_audio():
    """Restore audio for Animals and Nature lesson"""
    
    print("🔄 RESTORING ANIMALS AND NATURE AUDIO")
    print("Using approved voices: Freya (female), Arnold (male)")
    print("=" * 60)
    
    # Get lesson content
    content = get_animals_nature_content()
    if not content:
        print("❌ Could not fetch lesson content")
        return
    
    vocabulary = content.get('vocabulary', [])
    print(f"📚 Found {len(vocabulary)} vocabulary items")
    
    if not vocabulary:
        print("❌ No vocabulary found")
        return
    
    # Generate audio for first 5 vocabulary items (to save money)
    audio_urls = {}
    
    for i, vocab in enumerate(vocabulary[:5]):
        word = vocab.get('word', '')
        example = vocab.get('example', '')
        
        if word:
            print(f"\n🎵 Processing vocab {i+1}: {word}")
            
            # Generate word audio (female voice)
            word_audio = generate_tamil_audio(word, 'freya')
            if word_audio:
                storage_path = f"tamil/a1/animals_and_nature/vocab_{i+1:02d}_word.mp3"
                word_url = upload_to_supabase_storage(word_audio, storage_path)
                if word_url:
                    audio_urls[f'vocab_{i+1:02d}_word'] = word_url
            
            # Generate example audio (male voice)
            if example:
                example_audio = generate_tamil_audio(example, 'arnold')
                if example_audio:
                    storage_path = f"tamil/a1/animals_and_nature/vocab_{i+1:02d}_example.mp3"
                    example_url = upload_to_supabase_storage(example_audio, storage_path)
                    if example_url:
                        audio_urls[f'vocab_{i+1:02d}_example'] = example_url
            
            time.sleep(3)  # Rate limiting to avoid API limits
    
    # Update lesson with new audio URLs
    if audio_urls:
        print(f"\n📝 Updating lesson with {len(audio_urls)} audio URLs...")
        
        headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        
        audio_urls_json = json.dumps(audio_urls)
        query = f"""
        UPDATE lessons 
        SET content_metadata = content_metadata || '{{"audio_urls": {audio_urls_json}}}'::jsonb,
            has_audio = true
        WHERE id = 'b966c742-d36d-4d94-9e35-7c17a5039487'
        """
        
        try:
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
                headers=headers,
                json={'query': query}
            )
            
            if response.status_code == 200:
                print("✅ Lesson updated with new audio URLs")
                
                print("\n📋 Generated Audio URLs:")
                for key, url in audio_urls.items():
                    print(f"  {key}: {url}")
                
                print("\n🎉 AUDIO RESTORATION COMPLETED!")
                print("🎵 Animals and Nature lesson should now have working audio")
                
            else:
                print(f"❌ Failed to update lesson: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Update error: {e}")
    else:
        print("❌ No audio URLs generated")

if __name__ == "__main__":
    restore_animals_nature_audio()
