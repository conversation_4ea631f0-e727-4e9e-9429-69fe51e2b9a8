#!/usr/bin/env python3
"""
🚀 COMPREHENSIVE TAMIL LESSON FIXER
Using Gemini Flash 2.0 Lite to fix all Tamil lessons A2-C2

Phases:
1. Fix Tamil titles/descriptions to English
2. Generate missing content for empty lessons
3. Replace placeholder content with authentic Tamil content
4. Add missing pronunciations

Updates <PERSON><PERSON><PERSON> directly using provided credentials.
"""

import os
import json
import requests
import time
import re
from typing import Dict, List, Any, Tuple
from datetime import datetime

# Set up Gemini API
os.environ['GEMINI_API_KEY'] = 'AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q'

try:
    import google.generativeai as genai
    genai.configure(api_key=os.environ['GEMINI_API_KEY'])
    model = genai.GenerativeModel('gemini-2.0-flash-exp')
    print("✅ Gemini Flash 2.0 Lite initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize Gemini: {e}")
    exit(1)

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil Learning Path IDs
TAMIL_PATHS = {
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

# Expected content counts
EXPECTED_CONTENT = {
    'A2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'B1': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'B2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'C1': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15},
    'C2': {'vocabulary': 20, 'conversations': 15, 'grammar': 10, 'exercises': 15}
}

class TamilLessonFixer:
    """Comprehensive Tamil lesson fixer using Gemini Flash 2.0 Lite"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        self.fixed_count = 0
        self.total_lessons = 0
        
    def call_gemini(self, prompt: str, retries: int = 3) -> str:
        """Call Gemini Flash 2.0 Lite with retry logic"""
        for attempt in range(retries):
            try:
                response = model.generate_content(prompt)
                return response.text
            except Exception as e:
                print(f"⚠️  Gemini API call failed (attempt {attempt + 1}): {e}")
                if attempt < retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    raise e
    
    def is_tamil_text(self, text: str) -> bool:
        """Check if text contains Tamil characters"""
        if not text:
            return False
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))
    
    def romanize_tamil(self, tamil_text: str) -> str:
        """Generate Tamil romanization using Gemini"""
        prompt = f"""
        Convert this Tamil text to romanization (English letters representing Tamil pronunciation):
        
        Tamil: {tamil_text}
        
        Provide ONLY the romanization, no explanation.
        Use consistent romanization system (e.g., வணக்கம் -> Vanakkam)
        """
        
        try:
            return self.call_gemini(prompt).strip()
        except:
            # Fallback simple romanization
            return tamil_text.replace('வணக்கம்', 'Vanakkam').replace('நன்றி', 'Nandri')
    
    def get_lessons_for_level(self, level: str) -> List[Dict[str, Any]]:
        """Get all lessons for a specific level"""
        path_id = TAMIL_PATHS[level]
        
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={
                'path_id': f'eq.{path_id}',
                'select': '*',
                'order': 'sequence_order'
            }
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to fetch {level} lessons: {response.status_code}")
            return []
    
    def update_lesson(self, lesson_id: str, updates: Dict[str, Any]) -> bool:
        """Update a lesson in Supabase"""
        response = requests.patch(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            params={'id': f'eq.{lesson_id}'},
            json=updates
        )
        
        return response.status_code in [200, 204]
    
    def phase1_fix_titles_descriptions(self, level: str, lessons: List[Dict[str, Any]]) -> int:
        """Phase 1: Fix Tamil titles and descriptions to English"""
        print(f"\n🔥 PHASE 1: Fixing {level} Titles/Descriptions")
        print("-" * 50)
        
        fixed_count = 0
        
        for lesson in lessons:
            lesson_id = lesson['id']
            title = lesson.get('title', '')
            description = lesson.get('description', '')
            
            needs_fix = False
            updates = {}
            
            # Check if title needs translation
            if self.is_tamil_text(title):
                print(f"  🔄 Translating title: {title[:50]}...")
                
                prompt = f"""
                Translate this Tamil lesson title to proper English:
                
                Tamil: {title}
                
                Requirements:
                - Provide ONLY the English translation
                - Make it a proper lesson title (e.g., "Daily Routine: From Morning to Night")
                - Remove any Tamil text in parentheses
                - Keep it concise and educational
                """
                
                try:
                    english_title = self.call_gemini(prompt).strip()
                    # Clean up any quotes or extra text
                    english_title = english_title.strip('"\'').split('\n')[0]
                    updates['title'] = english_title
                    needs_fix = True
                    print(f"    ✅ New title: {english_title}")
                except Exception as e:
                    print(f"    ❌ Failed to translate title: {e}")
            
            # Check if description needs translation
            if description and self.is_tamil_text(description):
                print(f"  🔄 Translating description...")
                
                prompt = f"""
                Translate this Tamil lesson description to proper English:
                
                Tamil: {description}
                
                Requirements:
                - Provide ONLY the English translation
                - Make it a proper lesson description
                - Remove any Tamil text
                - Keep it educational and clear
                - Maximum 200 characters
                """
                
                try:
                    english_description = self.call_gemini(prompt).strip()
                    english_description = english_description.strip('"\'').split('\n')[0]
                    updates['description'] = english_description
                    needs_fix = True
                    print(f"    ✅ New description: {english_description[:50]}...")
                except Exception as e:
                    print(f"    ❌ Failed to translate description: {e}")
            
            # Update lesson if needed
            if needs_fix:
                if self.update_lesson(lesson_id, updates):
                    fixed_count += 1
                    print(f"    ✅ Updated lesson {lesson.get('sequence_order', '?')}")
                else:
                    print(f"    ❌ Failed to update lesson {lesson.get('sequence_order', '?')}")
            
            # Rate limiting
            time.sleep(0.5)
        
        print(f"📊 Phase 1 Complete: Fixed {fixed_count}/{len(lessons)} lessons")
        return fixed_count
    
    def phase2_generate_missing_content(self, level: str, lessons: List[Dict[str, Any]]) -> int:
        """Phase 2: Generate missing content for empty lessons"""
        print(f"\n🔥 PHASE 2: Generating Missing Content for {level}")
        print("-" * 50)
        
        expected = EXPECTED_CONTENT[level]
        fixed_count = 0
        
        for lesson in lessons:
            lesson_id = lesson['id']
            title = lesson.get('title', '')
            content_metadata = lesson.get('content_metadata', {})
            
            # Check if lesson needs content generation
            vocabulary = content_metadata.get('vocabulary', [])
            conversations = content_metadata.get('conversations', [])
            grammar = content_metadata.get('grammar_points', [])
            exercises = content_metadata.get('exercises', [])
            
            needs_content = (
                len(vocabulary) == 0 or 
                len(conversations) == 0 or 
                len(grammar) == 0 or 
                len(exercises) == 0
            )
            
            if not needs_content:
                continue
            
            print(f"  🔄 Generating content for: {title}")
            print(f"    Current: V:{len(vocabulary)} C:{len(conversations)} G:{len(grammar)} E:{len(exercises)}")
            
            # Generate complete lesson content
            prompt = f"""
            Generate complete Tamil lesson content for: "{title}"
            
            Level: {level} (CEFR level)
            Requirements:
            - {expected['vocabulary']} vocabulary items with Tamil words, English translations, pronunciations, and examples
            - {expected['conversations']} conversations with Tamil exchanges, English translations, and pronunciations
            - {expected['grammar']} grammar points with rules, explanations, and examples
            - {expected['exercises']} exercises (multiple choice, fill-in-blank, etc.)
            
            Return as valid JSON with this exact structure:
            {{
                "vocabulary": [
                    {{
                        "word": "Tamil word",
                        "translation": "English translation",
                        "pronunciation": "romanized pronunciation",
                        "example": "Tamil example (romanization) - English translation",
                        "part_of_speech": "noun/verb/adjective",
                        "difficulty": "basic/intermediate/advanced"
                    }}
                ],
                "conversations": [
                    {{
                        "title": "Conversation title",
                        "scenario": "Context description", 
                        "difficulty": "beginner/intermediate/advanced",
                        "exchanges": [
                            {{
                                "speaker": "Person A/Person B",
                                "text": "Tamil text",
                                "translation": "English translation",
                                "pronunciation": "romanized pronunciation"
                            }}
                        ]
                    }}
                ],
                "grammar_points": [
                    {{
                        "rule": "Grammar rule name",
                        "explanation": "Clear explanation in English",
                        "examples": ["Tamil example 1", "Tamil example 2"],
                        "tips": "Learning tip"
                    }}
                ],
                "exercises": [
                    {{
                        "type": "multiple_choice",
                        "question": "Question in English",
                        "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
                        "options_pronunciations": ["pronunciation 1", "pronunciation 2", "pronunciation 3", "pronunciation 4"],
                        "correctAnswer": 0,
                        "explanation": "Explanation of correct answer",
                        "points": 10
                    }}
                ]
            }}
            
            Ensure all Tamil text is authentic and culturally appropriate.
            Include proper pronunciations for all Tamil content.
            """
            
            try:
                response = self.call_gemini(prompt)
                # Clean up response to get valid JSON
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_content = response[json_start:json_end]
                    new_content = json.loads(json_content)
                    
                    # Update lesson
                    updates = {'content_metadata': new_content}
                    if self.update_lesson(lesson_id, updates):
                        fixed_count += 1
                        print(f"    ✅ Generated complete content")
                    else:
                        print(f"    ❌ Failed to update lesson")
                else:
                    print(f"    ❌ Invalid JSON response")
                    
            except Exception as e:
                print(f"    ❌ Failed to generate content: {e}")
            
            # Rate limiting for content generation
            time.sleep(2)
        
        print(f"📊 Phase 2 Complete: Generated content for {fixed_count} lessons")
        return fixed_count
    
    def phase3_enhance_placeholder_content(self, level: str, lessons: List[Dict[str, Any]]) -> int:
        """Phase 3: Replace placeholder content with authentic Tamil content"""
        print(f"\n🔥 PHASE 3: Enhancing Placeholder Content for {level}")
        print("-" * 50)
        
        placeholder_indicators = [
            'placeholder', 'example', 'sample', 'generic', 'template',
            'basic word', 'simple conversation', 'standard exercise'
        ]
        
        fixed_count = 0
        
        for lesson in lessons:
            lesson_id = lesson['id']
            title = lesson.get('title', '')
            content_metadata = lesson.get('content_metadata', {})
            
            if not content_metadata:
                continue
            
            # Check for placeholder content
            has_placeholder = False
            content_str = json.dumps(content_metadata).lower()
            
            for indicator in placeholder_indicators:
                if indicator in content_str:
                    has_placeholder = True
                    break
            
            if not has_placeholder:
                continue
            
            print(f"  🔄 Enhancing placeholder content for: {title}")
            
            # Enhance the content
            prompt = f"""
            Improve this Tamil lesson content by replacing any placeholder or generic content with authentic Tamil cultural content:
            
            Lesson: "{title}"
            Level: {level}
            Current Content: {json.dumps(content_metadata, indent=2)}
            
            Requirements:
            - Replace any placeholder, generic, or template content
            - Make all content authentic and culturally appropriate for Tamil speakers
            - Ensure vocabulary is topic-specific and realistic
            - Make conversations natural and culturally relevant
            - Include proper Tamil pronunciations for all content
            - Keep the same JSON structure
            
            Return the enhanced content as valid JSON.
            """
            
            try:
                response = self.call_gemini(prompt)
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_content = response[json_start:json_end]
                    enhanced_content = json.loads(json_content)
                    
                    # Update lesson
                    updates = {'content_metadata': enhanced_content}
                    if self.update_lesson(lesson_id, updates):
                        fixed_count += 1
                        print(f"    ✅ Enhanced content quality")
                    else:
                        print(f"    ❌ Failed to update lesson")
                else:
                    print(f"    ❌ Invalid JSON response")
                    
            except Exception as e:
                print(f"    ❌ Failed to enhance content: {e}")
            
            # Rate limiting
            time.sleep(1.5)
        
        print(f"📊 Phase 3 Complete: Enhanced {fixed_count} lessons")
        return fixed_count
    
    def phase4_add_pronunciations(self, level: str, lessons: List[Dict[str, Any]]) -> int:
        """Phase 4: Add missing pronunciations"""
        print(f"\n🔥 PHASE 4: Adding Missing Pronunciations for {level}")
        print("-" * 50)
        
        fixed_count = 0
        
        for lesson in lessons:
            lesson_id = lesson['id']
            title = lesson.get('title', '')
            content_metadata = lesson.get('content_metadata', {})
            
            if not content_metadata:
                continue
            
            needs_pronunciation_fix = False
            
            # Check vocabulary pronunciations
            vocabulary = content_metadata.get('vocabulary', [])
            for vocab in vocabulary:
                if not vocab.get('pronunciation'):
                    needs_pronunciation_fix = True
                    break
            
            # Check conversation pronunciations
            conversations = content_metadata.get('conversations', [])
            for conv in conversations:
                exchanges = conv.get('exchanges', [])
                for exchange in exchanges:
                    if not exchange.get('pronunciation'):
                        needs_pronunciation_fix = True
                        break
            
            # Check exercise option pronunciations
            exercises = content_metadata.get('exercises', [])
            for exercise in exercises:
                options = exercise.get('options', [])
                pronunciations = exercise.get('options_pronunciations', [])
                if options and len(pronunciations) != len(options):
                    needs_pronunciation_fix = True
                    break
            
            if not needs_pronunciation_fix:
                continue
            
            print(f"  🔄 Adding pronunciations for: {title}")
            
            # Add missing pronunciations
            prompt = f"""
            Add missing romanized Tamil pronunciations to this lesson content:
            
            Current Content: {json.dumps(content_metadata, indent=2)}
            
            Requirements:
            - Add 'pronunciation' field to any vocabulary item missing it
            - Add 'pronunciation' field to any conversation exchange missing it
            - Add 'options_pronunciations' array to any exercise missing it
            - Use consistent romanization (e.g., வணக்கம் -> Vanakkam)
            - Keep all existing content unchanged, only add missing pronunciations
            
            Return the complete content with added pronunciations as valid JSON.
            """
            
            try:
                response = self.call_gemini(prompt)
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_content = response[json_start:json_end]
                    enhanced_content = json.loads(json_content)
                    
                    # Update lesson
                    updates = {'content_metadata': enhanced_content}
                    if self.update_lesson(lesson_id, updates):
                        fixed_count += 1
                        print(f"    ✅ Added missing pronunciations")
                    else:
                        print(f"    ❌ Failed to update lesson")
                else:
                    print(f"    ❌ Invalid JSON response")
                    
            except Exception as e:
                print(f"    ❌ Failed to add pronunciations: {e}")
            
            # Rate limiting
            time.sleep(1)
        
        print(f"📊 Phase 4 Complete: Added pronunciations to {fixed_count} lessons")
        return fixed_count
    
    def process_level(self, level: str) -> Dict[str, int]:
        """Process all phases for a specific level"""
        print(f"\n🚀 PROCESSING {level} LEVEL")
        print("=" * 60)
        
        # Get all lessons for this level
        lessons = self.get_lessons_for_level(level)
        if not lessons:
            print(f"❌ No lessons found for {level}")
            return {'phase1': 0, 'phase2': 0, 'phase3': 0, 'phase4': 0}
        
        print(f"📚 Found {len(lessons)} lessons for {level}")
        self.total_lessons += len(lessons)
        
        # Execute all phases
        results = {}
        results['phase1'] = self.phase1_fix_titles_descriptions(level, lessons)
        
        # Refresh lessons after Phase 1 updates
        lessons = self.get_lessons_for_level(level)
        results['phase2'] = self.phase2_generate_missing_content(level, lessons)
        
        # Refresh lessons after Phase 2 updates
        lessons = self.get_lessons_for_level(level)
        results['phase3'] = self.phase3_enhance_placeholder_content(level, lessons)
        
        # Refresh lessons after Phase 3 updates
        lessons = self.get_lessons_for_level(level)
        results['phase4'] = self.phase4_add_pronunciations(level, lessons)
        
        total_fixed = sum(results.values())
        self.fixed_count += total_fixed
        
        print(f"🎉 {level} COMPLETE: Fixed {total_fixed} lesson aspects")
        return results
    
    def run_comprehensive_fix(self):
        """Run comprehensive fix for all levels A2-C2"""
        print("🚀 COMPREHENSIVE TAMIL LESSON FIXER")
        print("=" * 60)
        print("Using Gemini Flash 2.0 Lite to fix all Tamil lessons A2-C2")
        print("Phases: 1=Titles/Descriptions, 2=Missing Content, 3=Placeholder Content, 4=Pronunciations")
        print()
        
        start_time = datetime.now()
        all_results = {}
        
        # Process each level
        for level in ['A2', 'B1', 'B2', 'C1', 'C2']:
            try:
                all_results[level] = self.process_level(level)
            except Exception as e:
                print(f"❌ Error processing {level}: {e}")
                all_results[level] = {'error': str(e)}
        
        # Generate final report
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("🎉 COMPREHENSIVE FIX COMPLETE!")
        print("=" * 60)
        print(f"Duration: {duration}")
        print(f"Total Lessons Processed: {self.total_lessons}")
        print(f"Total Fixes Applied: {self.fixed_count}")
        print()
        
        print("📊 RESULTS BY LEVEL:")
        for level, results in all_results.items():
            if 'error' not in results:
                total = sum(results.values())
                print(f"{level}: P1={results['phase1']} P2={results['phase2']} P3={results['phase3']} P4={results['phase4']} (Total: {total})")
            else:
                print(f"{level}: ERROR - {results['error']}")
        
        print("\n✅ All Tamil lessons A2-C2 have been systematically fixed!")
        print("✅ Ready for quality validation and audio generation!")

def main():
    """Main execution function"""
    fixer = TamilLessonFixer()
    fixer.run_comprehensive_fix()

if __name__ == "__main__":
    main() 