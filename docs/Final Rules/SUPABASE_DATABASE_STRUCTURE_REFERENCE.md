# 🗄️ SUPABASE DATABASE STRUCTURE REFERENCE

## 📋 **OVERVIEW**

This document outlines the complete Supabase database structure for the NIRA Language Learning App, serving as the definitive reference for local content generation and database operations.

**Database URL**: `https://lyaojebttnqilmdosmjk.supabase.co`  
**Project ID**: `lyaojebttnqilmdosmjk`  
**Region**: `us-east-2`

---

## 🏗️ **CORE TABLES STRUCTURE**

### **1. LANGUAGES TABLE**
```sql
CREATE TABLE languages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    native_name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now()
);
```

**Key Records:**
- Tamil: `id = "6b427613-420f-4586-bce8-2773d722f0b4"`
- Spanish: `id = "spanish-uuid"`
- French: `id = "french-uuid"`

### **2. LEARNING_PATHS TABLE**
```sql
CREATE TABLE learning_paths (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    language_id UUID NOT NULL REFERENCES languages(id),
    agent_id UUID NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    level VARCHAR(10) NOT NULL, -- A1, A2, B1, B2, C1, C2
    estimated_hours INTEGER,
    sequence_order INTEGER,
    prerequisites TEXT[],
    learning_objectives TEXT[],
    cultural_focus TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
```

**Tamil Learning Path IDs:**
- A1: `6b427613-420f-4586-bce8-2773d722f0b4`
- A2: `0b14776f-f0b1-4e65-8fac-40a4ce8f125b`
- B1: `26aa02d3-7849-49ba-9da1-4ed61518d736`
- B2: `6a5619db-bc89-4ab8-98e7-9f74cbcad793`
- C1: `ea76fb1b-04cd-4713-bd31-1b4c5a325ad8`
- C2: `7b2f84d7-47ce-4e19-bb79-43926188fe4e`

### **3. LESSONS TABLE**
```sql
CREATE TABLE lessons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    path_id UUID NOT NULL REFERENCES learning_paths(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    lesson_type VARCHAR(50) NOT NULL,
    difficulty_level INTEGER,
    estimated_duration INTEGER, -- in minutes
    sequence_order INTEGER,
    learning_objectives TEXT[],
    vocabulary_focus TEXT[],
    grammar_concepts TEXT[],
    cultural_notes TEXT,
    prerequisite_lessons TEXT[],
    content_metadata JSONB, -- Main lesson content
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    audio_url TEXT,
    audio_metadata JSONB,
    has_audio BOOLEAN DEFAULT false
);
```

### **4. USER_PROGRESS TABLE**
```sql
CREATE TABLE user_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL, -- Auth0 user ID
    lesson_id UUID NOT NULL REFERENCES lessons(id),
    status VARCHAR(50) NOT NULL, -- 'not_started', 'in_progress', 'completed'
    score INTEGER,
    time_spent_seconds INTEGER DEFAULT 0,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
```

---

## 📊 **CONTENT_METADATA STRUCTURE**

The `content_metadata` JSONB field in the `lessons` table contains all lesson content:

### **Complete Structure:**
```json
{
  "vocabulary": [
    {
      "word": "Tamil word",
      "translation": "English translation",
      "pronunciation": "romanized pronunciation",
      "example": "Tamil example (pronunciation) - English translation",
      "difficulty": "basic|intermediate|advanced",
      "part_of_speech": "noun|verb|adjective|adverb",
      "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/vocab_{01-25}_word.mp3",
      "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/vocab_{01-25}_example.mp3"
    }
  ],
  "conversations": [
    {
      "title": "Conversation title",
      "scenario": "Context description",
      "difficulty": "beginner|intermediate|advanced",
      "exchanges": [
        {
          "text": "Tamil text",
          "speaker": "Speaker A|Speaker B",
          "translation": "English translation",
          "pronunciation": "romanized pronunciation",
          "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/conv_{01-15}_{01-02}.mp3"
        }
      ]
    }
  ],
  "grammar_points": [
    {
      "rule": "Grammar rule title",
      "explanation": "Detailed explanation in English",
      "examples": [
        "Tamil example 1 (pronunciation) - English translation",
        "Tamil example 2 (pronunciation) - English translation",
        "Tamil example 3 (pronunciation) - English translation"
      ],
      "tips": "Additional learning tips",
      "audio_urls": [
        "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/grammar_{01-10}_example_{01-03}.mp3"
      ]
    }
  ],
  "exercises": [
    {
      "type": "multiple_choice|fill_in_blank|matching|translation",
      "points": 10,
      "question": "Question text in English",
      "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
      "correctAnswer": 0,
      "explanation": "Explanation of correct answer",
      "options_audio_urls": [
        "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/exercise_{01-24}_option_{01-04}.mp3"
      ]
    }
  ]
}
```

---

## 🎵 **AUDIO STORAGE STRUCTURE**

### **Supabase Storage Bucket**: `lesson-audio`

### **Path Structure:**
```
lesson-audio/
├── {language}/          # e.g., tamil, spanish, french
│   ├── {level}/         # e.g., a1, a2, b1, b2, c1, c2
│   │   ├── {lesson_slug}/   # e.g., animals_nature, daily_routines
│   │   │   ├── vocab_01_word.mp3
│   │   │   ├── vocab_01_example.mp3
│   │   │   ├── conv_01_01.mp3
│   │   │   ├── conv_01_02.mp3
│   │   │   ├── grammar_01_example_01.mp3
│   │   │   └── exercise_01_option_01.mp3
```

### **Audio URL Pattern:**
```
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/{language}/{level}/{lesson_slug}/{file_name}.mp3
```

---

## 📈 **CONTENT REQUIREMENTS BY LEVEL**

### **A1 Level (30 lessons)**
- **Vocabulary**: 25 items per lesson
- **Conversations**: 15 conversations per lesson
- **Grammar Points**: 10 points per lesson  
- **Exercises**: 24 exercises per lesson
- **Audio Files**: 203 files per lesson

### **A2 Level (30 lessons)**
- **Vocabulary**: 20 items per lesson
- **Conversations**: 15 conversations per lesson
- **Grammar Points**: 10 points per lesson
- **Exercises**: 15 exercises per lesson

### **B1-C2 Levels**
- **B1/B2**: 20 lessons each
- **C1/C2**: 15 lessons each
- Same content structure as A2

---

## 🔑 **KEY RELATIONSHIPS**

```
languages (1) ←→ (many) learning_paths
learning_paths (1) ←→ (many) lessons  
lessons (1) ←→ (many) user_progress
```

---

## 🛠️ **ESSENTIAL QUERIES**

### **Get All Lessons for Tamil A1:**
```sql
SELECT l.* FROM lessons l
JOIN learning_paths lp ON l.path_id = lp.id
WHERE lp.language_id = '6b427613-420f-4586-bce8-2773d722f0b4'
AND lp.level = 'A1'
AND l.is_active = true
ORDER BY l.sequence_order;
```

### **Update Lesson Content:**
```sql
UPDATE lessons 
SET content_metadata = $1,
    updated_at = now()
WHERE id = $2;
```

### **Get User Progress:**
```sql
SELECT * FROM user_progress 
WHERE user_id = $1 
AND lesson_id = $2;
```

---

## 🔧 **ADDITIONAL TABLES**

### **5. SIMULATIONS TABLE**
```sql
CREATE TABLE simulations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    persona_id UUID NOT NULL,
    language_id UUID NOT NULL REFERENCES languages(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    difficulty_level VARCHAR(50),
    estimated_duration INTEGER,
    scenario_type VARCHAR(100),
    learning_objectives TEXT[],
    vocabulary_focus TEXT[],
    conversation_starters JSONB,
    success_criteria JSONB,
    cultural_notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
```

### **6. USER_PROFILES TABLE**
```sql
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL UNIQUE, -- Auth0 user ID
    email VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    preferred_languages TEXT[],
    target_languages TEXT[],
    current_level VARCHAR(10),
    learning_goals TEXT[],
    daily_goal_minutes INTEGER DEFAULT 30,
    timezone VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
```

### **7. KNOWLEDGE_BASE TABLE**
```sql
CREATE TABLE knowledge_base (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    agent_id UUID,
    language VARCHAR(10),
    file_name VARCHAR(255),
    file_type VARCHAR(50),
    file_size BIGINT,
    file_path TEXT,
    title VARCHAR(255),
    description TEXT,
    content TEXT,
    extracted_text TEXT,
    metadata JSONB,
    tags TEXT[],
    is_processed BOOLEAN DEFAULT false,
    processing_status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
```

---

## 🔐 **AUTHENTICATION & SECURITY**

### **Row Level Security (RLS) Policies**

**User Progress Table:**
```sql
-- Users can only access their own progress
CREATE POLICY "Users can view own progress" ON user_progress
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can update own progress" ON user_progress
    FOR UPDATE USING (auth.uid()::text = user_id);
```

**User Profiles Table:**
```sql
-- Users can only access their own profile
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid()::text = user_id);
```

---

## 📊 **INDEXES FOR PERFORMANCE**

```sql
-- Lessons table indexes
CREATE INDEX idx_lessons_path_id ON lessons(path_id);
CREATE INDEX idx_lessons_sequence_order ON lessons(sequence_order);
CREATE INDEX idx_lessons_is_active ON lessons(is_active);

-- Learning paths indexes
CREATE INDEX idx_learning_paths_language_id ON learning_paths(language_id);
CREATE INDEX idx_learning_paths_level ON learning_paths(level);

-- User progress indexes
CREATE INDEX idx_user_progress_user_id ON user_progress(user_id);
CREATE INDEX idx_user_progress_lesson_id ON user_progress(lesson_id);
CREATE INDEX idx_user_progress_status ON user_progress(status);

-- Content metadata GIN index for JSONB queries
CREATE INDEX idx_lessons_content_metadata ON lessons USING GIN (content_metadata);
```

---

## 🚀 **API ENDPOINTS REFERENCE**

### **Base URL**: `https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/`

### **Common Headers:**
```json
{
  "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c",
  "Authorization": "Bearer [ANON_KEY]",
  "Content-Type": "application/json"
}
```

### **Key Endpoints:**
- **Get Lessons**: `GET /lessons?path_id=eq.{path_id}&is_active=eq.true`
- **Create Lesson**: `POST /lessons`
- **Update Lesson**: `PATCH /lessons?id=eq.{lesson_id}`
- **Get User Progress**: `GET /user_progress?user_id=eq.{user_id}`
- **Update Progress**: `POST /user_progress` (upsert)

---

## 📝 **CONTENT VALIDATION RULES**

### **Vocabulary Items:**
- Must have: `word`, `translation`, `pronunciation`
- Optional: `example`, `part_of_speech`, `difficulty`
- Audio URLs: `word_audio_url`, `example_audio_url`

### **Conversations:**
- Must have: `title`, `scenario`, `exchanges`
- Each exchange: `text`, `speaker`, `translation`, `pronunciation`, `audio_url`
- Minimum 2 exchanges per conversation

### **Grammar Points:**
- Must have: `rule`, `explanation`, `examples`
- Minimum 3 examples per grammar point
- Audio URLs for each example

### **Exercises:**
- Must have: `type`, `question`, `options`, `correctAnswer`, `explanation`
- 4 options for multiple choice
- Audio URLs for each option

---

## 🔄 **DATA MIGRATION PATTERNS**

### **Adding New Language:**
1. Insert language record
2. Create learning paths for each level (A1-C2)
3. Generate lessons with proper `path_id` references
4. Upload audio files to storage
5. Update `content_metadata` with audio URLs

### **Content Updates:**
1. Always update `updated_at` timestamp
2. Preserve existing `content_metadata` structure
3. Validate content against quality checklist
4. Test audio URL accessibility

---

## 💡 **PRACTICAL EXAMPLES**

### **Creating a New Lesson (Python):**
```python
import requests
import json
from datetime import datetime

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "your_anon_key_here"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

# Create lesson payload
lesson_data = {
    "id": str(uuid.uuid4()),
    "path_id": "6b427613-420f-4586-bce8-2773d722f0b4",  # Tamil A1
    "title": "Colors and Shapes",
    "description": "Learn basic colors and shapes in Tamil",
    "lesson_type": "vocabulary",
    "difficulty_level": 1,
    "estimated_duration": 30,
    "sequence_order": 3,
    "learning_objectives": ["Learn 25 color/shape words", "Practice pronunciation"],
    "vocabulary_focus": ["colors", "shapes", "basic adjectives"],
    "grammar_concepts": ["adjective placement", "color agreement"],
    "content_metadata": {
        "vocabulary": [
            {
                "word": "சிவப்பு",
                "translation": "Red",
                "pronunciation": "sivappu",
                "example": "சிவப்பு ரோஜா (sivappu roja) - Red rose",
                "part_of_speech": "adjective",
                "difficulty": "basic",
                "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_shapes/vocab_01_word.mp3",
                "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_shapes/vocab_01_example.mp3"
            }
            # ... 24 more vocabulary items
        ],
        "conversations": [
            {
                "title": "Choosing Colors",
                "scenario": "Shopping for clothes",
                "difficulty": "beginner",
                "exchanges": [
                    {
                        "text": "எந்த நிறம் வேண்டும்?",
                        "speaker": "Shopkeeper",
                        "translation": "What color do you want?",
                        "pronunciation": "entha niram vendum?",
                        "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/colors_shapes/conv_01_01.mp3"
                    }
                ]
            }
            # ... 14 more conversations
        ]
        # ... grammar_points and exercises
    },
    "is_active": True,
    "created_at": datetime.now().isoformat(),
    "updated_at": datetime.now().isoformat()
}

# Insert lesson
response = requests.post(
    f"{SUPABASE_URL}/rest/v1/lessons",
    headers=headers,
    json=lesson_data
)
```

### **Querying Lessons with Content (Python):**
```python
# Get all Tamil A1 lessons with content
response = requests.get(
    f"{SUPABASE_URL}/rest/v1/lessons",
    headers=headers,
    params={
        "select": "*,learning_paths(*)",
        "learning_paths.language_id": "eq.6b427613-420f-4586-bce8-2773d722f0b4",
        "learning_paths.level": "eq.A1",
        "is_active": "eq.true",
        "order": "sequence_order"
    }
)

lessons = response.json()
for lesson in lessons:
    print(f"Lesson: {lesson['title']}")
    vocab_count = len(lesson['content_metadata'].get('vocabulary', []))
    print(f"Vocabulary items: {vocab_count}")
```

---

## 🔍 **TROUBLESHOOTING GUIDE**

### **Common Issues:**

**1. Content Metadata Not Updating:**
```sql
-- Check if JSONB is properly formatted
SELECT content_metadata FROM lessons WHERE id = 'your-lesson-id';

-- Update specific vocabulary item
UPDATE lessons
SET content_metadata = jsonb_set(
    content_metadata,
    '{vocabulary,0,word_audio_url}',
    '"https://new-audio-url.mp3"'
)
WHERE id = 'your-lesson-id';
```

**2. Missing Audio URLs:**
```python
# Validate audio URLs in content
def validate_audio_urls(lesson_content):
    issues = []

    # Check vocabulary audio
    for i, vocab in enumerate(lesson_content.get('vocabulary', [])):
        if not vocab.get('word_audio_url'):
            issues.append(f"Vocabulary {i+1}: Missing word_audio_url")
        if not vocab.get('example_audio_url'):
            issues.append(f"Vocabulary {i+1}: Missing example_audio_url")

    return issues
```

**3. Path ID Mismatches:**
```sql
-- Find lessons with invalid path_id
SELECT l.id, l.title, l.path_id
FROM lessons l
LEFT JOIN learning_paths lp ON l.path_id = lp.id
WHERE lp.id IS NULL;

-- Fix path_id for Tamil A1 lessons
UPDATE lessons
SET path_id = '6b427613-420f-4586-bce8-2773d722f0b4'
WHERE path_id = 'old-incorrect-path-id';
```

---

## 📋 **QUALITY CHECKLIST**

Before deploying content, verify:

- [ ] All required fields are populated
- [ ] Content counts match requirements (25 vocab, 15 conv, 10 grammar, 24 exercises for A1)
- [ ] All audio URLs are accessible
- [ ] Romanized pronunciations are included
- [ ] No placeholder content remains
- [ ] Cultural authenticity is maintained
- [ ] Database relationships are correct
- [ ] Content follows established patterns

---

## 🚀 **SCALING TO NEW LANGUAGES**

### **Step-by-Step Process:**

1. **Create Language Record:**
```sql
INSERT INTO languages (id, code, name, native_name, is_active)
VALUES (gen_random_uuid(), 'es', 'Spanish', 'Español', true);
```

2. **Create Learning Paths:**
```sql
INSERT INTO learning_paths (id, language_id, name, level, estimated_hours)
VALUES
(gen_random_uuid(), 'spanish-language-id', 'Spanish A1', 'A1', 60),
(gen_random_uuid(), 'spanish-language-id', 'Spanish A2', 'A2', 80);
-- ... continue for B1, B2, C1, C2
```

3. **Generate Content Using Templates:**
- Use existing Tamil A1 structure as template
- Adapt cultural context for target language
- Generate audio with appropriate voices
- Validate against quality checklist

4. **Test Integration:**
- Verify iOS app can load new content
- Test audio playback
- Validate user progress tracking

---

This comprehensive structure supports the complete NIRA language learning system with proper relationships, content organization, audio integration, security, and scalability for 50+ languages.
