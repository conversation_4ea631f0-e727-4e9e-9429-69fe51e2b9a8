# 🎵 AUDIO STRUCTURE & NAMING STANDARDS FOR NIRA

## 📁 **STORAGE STRUCTURE**

### **Supabase S3 Storage Path Structure**
```
lesson-audio/
├── {language}/
│   ├── {level}/
│   │   ├── {lesson_slug}/
│   │   │   ├── vocab_01_word.mp3
│   │   │   ├── vocab_01_pronunciation.mp3
│   │   │   ├── vocab_01_sentence.mp3
│   │   │   ├── conv_01_speaker_a.mp3
│   │   │   ├── conv_01_speaker_b.mp3
│   │   │   ├── grammar_01_explanation.mp3
│   │   │   ├── grammar_01_example.mp3
│   │   │   ├── exercise_01_question.mp3
│   │   │   ├── exercise_01_option_a.mp3
│   │   │   └── exercise_01_option_b.mp3
```

### **Example Structure** ✅ **CONFIRMED WORKING**
```
lesson-audio/
├── tamil/
│   ├── a1/
│   │   ├── animals_nature/          ← CONFIRMED: Files exist here
│   │   │   ├── vocab_01_word.mp3    ← CONFIRMED: Working audio file
│   │   │   ├── vocab_02_word.mp3    ← CONFIRMED: Working audio file
│   │   │   ├── vocab_03_word.mp3
│   │   │   ├── vocab_04_word.mp3
│   │   │   ├── vocab_05_word.mp3
│   │   │   └── ...
│   │   ├── basic_greetings/
│   │   ├── family_members/
│   │   └── numbers_and_counting/
│   ├── a2/
│   ├── b1/
│   └── ...
├── spanish/
├── french/
└── ...
```

**⚠️ IMPORTANT**: Use underscores only, no "and" in folder names:
- ✅ `animals_nature` (CORRECT - files exist here)
- ❌ `animals_and_nature` (WRONG - no files here)

---

## 🏷️ **FILE NAMING CONVENTIONS**

### **Vocabulary Audio Files** ✅ **FIXED NAMING**
- `vocab_{number:02d}_word.mp3` - The actual word/phrase
- `vocab_{number:02d}_pronunciation.mp3` - Pronunciation guide (if different)
- `vocab_{number:02d}_sentence.mp3` - Example sentence using the word

**✅ CORRECT Examples:**
- `vocab_01_word.mp3` - "நாய்" (dog)
- `vocab_01_sentence.mp3` - "நாய் குரைக்கிறது" (the dog is barking)

**❌ WRONG (Old naming):**
- `vocab_01_example.mp3` - Too generic, doesn't specify content type

### **Conversation Audio Files**
- `conv_{number:02d}_speaker_a.mp3` - First speaker's dialogue
- `conv_{number:02d}_speaker_b.mp3` - Second speaker's dialogue
- `conv_{number:02d}_full.mp3` - Complete conversation (optional)

**Examples:**
- `conv_01_speaker_a.mp3` - "வணக்கம்!"
- `conv_01_speaker_b.mp3` - "வணக்கம்! எப்படி இருக்கீங்க?"

### **Grammar Audio Files**
- `grammar_{number:02d}_explanation.mp3` - Grammar rule explanation
- `grammar_{number:02d}_example.mp3` - Example demonstrating the rule

**Examples:**
- `grammar_01_explanation.mp3` - Explanation of verb conjugation
- `grammar_01_example.mp3` - "நான் படிக்கிறேன்"

### **Exercise Audio Files**
- `exercise_{number:02d}_question.mp3` - The question being asked
- `exercise_{number:02d}_option_a.mp3` - First option
- `exercise_{number:02d}_option_b.mp3` - Second option
- `exercise_{number:02d}_option_c.mp3` - Third option
- `exercise_{number:02d}_option_d.mp3` - Fourth option

**Examples:**
- `exercise_01_question.mp3` - "What is the Tamil word for 'dog'?"
- `exercise_01_option_a.mp3` - "நாய்"
- `exercise_01_option_b.mp3` - "பூனை"

---

## 🌍 **LANGUAGE & LEVEL CODES**

### **Language Codes** (ISO 639-1)
- `tamil` (ta)
- `spanish` (es)
- `french` (fr)
- `german` (de)
- `chinese` (zh)
- `japanese` (ja)
- `korean` (ko)
- `arabic` (ar)
- `hindi` (hi)
- `russian` (ru)
- etc.

### **Level Codes** (CEFR)
- `a1` - Beginner
- `a2` - Elementary
- `b1` - Intermediate
- `b2` - Upper Intermediate
- `c1` - Advanced
- `c2` - Proficient

### **Lesson Slug Format**
- Convert lesson title to lowercase
- Replace spaces with underscores
- Remove special characters
- Keep only alphanumeric and underscores

**Examples:**
- "Animals and Nature" → `animals_and_nature`
- "Basic Greetings & Introductions" → `basic_greetings_introductions`
- "Numbers and Counting (1-100)" → `numbers_and_counting_1_100`

---

## 🔗 **URL STRUCTURE**

### **Public URL Format**
```
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/{language}/{level}/{lesson_slug}/{filename}
```

### **Example URLs**
```
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_and_nature/vocab_01_word.mp3
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_and_nature/vocab_01_sentence.mp3
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/spanish/a2/daily_routine/conv_01_speaker_a.mp3
```

---

## 📝 **DATABASE STORAGE**

### **Audio URLs in content_metadata**
```json
{
  "audio_urls": {
    "vocab_01_word": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_and_nature/vocab_01_word.mp3",
    "vocab_01_sentence": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_and_nature/vocab_01_sentence.mp3",
    "conv_01_speaker_a": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_and_nature/conv_01_speaker_a.mp3",
    "grammar_01_explanation": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_and_nature/grammar_01_explanation.mp3",
    "exercise_01_question": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_and_nature/exercise_01_question.mp3"
  }
}
```

---

## 🎯 **IMPLEMENTATION RULES**

### **✅ DO**
- Follow exact folder structure: `{language}/{level}/{lesson_slug}/`
- Use meaningful file names that describe content type
- Use 2-digit zero-padded numbers (01, 02, 03...)
- Store all URLs in `content_metadata.audio_urls`
- Use approved ElevenLabs voices (Freya, Elli, Arnold, Sam)

### **❌ DON'T**
- Use generic names like "example" for everything
- Mix different naming conventions
- Skip the lesson_slug folder level
- Store audio URLs outside content_metadata
- Generate audio without checking existing files first

---

## 🔧 **MIGRATION NOTES**

### **Current Issues to Fix**
1. **Generic "example" naming** - Replace with specific content type names
2. **Missing lesson_slug folders** - Ensure all audio follows folder structure
3. **Inconsistent numbering** - Use 2-digit zero-padded format

### **Future Audio Generation**
- Always check existing files before generating new ones
- Follow this naming convention for ALL new audio
- Apply to ALL languages and levels
- Update existing scripts to use new naming

---

## 📊 **QUALITY STANDARDS**

### **Audio Requirements**
- **Format**: MP3, 128kbps minimum
- **Voices**: Use approved ElevenLabs voices only
- **Language**: Native pronunciation for target language
- **Clarity**: Clear, professional quality
- **Consistency**: Same voice for same content type within lesson

### **File Management**
- **Backup**: Keep local copies before uploading
- **Validation**: Test URLs before updating database
- **Documentation**: Update this document for any changes
- **Cost Control**: Reuse existing audio, don't regenerate unnecessarily

---

---

## 🔧 **CURRENT STATUS & IMPLEMENTATION**

### **✅ COMPLETED**
1. **Documentation Updated** - Audio structure and naming standards documented
2. **Animals & Nature Fixed** - Linked to existing audio files in `animals_nature/`
3. **Correct Path Identified** - `tamil/a1/animals_nature/` contains working audio files
4. **Database Updated** - Animals and Nature lesson now has working audio URLs

### **🎵 WORKING AUDIO FILES**
```
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_01_word.mp3 ✅
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_02_word.mp3 ✅
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_03_word.mp3 ✅
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_04_word.mp3 ✅
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_05_word.mp3 ✅
```

### **📋 NEXT STEPS**
1. **Test Audio in App** - Verify Animals and Nature audio works in NIRA app
2. **Find Other Existing Audio** - Check for other Tamil A1 lessons with existing audio
3. **Link Existing Files** - Connect existing audio to lessons before generating new ones
4. **Follow Naming Standards** - Use proper file names for all future audio generation
5. **Apply to All Languages** - Use this structure for all 89 languages

### **🚨 CRITICAL RULES**
- ✅ **ALWAYS check existing files first** - Don't regenerate paid audio
- ✅ **Follow exact folder structure** - `{language}/{level}/{lesson_slug}/`
- ✅ **Use meaningful file names** - No generic "example" naming
- ✅ **Test URLs before updating database** - Verify files exist
- ✅ **Document any changes** - Update this file for new patterns

**This structure must be followed for ALL audio across ALL lessons and ALL languages in NIRA.**
